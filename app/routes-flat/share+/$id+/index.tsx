import {
  usePara<PERSON>,
  useLoaderD<PERSON>,
  Links,
  ScrollRestoration,
  Scripts,
  LiveReload,
} from "@remix-run/react";
import { json, LoaderFunction } from "@remix-run/node";
import { useEffect, useState } from "react";

function detectDevice(userAgent: string | null) {
  const ua = userAgent?.toLowerCase() || "";
  const isAndroid = /android/.test(ua);
  const isIOS = /iphone|ipad|ipod/.test(ua);
  const isMobile = isAndroid || isIOS || /mobile/.test(ua);

  let deviceType: "android" | "ios" | "web" = "web";
  if (isAndroid) deviceType = "android";
  else if (isIOS) deviceType = "ios";

  return {
    isMobile,
    isAndroid,
    isIOS,
    deviceType,
  };
}

export const loader: LoaderFunction = async ({ params, request }) => {
  const userAgent = request.headers.get("user-agent");
  const deviceInfo = detectDevice(userAgent);

  return json({
    id: params.id,
    ...deviceInfo,
  });
};

export default function SharePage() {
  const { id } = useParams();
  const data = useLoaderData<{
    id: string;
    isMobile: boolean;
    isAndroid: boolean;
    isIOS: boolean;
    deviceType: string;
  }>();

  const [originalUrl, setOriginalUrl] = useState<string | null>(null); // Fallback URL (App Store/Play Store)
  const [deepLinkUrl, setDeepLinkUrl] = useState<string | null>(null); // Deep link URL for app
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [showUI, setShowUI] = useState(false);
  const [isButtonLoading, setIsButtonLoading] = useState(false); // Track button loading state

  useEffect(() => {
    if (!id || !data.deviceType) return;

    const fetchShareData = async () => {
      try {
        const res = await fetch(
          `https://api3-dev.contractorforeman.net/api/share/${id}?platform=${data.deviceType}`
        );
        if (!res.ok) throw new Error(`API error: ${res.status}`);
        const jsonData = await res.json();

        const rawUrl = jsonData?.data?.originalUrl;
        const parsedRawUrl = new URL(rawUrl || "", window.location.origin);
        const finalUrl =
          window.location.origin + parsedRawUrl.pathname + parsedRawUrl.hash;

        if (data.deviceType === "web") {
          window.location.href = finalUrl;
        } else if (data.deviceType === "ios") {
          const rawUrlForIos = jsonData?.data?.phpOriginalUrl;
          const scheme = jsonData.data.scheme;
          const fallbackUrl = jsonData.data.fallbackUrl;
          const deepLink = `${scheme}://${rawUrlForIos}`;

          setDeepLinkUrl(deepLink);
          setOriginalUrl(fallbackUrl);
          setShowUI(true); // Show UI immediately for mobile
        } else if (data.deviceType === "android") {
          const rawUrlForAndroid = jsonData?.data?.phpOriginalUrl;
          const scheme = jsonData.data.scheme;
          const packageName = jsonData.data.package;
          const fallbackUrl = jsonData.data.fallbackUrl;
          const cleanUrl = rawUrlForAndroid.replace(/^https?:\/\//, "");

          const intentUrl = `intent://${cleanUrl}#Intent;scheme=${scheme};package=${packageName};S.browser_fallback_url=${fallbackUrl};end`;

          setDeepLinkUrl(intentUrl);
          setOriginalUrl(fallbackUrl);
          setShowUI(true); // Show UI immediately for mobile
        }
      } catch (err: any) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchShareData();
  }, [id, data.deviceType]);

  // Handle back navigation to ensure UI is visible
  useEffect(() => {
    const handlePageShow = (event: PageTransitionEvent) => {
      if (event.persisted) {
        // Page restored from cache
        setShowUI(true);
        setLoading(false);
        setIsButtonLoading(false);
      }
    };
    window.addEventListener("pageshow", handlePageShow);
    return () => window.removeEventListener("pageshow", handlePageShow);
  }, []);

  const handleButtonClick = () => {
    if (!deepLinkUrl || !originalUrl || isButtonLoading) return;

    setIsButtonLoading(true);

    const ua = navigator.userAgent.toLowerCase();
    const isSafari = /^((?!chrome|android).)*safari/.test(ua);
    const isIOS = data.deviceType === "ios";

    let didHide = false;

    const onVisibilityChange = () => {
      if (document.hidden) {
        didHide = true; // App opened successfully
      }
    };

    document.addEventListener("visibilitychange", onVisibilityChange);

    const cleanup = () => {
      document.removeEventListener("visibilitychange", onVisibilityChange);
      setIsButtonLoading(false);
    };

    const now = Date.now();

    if (isIOS && isSafari) {
      window.location.href = deepLinkUrl;
    } else {
      const iframe = document.createElement("iframe");
      iframe.style.display = "none";
      iframe.src = deepLinkUrl;
      document.body.appendChild(iframe);
      setTimeout(() => document.body.removeChild(iframe), 1000);
    }

    // Only fallback if app was not opened
    setTimeout(() => {
      cleanup();
      if (!didHide && !document.hidden) {
        window.location.href = originalUrl!;
      }
    }, 2000);
  };

  return (
    <html lang="en">
      <head>
        <title>Contractor Foreman Share</title>
        <meta name="apple-itunes-app" content="app-id=1239787613" />
        <Links />
      </head>
      <body>
        <div
          style={{
            fontFamily: "Arial, sans-serif",
            padding: "20px",
            maxWidth: "500px",
            margin: "0 auto",
            textAlign: "center",
          }}
        >
          {!loading && showUI && !error && (
            <>
              <h1>Contractor Foreman</h1>
              <div style={{ margin: "20px 0", lineHeight: 1.5 }}>
                <p>
                  Tap the button below to open this content in the Contractor
                  Foreman app.
                </p>
                {originalUrl && (
                  <p>
                    If you don&apos;t have the app installed, you can{" "}
                    <a href={originalUrl}>
                      download it from{" "}
                      {data.deviceType === "ios" ? "App Store" : "Play Store"}
                    </a>
                    .
                  </p>
                )}
              </div>

              <button
                style={{
                  backgroundColor: "#4285f4",
                  color: "white",
                  border: "none",
                  padding: "12px 24px",
                  fontSize: "18px",
                  borderRadius: "4px",
                  cursor: isButtonLoading ? "not-allowed" : "pointer",
                  margin: "20px 0",
                  width: "100%",
                  maxWidth: "300px",
                }}
                onClick={handleButtonClick}
                disabled={isButtonLoading}
              >
                {isButtonLoading ? "Opening..." : "OPEN IN APP"}
              </button>
            </>
          )}

          {!loading && error && <p style={{ color: "red" }}>Error: {error}</p>}
        </div>

        <ScrollRestoration />
        <Scripts />
        <LiveReload />
      </body>
    </html>
  );
}
