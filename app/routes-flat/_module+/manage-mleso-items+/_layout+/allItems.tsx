import { use<PERSON><PERSON>back, useEffect, useMemo, useRef, useState } from "react";
// Atoms
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
import { Image } from "~/shared/components/atoms/image";
import { Popover } from "~/shared/components/atoms/popover";
import { Tag } from "~/shared/components/atoms/tag";
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Typography } from "~/shared/components/atoms/typography";

// molecules
import { DropdownMenu } from "~/shared/components/molecules/dropdownMenu";
import { ErrorBoundary } from "~/shared/components/molecules/errorBoundary/ErrorBoundary";
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { ConfirmModal } from "~/shared/components/molecules/confirmModal";
import { DynamicTable } from "~/shared/components/molecules/dynamicTable";
import DynamicTableSelectFilter from "~/shared/components/molecules/dynamicTable/dynamicTableSelectFilter/DynamicTableSelectFilter";
import DynamicTableInputFilter from "~/shared/components/molecules/dynamicTable/dynamicTableInputFilter/DynamicTableInputFilter";

// Organisms
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";

// Shared
import { MIDashboardRegularIconAdd } from "~/shared/utils/helper/fontAwesome/libraryAdd/module/mleso-items/regular";
import useTableGridData from "~/shared/hooks/useTableGridData";
import {
  getWebWorkerApiParams,
  webWorkerApi,
} from "~/shared/services/apiService/web-worker";
import { agGridSelectHandler } from "~/shared/utils/helper/agGrid";

// Zustand
import { getGlobalUser } from "~/zustand/global/user/slice";
import { useGlobalModule } from "~/zustand/global/modules/slice";
import { getCurrentMenuModule } from "~/zustand/global/config/slice";

// Lodash
import isEqual from "lodash/isEqual";
import debounce from "lodash/debounce";

// Formik
import * as Yup from "yup";

// Other
import DetailsTopBar from "~/modules/settings/costItemsDatabase/components/DetailsTopBar";
import {
  ADD_ITEMS_OPTION,
  ADD_ITEMS_OPTION_KEYS,
  ITEMS_OPTIONS,
  MLESOITEMS_SELECT_OPTION_BY_KEYS,
  MLESOITEMS_SELECT_OPTION_KEYS,
  MLESOITEMS_SELECTOPTION_TAB_KEYS,
} from "~/modules/settings/costItemsDatabase/utils/constants";
import {
  ColDef,
  ColumnMovedEvent,
  GridReadyEvent,
  IRowNode,
  KeyCreatorParams,
  SortChangedEvent,
  ValueFormatterParams,
  ValueGetterParams,
  ValueSetterParams,
  ICellRendererParams,
} from "ag-grid-community";
import { cidbItemRoutes } from "~/route-services/cidb-item.routes";
import { Number, sanitizeString } from "~/helpers/helper";
import { useTranslation } from "~/hook";
import {
  floatWithNegativeRegex,
  getAddAllItemsModule,
  getAddAllItemsType,
  getAddAllItemsTypeIcon,
  getAddAllItemsTypesForOneBuild,
  getItemTypeKey,
  getItemTypeKeyIcon,
  getItemTypeKeyIconModule,
  getItemTypeKeyModule,
  getItemTypeKeyModuleId,
  getKeyAddAllItems,
  getNormalizedItemType,
  wholeNumberRegex,
} from "~/shared/utils/helper/common";
import { AddItems } from "~/modules/settings/costItemsDatabase/components/sidebar/addItems";
import { costCodeRoutes } from "~/route-services/cost-code.routes";
import { useExistingCustomers } from "~/components/sidebars/multi-select/customer/zustand";
import CostCodeCellEditor from "~/modules/settings/costItemsDatabase/components/costCodeCellEditor/CostCodeCellEditor";
import CostCodeFilter from "~/modules/settings/costItemsDatabase/components/costCodeFilter/CostCodeFilter";
import {
  changeFloatingFilterDisabled,
  fetchCIDBItemsData,
  getAGGridValidCellClick,
  getCIDBValidationFieldsUpdate,
  onSingleClick,
  setFiledMinWidthWithWidth,
} from "~/modules/settings/costItemsDatabase/utils/helper";
import { FormikHelpers, FormikProps } from "formik";
import { apiRoutes } from "~/route-services/routes";
import UnitCellEditor from "~/modules/settings/costItemsDatabase/components/unitCellEditor/UnitCellEditor";
import { addUnit, getUnitList } from "~/redux/action/unitActions";
import { onEnterSelectSearchValue } from "~/shared/components/molecules/selectField/units";
import FavoriteFilter from "~/modules/settings/costItemsDatabase/components/favoriteFilter/FavoriteFilter";
import { getCommonSidebarCollapse, getGSettings } from "~/zustand";
import { getItemVariants } from "~/modules/financials/pages/estimates/redux/action/ESItemAction";
import { deleteFiles } from "~/modules/document/fileAndPhoto/redux/action/filePhotoRightAction";
import { getGlobalAppSettings } from "~/zustand/global/settings/slice";
import { useOutletContext } from "@remix-run/react";
import FavoriteEditor from "~/modules/settings/costItemsDatabase/components/favoriteEditor/FavoriteEditor";

// Fort Awesome Library Add icons
MIDashboardRegularIconAdd();

function AllItems() {
  const { _t } = useTranslation();
  const sidebarCollapse: boolean | undefined = getCommonSidebarCollapse();
  const module: IInitialGlobalData["config"]["current_module"] =
    getCurrentMenuModule();
  const { currentOption } = useOutletContext<{
    options: ICIDBTabOption[];
    currentOption: ICIDBTabOption;
  }>();
  const {
    module_id = 0,
    module_key = "",
    module_access = "no_access",
  } = module || {};

  const { formatter } = useCurrencyFormatter();
  const user: IInitialGlobalData["user"] = getGlobalUser();
  const { allow_delete_module_items = "0" } = user || {};
  const { getGlobalModuleByKey, checkGlobalModulePermission } =
    useGlobalModule();
  const { datasource, gridRowParams } =
    useTableGridData<IAllItemAGGridFilterParams>();
  const { getExistingUsersWithApi } = useExistingCustomers();

  const accessibleIDs = useMemo(() => {
    return [
      CFConfig.material_module_id,
      CFConfig.equipment_module_id,
      CFConfig.labor_module_id,
      CFConfig.subcontractor_module_id,
      CFConfig.other_items_module_id,
    ].reduce((acc: number[], moduleId) => {
      const moduleAccess = checkGlobalModulePermission(moduleId);
      if (moduleAccess === "full_access" || moduleAccess === "read_only") {
        acc.push(moduleId);
      }
      return acc;
    }, []);
  }, []);

  const fullAccessIDs = useMemo(() => {
    return accessibleIDs.reduce((acc: string[], moduleId) => {
      const moduleAccess = checkGlobalModulePermission(moduleId);
      if (moduleAccess === "full_access") {
        acc.push(moduleId.toString());
      }
      return acc;
    }, []);
  }, [accessibleIDs]);

  const gridRef =
    useRef<ExtendedAgGridReact<ICIDBGetAllItemsListApiResponseData> | null>(
      null
    );
  const isNavigationRef = useRef<boolean>(false);
  const isNavigatingRef = useRef<boolean>(false);
  const isInitialized = useRef<boolean>(false);
  const [AGFilterParams, setAGFilterParams] = useState<{
    search: ISendCidbActionProps["search"];
    status: ISendCidbActionProps["status"];
  }>({ search: "", status: 0 });

  const [selectedData, setSelectedData] = useState<
    (
      | ICIDBGetAllItemsListApiResponseData
      | ICIDBAllItemDetailApiResponse["data"]
    ) & { variants_items?: VariaonsofItem[] }
  >();
  const [rowNodeID, setRowNodeID] = useState<string>();
  const [copyExistingItemID, setCopyExistingItemID] = useState<number>();
  const [supplier, setSupplier] = useState<Partial<CustomerSelectedData>>();
  const [supplierLoading, setSupplierLoading] = useState<boolean>(false);
  const [operator, setOperator] = useState<Partial<CustomerSelectedData>>();
  const [confirmDialogOpen, setConfirmDialogOpen] = useState<boolean>(false);
  const [inUsesSelectedItems, setInUsesSelectedItems] =
    useState<TSelectedItemsIsInUsed>("no_items_in_use");
  const [confirmArchiveDialogOpen, setConfirmArchiveDialogOpen] =
    useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [fetchingCopyDataLoading, setFetchingCopyDataLoading] =
    useState<boolean>(false);
  const [formLoading, setFormLoading] = useState<boolean>(false);
  const [addEditSidebarOpen, setAddEditSidebarOpen] = useState<boolean>(false);
  const [isCoping, setIsCoping] = useState<boolean>(false);
  const [selectedItemType, setSelectedItemType] = useState<string>("");
  const [isAnyFileRemoved, setIsAnyFileRemoved] = useState<boolean>(false);
  const [isItemsSelected, setIsItemsSelected] = useState<boolean>(false);
  const [costCodes, setCostCodes] = useState<
    ICostCodeGroupListResponse["data"]["cost_codes"]
  >([]);
  const [isCostCodesGetting, setIsCostCodesGetting] = useState<boolean>(true);
  const [units, setUnits] = useState<
    IUnitListResponseDataAndStatusCode["units"]
  >([]);
  const [isUnitsGetting, setIsUnitsGetting] = useState<boolean>(true);
  const [newUnitName, setNewUnitName] = useState<string>("");
  const [isAddingCustomData, setIsAddingCustomData] = useState<boolean>(false);
  const [isSelectedItemsActive, setIsSelectedItemsActive] =
    useState<boolean>(false);
  const addItemsRef = useRef<{
    formik: FormikProps<IAddItemsProps["formInitialValues"]>;
  }>(null);
  const [currentItemIndex, setCurrentItemIndex] = useState<number>(0);
  const rowCount = gridRef?.current?.api?.getDisplayedRowCount() || 0;
  const canNavigatePrevious = () => currentItemIndex > 0;
  const canNavigateNext = () => currentItemIndex < rowCount - 1;
  const [hasChanges, setHasChanges] = useState<boolean>(false);
  const { cost_code_sort_order }: GSettings = getGSettings();
  const appSettings: IInitialGlobalData["settings"]["app_settings"] =
    getGlobalAppSettings();
  const [confirmCopyDialogOpen, setConfirmCopyDialogOpen] =
    useState<boolean>(false);
  const {
    default_equipment_markup_percent = "",
    default_labor_markup_percent = "",
    default_material_markup_percent = "",
    default_other_item_markup_percent = "",
    default_sub_contractor_markup_percent = "",
    default_undefined_markup_percent = "",
  } = appSettings || {};

  let markupDefaultValue = "";
  if (selectedItemType === ADD_ITEMS_OPTION_KEYS.material) {
    markupDefaultValue = default_material_markup_percent;
  } else if (selectedItemType === ADD_ITEMS_OPTION_KEYS.equipment) {
    markupDefaultValue = default_equipment_markup_percent;
  } else if (selectedItemType === ADD_ITEMS_OPTION_KEYS.labor) {
    markupDefaultValue = default_labor_markup_percent;
  } else if (selectedItemType === ADD_ITEMS_OPTION_KEYS.subcontractor) {
    markupDefaultValue = default_sub_contractor_markup_percent;
  } else if (selectedItemType === ADD_ITEMS_OPTION_KEYS.other) {
    markupDefaultValue = default_other_item_markup_percent;
  } else if (default_undefined_markup_percent) {
    markupDefaultValue = default_undefined_markup_percent;
  }

  const allItemsModule = useMemo(() => {
    let itemType = selectedData?.item_type?.toString()?.toLowerCase() || "";
    if (itemType === "sub contract") {
      itemType = "subcontractor";
    }
    let module: IInitialGlobalData["module"]["modules"][0] | undefined;
    if (selectedData && itemType && !selectedItemType) {
      module = getGlobalModuleByKey(getItemTypeKeyModule({ type: itemType }));
    } else {
      module = getGlobalModuleByKey(
        getAddAllItemsModule({ type: selectedItemType })
      );
    }

    if (module) {
      switch (module.module_key) {
        case CFConfig.other_items_key:
          module = {
            ...module,
            original_module_name: "Other Item",
          };
          break;
        case CFConfig.material_module:
          module = {
            ...module,
            original_module_name: "Material Item",
          };
          break;
        case CFConfig.labour_module:
          module = {
            ...module,
            original_module_name: "Labor Item",
          };
          break;
        case CFConfig.equipment_module:
          module = {
            ...module,
            original_module_name: "Equipment Item",
          };
          break;
        case CFConfig.sub_contractor_module:
          module = {
            ...module,
            original_module_name: "Subcontractor Item",
          };
          break;
        default:
          module = {
            ...module,
            original_module_name: "Item Group",
          };
          break;
      }
    }
    return module;
  }, [selectedData, selectedItemType]);

  const refreshAgGrid = () => {
    const gridParams = gridRowParams?.gridParams;
    if (gridParams) {
      gridParams?.api.deselectAll();
      setIsSelectedItemsActive(false);
      gridParams.api.setServerSideDatasource({ getRows: () => {} });
      gridParams.api.setServerSideDatasource(datasource);
    }
  };

  useEffect(() => {
    (async function () {
      await fetchCIDBItemsData({
        moduleKey: CFConfig.mixed_company_type_key,
        filterParams: AGFilterParams,
        gridRowParams,
        itemTypes: accessibleIDs.length === 5 ? [] : accessibleIDs,
      });
      if (gridRowParams?.gridParams) {
        changeFloatingFilterDisabled(gridRowParams?.gridParams, false);
      }
    })();
    return () => {};
  }, [gridRowParams, accessibleIDs]);

  useEffect(() => {
    if (gridRef && gridRef.current && isInitialized.current) {
      refreshAgGrid();
    } else {
      isInitialized.current = true;
    }
  }, [AGFilterParams?.search]);

  const onGridReady = (gridParams: GridReadyEvent) => {
    changeFloatingFilterDisabled(gridParams, true);
    gridParams?.api?.setServerSideDatasource(datasource);
    // Delay redrawRows until data is rendered
    gridParams.api.addEventListener("firstDataRendered", () => {
      gridParams.api.redrawRows();
    });
  };

  const onSortChanged = async (params: SortChangedEvent) => {
    params.api.setServerSideDatasource(datasource);
  };

  const updateValue = async (form: any) => {
    try {
      return (await webWorkerApi({
        url: cidbItemRoutes.update_item,
        method: "post",
        data: {
          cost_code_key: form.cost_code_key,
          ...form,
        },
      })) as IApiCallResponse;
    } catch (error) {
      return {
        success: false,
        message: (error as Error).message,
      } as IApiCallResponse;
    }
  };

  useEffect(() => {
    (async function () {
      try {
        const data = await getWebWorkerApiParams({
          otherParams: {
            order_by:
              cost_code_sort_order === "by_number" ? "csi_number" : "csi_name",
            order_by_dir: "ASC",
          },
        });
        const response = (await webWorkerApi({
          url: costCodeRoutes.group_list,
          method: "post",
          data: data,
        })) as ICostCodeGroupListResponse;

        const costCodes = response.data.cost_codes || [];
        setCostCodes(costCodes);
        const api = gridRef.current?.api;
        if (!api) return;

        const existingColDefs = api.getColumnDefs();
        if (!existingColDefs) return;

        const updatedColDefs = existingColDefs.map((col) =>
          "field" in col && col.field === "cost_code_name"
            ? {
                ...col,
                filterParams: {
                  values: costCodes,
                },
              }
            : col
        );

        api.setColumnDefs(updatedColDefs);

        // Ensure the grid header re-renders
        api.refreshHeader();
      } catch (error) {}
      setIsCostCodesGetting(false);
    })();
    if (window.ENV.ENABLE_UNIT_DROPDOWN) {
      (async function () {
        try {
          const response = await getUnitList();
          if (!response.success) {
            setIsUnitsGetting(false);
            return;
          }
          const units = response.data?.units || [];
          setUnits(units);
          const api = gridRef.current?.api;
          if (!api) return;

          const existingColDefs = api.getColumnDefs();
          if (!existingColDefs) return;

          const updatedColDefs = existingColDefs.map((col) =>
            "field" in col && col.field === "unit"
              ? {
                  ...col,
                  filterParams: {
                    values:
                      units.map((unit) => ({
                        label: unit.name?.toString(),
                        value: unit.name?.toString(),
                      })) ?? [],
                  },
                }
              : col
          );

          api.setColumnDefs(updatedColDefs);

          // Ensure the grid header re-renders
          api.refreshHeader();
        } catch (error) {}
        setIsUnitsGetting(false);
      })();
    }
  }, []);

  const fetchAndSetData = async (
    data: Partial<ICIDBGetAllItemsListApiResponseData>,
    action?: "new"
  ) => {
    const params = await getWebWorkerApiParams({
      otherParams: {
        cost_code_key: getItemTypeKey({
          type: getNormalizedItemType(data?.item_type),
        }),
        item_id: data.id,
      },
    });
    const response = (await webWorkerApi({
      url: cidbItemRoutes.item_detail,
      method: "post",
      data: params,
    })) as ICIDBAllItemDetailApiResponse;

    async function fetchSupplier(supplierID?: number | string | null) {
      if (supplierID) {
        setSupplierLoading(true);
        await getExistingUsersWithApi({
          usersIds: supplierID.toString(),
          apiDataReturn: (users: Partial<CustomerSelectedData>[]) => {
            if (users.length) setSupplier(users[0]);
          },
        });
        setSupplierLoading(false);
      }
    }

    async function fetchVariants(
      data: Partial<ICIDBGetAllItemsListApiResponseData>
    ) {
      if (data?.item_variations?.trim()?.length) {
        let item_type = "";
        switch (data.type_id?.toString()) {
          case CFConfig.material_teb_id?.toString():
            item_type = CFConfig.material_teb_id?.toString() || "";
            break;
          case CFConfig.equipment_teb_id?.toString():
            item_type = CFConfig.equipment_teb_id?.toString() || "";
            break;
          case CFConfig.labor_teb_id?.toString():
            item_type = CFConfig.labor_teb_id?.toString() || "";
            break;
          case CFConfig.subcontractor_teb_id?.toString():
            item_type = CFConfig.subcontractor_teb_id?.toString() || "";
            break;
          case CFConfig.other_items_teb_id?.toString():
            item_type = CFConfig.other_items_teb_id?.toString() || "";
            break;
        }
        const variantResponse = (await getItemVariants({
          item_id: data.id?.toString() || "",
          item_type,
        })) as IEstimateItemVariantApiRes;
        if (variantResponse.success) {
          setSelectedData((prev) =>
            prev ? { ...prev, variants_items: variantResponse.data } : prev
          );
        }
      }
    }

    async function fetchOperator(operatorID?: number | string | null) {
      if (operatorID) {
        await getExistingUsersWithApi({
          usersIds: operatorID.toString(),
          apiDataReturn: (users: Partial<CustomerSelectedData>[]) => {
            if (users.length) setOperator(users[0]);
          },
        });
      }
    }

    if (response.success) {
      if (action === "new") {
        setSelectedData((prev) =>
          prev
            ? {
                ...prev,
                ...response.data,
                id: data.id || prev.id,
                name: prev.name,
                internal_notes: prev.internal_notes,
                supplier_link: prev.supplier_link,
                ...(data.item_type_id?.toString() ===
                CFConfig.equipment_module_id?.toString()
                  ? {
                      plate_number: "",
                      registration_expire_on: "",
                      insurance_carrier: "",
                      inspection_expire_on: "",
                      policy_number: "",
                      purchase_date: "",
                      policy_expire_on: "",
                      e_hours_used: "",
                      last_meter_reading_date: "",
                      oil_changed_date: "",
                      oil_changed_time: "",
                      last_tire_rotation_date: "",
                      last_tire_rotation_time: "",
                      warranty_details: "",
                    }
                  : {}),
              }
            : prev
        );
      } else {
        setSelectedData((prev) =>
          prev ? { ...prev, ...response.data, id: data.id || prev.id } : prev
        );
      }

      if (!data?.supplier_id && response.data?.supplier_id) {
        fetchSupplier(response.data?.supplier_id);
      }
      if (
        !data?.item_variations?.trim()?.length &&
        response.data?.item_variations?.trim()?.length
      ) {
        fetchVariants({ ...response.data, id: data.id || response.data.id });
      }
      if (!data?.operator_id && response.data?.operator_id) {
        fetchOperator(response.data?.operator_id);
      }
    }

    if (data?.item_variations?.trim()?.length) {
      fetchVariants(data);
    }
    if (data?.supplier_id) {
      fetchSupplier(data?.supplier_id);
    }
  };

  const windowWidth = 1600;
  setFiledMinWidthWithWidth(gridRef, "name", windowWidth, 200);

  const checkReadOnly = (
    itemTypeID: ICIDBGetAllItemsListApiResponseData["item_type_id"]
  ) => module_access === "read_only" || !fullAccessIDs.includes(itemTypeID);

  const columnDefs: ColDef<ICIDBGetAllItemsListApiResponseData>[] = useMemo(
    () => [
      {
        headerName: "",
        field: "checkbox" as keyof ICIDBGetAllItemsListApiResponseData,
        headerCheckboxSelection: module_access === "full_access",
        checkboxSelection: ({ data }) =>
          !checkReadOnly(data?.item_type_id || ""),
        width: module_access === "read_only" ? 20 : 40,
        minWidth: module_access === "read_only" ? 20 : 40,
        maxWidth: module_access === "read_only" ? 20 : 40,
        suppressMovable: true,
        lockPosition: "left",
        suppressColumnsToolPanel: true,
        pinned: null,
        lockPinned: true,
        headerClass: "ag-header-center all-row-checkbox-cell",
        cellClass: ({ data }) =>
          "ag-center-aligned-cell cursor-auto cell-checkbox item_" + data?.id,
      },
      {
        headerName: _t("Type"),
        field: "item_type",
        sortable: false,
        width: 50,
        minWidth: 50,
        maxWidth: 50,
        suppressMovable: false,
        pinned: null,
        lockPinned: true,
        suppressMenu: true,
        headerClass: "ag-header-center",
        cellClass: "ag-center-aligned-cell",
        cellRenderer: ({
          value,
        }: ICellRendererParams<
          ICIDBGetAllItemsListApiResponseData,
          ICIDBGetAllItemsListApiResponseData["item_type"]
        >) => {
          const formattedValue =
            value?.toString() === "Sub Contract"
              ? "Subcontractor"
              : value?.toString() || "";
          return (
            <Tooltip title={formattedValue}>
              <FontAwesomeIcon
                className="w-4 h-4 text-primary-900 mx-auto"
                icon={getItemTypeKeyIcon({
                  type:
                    value?.toString().toLowerCase() === "sub contract"
                      ? CFConfig.subcontractor_key
                      : value?.toString().toLowerCase() || "",
                })}
              />
            </Tooltip>
          );
        },
      },
      {
        headerName: "",
        field: "one_build_id",
        sortable: false,
        width: 40,
        minWidth: 40,
        maxWidth: 40,
        suppressMovable: false,
        pinned: null,
        lockPinned: true,
        suppressMenu: true,
        headerClass: "ag-header-center",
        cellClass: "ag-center-aligned-cell cell-click-event-none cursor-auto",
        cellRenderer: ({
          value,
          node,
          data,
        }: ICellRendererParams<
          ICIDBGetAllItemsListApiResponseData,
          ICIDBGetAllItemsListApiResponseData["one_build_id"]
        >) => {
          const isReadOnly = checkReadOnly(data?.item_type_id || "");
          return value ? (
            <Tooltip
              title={`Source: 1build${!isReadOnly ? " - Click to Update" : ""}`}
            >
              <div
                className={`w-4 h-4 ${
                  isReadOnly ? "cursor-not-allowed" : "cursor-pointer"
                }`}
                onClick={() => {
                  if (!isReadOnly) {
                    setRowNodeID(node.id);
                    setSelectedData(data);
                    setConfirmDialogOpen(true);
                  }
                }}
              >
                <img
                  className="!h-full !w-full"
                  src={`${window.ENV.CDN_URL}assets/images/1build.svg`}
                />
              </div>
            </Tooltip>
          ) : (
            <></>
          );
        },
      },
      {
        headerName: _t("SKU"),
        field: "sku",
        editable: ({ data }) => !checkReadOnly(data?.item_type_id || ""),
        sortable: true,
        minWidth: 105,
        maxWidth: 105,
        suppressMenu: true,
        resizable: false,
        suppressMovable: false,
        pinned: null,
        lockPinned: true,
        headerClass: "ag-header-left ",
        cellClass: "ag-cell-left",
        filter: "agTextColumnFilter",
        floatingFilter: true,
        floatingFilterComponentParams: { suppressFilterButton: true }, // Hide button
        filterParams: {
          placeholder: "Search SKU",
        },
        floatingFilterComponent: DynamicTableInputFilter,
        valueSetter: ({ data, newValue, node }) => {
          (async function () {
            if (data.sku !== newValue) {
              const oldData = { ...data };
              data.sku = newValue;
              node?.setData(data);
              const response = await updateValue({
                item_id: data.id,
                sku: newValue,
                cost_code_key: getItemTypeKey({
                  type: getNormalizedItemType(data?.item_type),
                }),
              });
              if (!response.success) {
                node?.setData(oldData);
                notification.error({ description: response.message });
              }
            }
          })();
          return false;
        },
        cellRenderer: ({
          value,
        }: ICellRendererParams<
          ICIDBGetAllItemsListApiResponseData,
          ICIDBGetAllItemsListApiResponseData["sku"]
        >) => {
          const sku = value ? HTMLEntities.decode(sanitizeString(value)) : "";
          return (
            <Tooltip title={sku}>
              <Typography className="table-tooltip-text text-center">
                {sku || "-"}
              </Typography>
            </Tooltip>
          );
        },
      },
      {
        headerName: "",
        field: "file_path",
        sortable: false,
        width: 40,
        minWidth: 40,
        maxWidth: 40,
        suppressMenu: true,
        suppressMovable: false,
        pinned: null,
        lockPinned: true,
        headerClass: "ag-header-center",
        cellClass: "ag-center-aligned-cell cursor-auto",
        cellRenderer: ({
          value,
          data,
        }: ICellRendererParams<
          ICIDBGetAllItemsListApiResponseData,
          ICIDBGetAllItemsListApiResponseData["file_path"]
        >) => {
          const filePath = value?.split(",")?.shift() || value;
          const thumbFilePath = data?.thumb_file_path
            ? data.thumb_file_path.split(",").shift()
            : "";

          return (
            <div className="h-[18px] w-[18px] text-center">
              {filePath && (
                <Popover
                  placement="left"
                  content={
                    <div className="">
                      <Typography className="block text-sm py-2 px-3.5 rounded-t-lg bg-[#F8F8F8]">
                        {_t("Product Image")}
                      </Typography>
                      <Image
                        rootClassName="w-[180px] h-[180px] p-2.5 flex m-auto"
                        className="!w-full !h-full object-cover m-auto rounded"
                        preview={false}
                        src={
                          thumbFilePath?.startsWith("http")
                            ? thumbFilePath
                            : filePath
                        }
                      />
                    </div>
                  }
                >
                  <FontAwesomeIcon
                    className="w-3.5 h-3.5 cursor-pointer text-primary-900"
                    icon="fa-regular fa-image"
                  />
                </Popover>
              )}
            </div>
          );
        },
      },
      {
        headerName: _t("Name"),
        field: "name",
        editable: ({ data }) => !checkReadOnly(data?.item_type_id || ""),
        minWidth: window.innerWidth < windowWidth ? 200 : undefined,
        sortable: true,
        flex: 2,
        suppressMenu: true,
        resizable: true,
        suppressMovable: false,
        pinned: null,
        lockPinned: true,
        headerClass: "ag-header-left default-sort",
        cellClass: "ag-cell-left",
        filter: "agTextColumnFilter",
        floatingFilter: true,
        floatingFilterComponentParams: { suppressFilterButton: true }, // Hide button
        filterParams: {
          placeholder: "Search Name",
        },
        floatingFilterComponent: DynamicTableInputFilter,
        valueGetter: ({ data }) =>
          HTMLEntities.decode(sanitizeString(data?.name)),
        valueSetter: ({ data, newValue, node }) => {
          (async function () {
            const updatedValue = newValue.trim();
            if (!updatedValue) {
              notification.error({ description: "Name field is required." });
              return;
            }

            if (data.name !== updatedValue) {
              const oldData = { ...data };
              data.name = updatedValue;
              node?.setData(data);
              const response = await updateValue({
                item_id: data.id,
                ...getCIDBValidationFieldsUpdate(
                  data,
                  HTMLEntities.encode(updatedValue),
                  "name"
                ),
                cost_code_key: getItemTypeKey({
                  type: getNormalizedItemType(data?.item_type),
                }),
              });
              if (!response.success) {
                node?.setData(oldData);
                notification.error({ description: response.message });
              }
            }
          })();
          return false;
        },
        cellRenderer: ({
          value,
          data,
        }: ICellRendererParams<
          ICIDBGetAllItemsListApiResponseData,
          ICIDBGetAllItemsListApiResponseData["name"]
        >) => {
          const { supplier_id, supplier_name, item_type_id } = data || {};
          const name = HTMLEntities.decode(sanitizeString(value || ""));
          return (
            <div
              className="flex items-center gap-1.5"
              style={{ width: "inherit" }}
            >
              <Tooltip title={name}>
                <Typography
                  className={`table-tooltip-text text-center !w-fit ${
                    Boolean(supplier_id) ? "!max-w-[calc(100%-30px)]" : ""
                  }`}
                >
                  {name}
                </Typography>
              </Tooltip>
              {Boolean(supplier_id) &&
                ([
                  CFConfig.material_module_id?.toString(),
                  CFConfig.equipment_module_id?.toString(),
                  CFConfig.other_items_module_id?.toString(),
                ].includes(item_type_id?.toString() || "") ? (
                  <Tooltip
                    title={
                      "Supplier: " +
                      HTMLEntities.decode(sanitizeString(supplier_name || ""))
                    }
                  >
                    <FontAwesomeIcon
                      className="text-base w-3.5 h-3.5 text-primary-900 inline pl-1"
                      icon="fa-regular fa-tags"
                    />
                  </Tooltip>
                ) : (
                  <Tooltip
                    title={
                      "Subcontractor: " +
                      HTMLEntities.decode(sanitizeString(supplier_name || ""))
                    }
                  >
                    <FontAwesomeIcon
                      className="text-base w-3.5 h-3.5 text-primary-900 inline pl-1"
                      icon="fa-regular fa-helmet-safety"
                    />
                  </Tooltip>
                ))}
            </div>
          );
        },
      },
      {
        headerName: _t("Unit Cost"),
        field: "unit_cost",
        editable: ({ data }) => !checkReadOnly(data?.item_type_id || ""),
        minWidth: 130,
        maxWidth: 130,
        headerClass: "ag-header-right",
        cellClass: "ag-right-aligned-cell",
        sortable: true,
        suppressMovable: false,
        pinned: null,
        lockPinned: true,
        suppressMenu: true,
        valueGetter: (params) =>
          params.data?.unit_cost?.toString()
            ? Number(params.data?.unit_cost || "") / 100
            : null,
        cellEditor: "agNumberCellEditor",
        suppressKeyboardEvent: (params) => {
          if (params.editing) {
            const event = params.event;
            const key = event.key;
            if (key === "+" || key === "-") {
              event.preventDefault();
              return true;
            }
          }
          return false;
        },
        valueSetter: ({ data, newValue, node }) => {
          (async function () {
            let value = 0;
            if (newValue) {
              if (newValue < 0) {
                notification.error({
                  description: _t(
                    "Negative values are not allowed for Unit Cost."
                  ),
                });
                return;
              }
              const fullStr = BigInt(Math.floor(Number(newValue))).toString();

              if (fullStr.length > 10) {
                notification.error({
                  description: _t(
                    "Unit cost should be less than or equal to 10 digits."
                  ),
                });
                return;
              }

              if (!floatWithNegativeRegex.test(newValue.toString())) {
                notification.error({
                  description: _t(
                    "Decimal part should be less than or equal to 2 digits."
                  ),
                });
                return;
              }
              value = newValue * 100;
            }

            if (Number(data.unit_cost || "") !== value) {
              const oldData = { ...data };
              data.unit_cost = value.toString();
              node?.setData(data);
              const response = await updateValue({
                item_id: data.id,
                ...getCIDBValidationFieldsUpdate(data, value, "unit_cost"),
                cost_code_key: getItemTypeKey({
                  type: getNormalizedItemType(data?.item_type),
                }),
              });
              if (!response.success) {
                node?.setData(oldData);
                notification.error({ description: response.message });
              }
            }
          })();
          return false;
        },
        cellRenderer: ({
          value,
        }: ICellRendererParams<
          ICIDBGetAllItemsListApiResponseData,
          ICIDBGetAllItemsListApiResponseData["unit_cost"]
        >) => {
          const unitCode = value?.toString()
            ? formatter(Number(value || "0").toFixed(2)).value_with_symbol
            : "";
          return (
            <Tooltip title={unitCode}>
              <Typography className="table-tooltip-text text-end">
                {unitCode || "-"}
              </Typography>
            </Tooltip>
          );
        },
      },
      {
        headerName: _t("Unit"),
        field: "unit",
        editable: ({ data }) => !checkReadOnly(data?.item_type_id || ""),
        minWidth: 108,
        maxWidth: 108,
        headerClass: "ag-header-left",
        cellClass: "ag-cell-left",
        sortable: true,
        suppressMovable: false,
        pinned: null,
        lockPinned: true,
        suppressMenu: true,
        valueGetter: ({ data }) =>
          data?.unit ? HTMLEntities.decode(sanitizeString(data.unit)) : "",
        cellRenderer: ({
          value,
        }: ICellRendererParams<
          ICIDBGetAllItemsListApiResponseData,
          ICIDBGetAllItemsListApiResponseData["unit"]
        >) => (
          <Tooltip title={value}>
            <Typography className="table-tooltip-text">
              {value || "-"}
            </Typography>
          </Tooltip>
        ),
        ...(window.ENV.ENABLE_UNIT_DROPDOWN
          ? {
              filter: "agSetColumnFilter",
              floatingFilter: !isUnitsGetting,
              floatingFilterComponentParams: {
                suppressFilterButton: true,
                placeholder: "Search Unit",
              },
              floatingFilterComponent: DynamicTableSelectFilter,
              filterParams: {
                suppressMiniFilter: true,
                suppressSelectAll: true,
                defaultToNothingSelected: true,
                values:
                  units.map((unit) => ({
                    value: unit.name?.toString(),
                    label: unit.name?.toString(),
                  })) ?? [],
                allowClear: true,
                valueFormatter: (params: ValueFormatterParams<IOption>) =>
                  params.value.label,
                keyCreator: (params: KeyCreatorParams<IOption>) =>
                  params.value.value,
              },
              suppressKeyboardEvent: (params) => {
                if (params.event.key === "Enter") {
                  params.event.preventDefault();
                  return true;
                }
                return false;
              },
              cellEditorParams: {
                values: units,
                onKeyDown: (
                  e: React.KeyboardEvent<HTMLInputElement>,
                  data: ICIDBGetAllItemsListApiResponseData
                ) => {
                  const value = e.currentTarget.value?.trim();
                  if (e.key === "Enter") {
                    const newType = onEnterSelectSearchValue(
                      e,
                      units?.map((unit) => ({
                        label: unit?.name,
                        value: "",
                      })) || []
                    );
                    if (newType) {
                      setNewUnitName(newType);
                      setSelectedData(data);
                    } else if (value) {
                      notification.error({
                        description:
                          "Records already exist, no new records were added.",
                      });
                    }
                  } else if (value?.length > 14 && e.key.length === 1) {
                    e.preventDefault();
                  }
                },
              },
              cellEditor: UnitCellEditor<ICIDBGetAllItemsListApiResponseData>,
              valueSetter: ({ data, newValue, node, ...event }) => {
                const { stopEditing } =
                  event as ValueSetterParams<ICIDBGetAllItemsListApiResponseData> & {
                    stopEditing: () => void;
                  };
                (async function () {
                  const updatedValue = newValue.name?.trim();
                  if (data.unit !== updatedValue) {
                    const oldData = { ...data };
                    data.unit = updatedValue;
                    node?.setData(data);
                    const response = await updateValue({
                      item_id: data.id,
                      ...getCIDBValidationFieldsUpdate(
                        data,
                        HTMLEntities.encode(updatedValue),
                        "unit"
                      ),
                      cost_code_key: getItemTypeKey({
                        type: getNormalizedItemType(data?.item_type),
                      }),
                    });
                    if (!response.success) {
                      node?.setData(oldData);
                      notification.error({ description: response.message });
                    }
                  }
                })();
                stopEditing?.();
                return false;
              },
            }
          : {
              filter: "agTextColumnFilter",
              floatingFilter: true,
              floatingFilterComponentParams: { suppressFilterButton: true },
              filterParams: {
                placeholder: "Search Unit",
              },
              floatingFilterComponent: DynamicTableInputFilter,
              valueSetter: ({ data, newValue, node }) => {
                (async function () {
                  const updatedValue = newValue?.trim();
                  if (updatedValue.length > 15) {
                    notification.error({
                      description: "Unit must be 15 characters or less.",
                    });
                    return;
                  }
                  if (data.unit !== updatedValue) {
                    const oldData = { ...data };
                    data.unit = updatedValue;
                    node?.setData(data);
                    const response = await updateValue({
                      item_id: data.id,
                      ...getCIDBValidationFieldsUpdate(
                        data,
                        HTMLEntities.encode(updatedValue),
                        "unit"
                      ),
                      cost_code_key: getItemTypeKey({
                        type: getNormalizedItemType(data?.item_type),
                      }),
                    });
                    if (!response.success) {
                      node?.setData(oldData);
                      notification.error({ description: response.message });
                    }
                  }
                })();
                return false;
              },
            }),
      },
      {
        headerName: _t("MU %"),
        field: "markup",
        editable: ({ data }) => !checkReadOnly(data?.item_type_id || ""),
        minWidth: 118,
        maxWidth: 118,
        headerClass: "ag-header-right",
        cellClass: "ag-right-aligned-cell",
        suppressMovable: false,
        pinned: null,
        lockPinned: true,
        suppressMenu: true,
        sortable: true,
        filter: "agNumberColumnFilter",
        suppressKeyboardEvent: (params) => {
          if (params.editing) {
            const event = params.event;
            const key = event.key;
            if (key === "+" || key === "-") {
              event.preventDefault();
              return true;
            }
          }
          return false;
        },
        floatingFilter: true,
        floatingFilterComponentParams: { suppressFilterButton: true }, // Hide button
        filterParams: {
          placeholder: "Search MU %",
        },
        floatingFilterComponent: DynamicTableInputFilter,
        // TODO: This will be changed after discussion or will be removed.
        // valueGetter: ({ data }) => {
        //   return data?.markup !== null && data?.markup !== undefined
        //     ? data?.markup
        //     : 0;
        // },
        cellEditor: "agNumberCellEditor",
        valueSetter: ({ data, newValue, node }) => {
          (async function () {
            if (data.markup?.toString() !== newValue?.toString()) {
              if (Number(newValue) < 0) {
                notification.error({
                  description: "Negative values are not allowed for Markup",
                });
                return;
              } else if (Number(newValue) > 999) {
                notification.error({
                  description:
                    "Markup should be less than or equal to 3 digits",
                });
                return;
              } else if (
                newValue?.toString() &&
                !wholeNumberRegex.test(newValue?.toString())
              ) {
                notification.error({
                  description: _t("Decimal is not allowed in Markup"),
                });
                return false;
              }
              const oldData = { ...data };
              const value = newValue?.toString()
                ? Number(Number(newValue).toFixed())
                : null;
              data.markup = value;
              node?.setData(data);
              const response = await updateValue({
                item_id: data.id,
                ...getCIDBValidationFieldsUpdate(oldData, value, "markup"),
                markup: value,
                cost_code_key: getItemTypeKey({
                  type: getNormalizedItemType(data?.item_type),
                }),
              });
              if (!response.success) {
                node?.setData(oldData);
                notification.error({ description: response.message });
              }
            }
          })();
          return false;
        },
        cellRenderer: ({
          value,
        }: ICellRendererParams<
          ICIDBGetAllItemsListApiResponseData,
          ICIDBGetAllItemsListApiResponseData["markup"]
        >) => {
          return (
            <Tooltip title={value || undefined}>
              <Typography className="table-tooltip-text text-end">
                {value?.toString() || "-"}
              </Typography>
            </Tooltip>
          );
        },
      },
      {
        headerName: _t("Total"),
        field: "total",
        headerClass: "ag-header-right",
        cellClass: "ag-right-aligned-cell",
        suppressMovable: false,
        pinned: null,
        lockPinned: true,
        suppressMenu: true,
        minWidth: 130,
        maxWidth: 130,
        sortable: true,
        cellRenderer: ({
          data,
        }: ICellRendererParams<ICIDBGetAllItemsListApiResponseData>) => {
          const { unit_cost, hidden_markup, markup } = data || {};
          let total = Number(unit_cost || "0");
          if (total) {
            if (hidden_markup !== null && Number(hidden_markup) !== 0) {
              total += total * (Number(hidden_markup) / 100);
            }
            if (markup !== null && Number(markup) !== 0) {
              total += total * (Number(markup) / 100);
            }
          }
          const displayTotal = total
            ? formatter((total / 100).toFixed(2)).value_with_symbol
            : "";
          return (
            <Tooltip title={displayTotal}>
              <Typography className="table-tooltip-text text-end">
                {displayTotal || "-"}
              </Typography>
            </Tooltip>
          );
        },
      },
      {
        headerName: _t("Cost Code"),
        field: "cost_code_name",
        minWidth: 280,
        flex: 2,
        resizable: true,
        suppressMovable: false,
        pinned: null,
        lockPinned: true,
        suppressMenu: true,
        headerClass: "ag-header-left",
        filter: "agSetColumnFilter",
        floatingFilter: true,
        filterParams: {
          suppressMiniFilter: true,
          suppressSelectAll: true,
          defaultToNothingSelected: true,
          values: costCodes,
          valueFormatter: (
            params: ValueFormatterParams<
              ICostCodeGroupListResponse["data"]["cost_codes"][0]["sub_code"][0]
            >
          ) =>
            `${params.value.csi_name}${
              params.value.csi_code ? ` (${params.value.csi_code})` : ""
            }${
              params.value.is_deleted?.toString() === "1" ? ` (Archived)` : ""
            }`,
          keyCreator: (
            params: KeyCreatorParams<
              ICostCodeGroupListResponse["data"]["cost_codes"][0]["sub_code"][0]
            >
          ) => params.value.code_id?.toString(),
        },
        floatingFilterComponentParams: { suppressFilterButton: true },
        floatingFilterComponent: CostCodeFilter,
        valueGetter: ({
          data,
        }: ValueGetterParams<
          ICIDBGetAllItemsListApiResponseData,
          ICIDBGetAllItemsListApiResponseData["cost_code_id"]
        >): Partial<
          ICostCodeGroupListResponse["data"]["cost_codes"][0]["sub_code"][0]
        > => {
          let value = costCodes.reduce((obj, costCode) => {
            const value = costCode.sub_code.find(
              (subCode) =>
                subCode.code_id?.toString() === data?.cost_code_id?.toString()
            );
            if (value) return value;

            return obj;
          }, undefined as ICostCodeGroupListResponse["data"]["cost_codes"][0]["sub_code"][0] | undefined);

          if (!value)
            return {
              code_id: 0,
              csi_name: data?.cost_code_name
                ? data.cost_code_name +
                  (isCostCodesGetting ? "" : " (Archived)")
                : "",
            } as Partial<
              ICostCodeGroupListResponse["data"]["cost_codes"][0]["sub_code"][0]
            >;

          return value;
        },
        cellEditor: CostCodeCellEditor<ICIDBGetAllItemsListApiResponseData>,
        cellClass: "ag-cell-left",
        cellRenderer: ({
          value,
        }: ICellRendererParams<
          ICIDBGetAllItemsListApiResponseData,
          ICostCodeGroupListResponse["data"]["cost_codes"][0]["sub_code"][0]
        >) => {
          const santiziedValue = value
            ? HTMLEntities.decode(
                sanitizeString(
                  value.code_id
                    ? `${value.csi_name}${
                        value.csi_code ? ` (${value.csi_code})` : ""
                      }${
                        value.is_deleted?.toString() === "1"
                          ? ` (Archived)`
                          : ""
                      }`
                    : value?.csi_name
                )
              )
            : "";
          if (!santiziedValue) return "-";
          return (
            <Tooltip title={santiziedValue}>
              <Typography className="table-tooltip-text">
                {santiziedValue}
              </Typography>
            </Tooltip>
          );
        },
        editable: ({ data }) => !checkReadOnly(data?.item_type_id || ""),
        cellEditorParams: {
          values: costCodes,
        },
        valueSetter: ({
          data,
          newValue,
          node,
          ...event
        }: ValueSetterParams<
          ICIDBGetAllItemsListApiResponseData,
          ICostCodeGroupListResponse["data"]["cost_codes"][0]["sub_code"][0]
        >) => {
          const { stopEditing } =
            event as ValueSetterParams<ICIDBGetAllItemsListApiResponseData> & {
              stopEditing: () => void;
            };
          (async function () {
            if (
              newValue?.code_id?.toString() !== data.cost_code_id?.toString()
            ) {
              const oldData = { ...data };
              data.cost_code_name = newValue?.csi_name || "";
              data.cost_code_id = Number(newValue?.code_id) || null;
              node?.setData(data);
              const response = await updateValue({
                item_id: data.id,
                cost_code_name: newValue?.csi_name || "",
                cost_code_id: newValue?.code_id || "",
                cost_code_key: getItemTypeKey({
                  type: getNormalizedItemType(data?.item_type),
                }),
              });
              if (!response.success) {
                node?.setData(oldData);
                notification.error({ description: response.message });
              }
            }
          })();
          stopEditing?.();
          return false;
        },
      },
      {
        headerName: _t("Status"),
        field: "status_name",
        sortable: true,
        maxWidth: 130,
        minWidth: 130,
        suppressMenu: true,
        suppressMovable: false,
        pinned: null,
        hide: true,
        lockPinned: true,
        headerClass: "ag-header-center",
        // filter: "agSetColumnFilter",
        // floatingFilter: true,
        // filterParams: {
        //   suppressMiniFilter: true,
        //   suppressSelectAll: true, // Ensures only one option can be selected
        //   defaultToNothingSelected: true, // Ensures all options can not be selected
        //   placeholder: "Select Status",
        //   values: [
        //     { value: "0", label: "Active" },
        //     { value: "1", label: "Archived" },
        //   ],
        //   allowClear: true,
        //   showSearch: false,
        //   defaultValue: "0",
        //   valueFormatter: (params: ValueFormatterParams<IOption>) =>
        //     params.value.label, // Ensures correct value display
        //   keyCreator: (params: KeyCreatorParams<IOption>) => params.value.value, // Ensures correct filtering by key
        // },
        // floatingFilterComponentParams: {
        //   suppressFilterButton: true,
        //   placeholder: "Select Status",
        // }, // Hide button
        // floatingFilterComponent: DynamicTableSelectFilter,
        cellClass: "ag-cell-center",
        cellEditor: "agRichSelectCellEditor",
        cellRenderer: ({
          value,
        }: ICellRendererParams<
          ICIDBGetAllItemsListApiResponseData,
          ICIDBGetAllItemsListApiResponseData["status_name"]
        >) => {
          return (
            <Tooltip title={value}>
              <div className="text-center">
                <Tag
                  color={
                    value === "Active"
                      ? "#00800026"
                      : value === "Archived"
                      ? "#FF000026"
                      : "#22355826"
                  }
                  className={`mx-auto text-13 type-badge common-tag ${
                    value === "Active"
                      ? "!text-[#008000]"
                      : value === "Archived"
                      ? "!text-[#FF0000]"
                      : "!text-[#223558]"
                  }`}
                >
                  {value}
                </Tag>
              </div>
            </Tooltip>
          );
        },
      },
      {
        headerName: "",
        field: "is_favorite", // for filter is_favorite
        maxWidth: 130,
        minWidth: 130,
        suppressMenu: true,
        lockPosition: "right",
        cellClass: "cell-click-event-none cursor-auto",
        suppressColumnsToolPanel: true,
        pinned: null,
        lockPinned: true,
        headerComponent: FavoriteFilter,
        filter: "agSetColumnFilter",
        filterParams: {
          values: [
            { label: "Favorites", value: 1 },
            { label: "Not Favorites", value: 0 },
          ],
          valueFormatter: (params: ValueFormatterParams) => params.value.label,
          keyCreator: (params: KeyCreatorParams) => params.value.value,
        },
        cellRenderer: ({
          data,
          node,
          ...params
        }: ICellRendererParams<ICIDBGetAllItemsListApiResponseData>) => {
          const { notes, aws_files, is_favorite } = data || {};
          const isReadOnly = checkReadOnly(data?.item_type_id || "");

          let options = [
            {
              label: "Copy This Item",
              icon: "fa-regular fa-copy",
              key: "copy",
            },
            ...ITEMS_OPTIONS.map((option) => ({
              ...option,
              content:
                typeof option.content === "function"
                  ? option.content()
                  : option.content,
            })),
          ];
          const isDeleted = data?.is_deleted?.toString() !== "1";
          options = options.filter((option) =>
            isDeleted ? option.key !== "active" : option.key !== "archive"
          );
          options = options.map((option) => ({
            ...option,
            ...(isReadOnly && option.key === "preview"
              ? {
                  label: "View item",
                }
              : {}),
            ...(option.key === "delete" && data?.is_in_use?.toString() === "1"
              ? {
                  content:
                    "This item is currently in use and cannot be deleted",
                }
              : {}),
            disabled:
              (option.key === "delete" &&
                (allow_delete_module_items === "0" ||
                  data?.is_in_use?.toString() === "1")) ||
              (isReadOnly && option.key !== "preview"),
            onClick: () => {
              if (option.key === "copy" && data) {
                setSelectedData(data);
                setConfirmCopyDialogOpen(true);
              } else {
                setSelectedData(data);
                if (option.key === "delete") {
                  setConfirmDialogOpen(true);
                } else if (
                  option.key === "active" ||
                  option.key === "archive"
                ) {
                  setConfirmArchiveDialogOpen(true);
                } else if (option.key === "preview" && data) {
                  setAddEditSidebarOpen(true);
                  fetchAndSetData(data);
                }
              }
            },
          }));

          return (
            <div className="flex items-center gap-0.5 justify-end">
              <div className="w-6 h-6 flex items-center justify-center">
                {Boolean(notes) && (
                  <Tooltip
                    title={notes}
                    rootClassName="lg:!max-w-[calc(50%-100px)] !max-w-[calc(100%-200px)]"
                  >
                    <FontAwesomeIcon
                      className="text-base w-4 h-4 text-primary-900/80"
                      icon="fa-regular fa-memo"
                    />
                  </Tooltip>
                )}
              </div>
              <div className="w-6 h-6 flex items-center justify-center">
                {Boolean(aws_files?.length) && (
                  <FontAwesomeIcon
                    className="text-base w-4 h-4 text-primary-900/80"
                    icon="fa-regular fa-paperclip"
                  />
                )}
              </div>
              <FavoriteEditor
                data={data}
                node={node}
                updateValue={async (newData) =>
                  updateValue({
                    item_id: newData.id,
                    is_favorite: newData.is_favorite,
                    cost_code_key: getItemTypeKey({
                      type: getNormalizedItemType(newData?.item_type),
                    }),
                  })
                }
                isReadOnly={isReadOnly}
                refreshAgGrid={refreshAgGrid}
                {...params}
              />
              <DropdownMenu
                options={options}
                icon="fa-regular fa-ellipsis-vertical"
                iconClassName="text-primary-900/80 group-hover/buttonHover:text-primary-900"
                buttonClass="m-0 hover:!bg-[#0000000f]"
              />
            </div>
          );
        },
      },
    ],
    [
      costCodes,
      isCostCodesGetting,
      module_access,
      ...(window.ENV.ENABLE_UNIT_DROPDOWN ? [isUnitsGetting] : []),
      fullAccessIDs,
      window.ENV.ENABLE_UNIT_DROPDOWN,
    ]
  );

  const onSearchChange = debounce((e: React.ChangeEvent<HTMLInputElement>) => {
    setAGFilterParams((prev) => ({
      ...prev,
      search: e.target.value?.trim(),
    }));
  }, 300);

  const formData: IAddItemsProps["formInitialValues"] = useMemo(() => {
    if (selectedData) {
      const filePath = selectedData?.file_path
        ? selectedData.file_path.split(",")[0].trim()
        : null;
      return {
        name: HTMLEntities.decode(sanitizeString(selectedData.name)) || "",
        sku: selectedData.sku || "",
        status: selectedData?.is_deleted?.toString() || "",
        product_image: selectedData.photo || "",
        supplier,
        variations: selectedData.variants_items
          ? selectedData.variants_items
              .map((variation) => variation.name?.trim() || "")
              .filter(Boolean)
          : [],
        cost_code: selectedData.cost_code_id?.toString() || "",
        cost_code_name: selectedData.cost_code_name || "",
        unit_cost: selectedData.unit_cost?.toString()
          ? Number(selectedData.unit_cost) / 100
          : null,
        unit:
          HTMLEntities.encode(sanitizeString(selectedData.unit || "")) || "",
        markup: selectedData.markup?.toString() || null,
        hidden_markup: selectedData.hidden_markup?.toString() || null,
        plate_number: selectedData.plate_number?.toString() || "",
        registration_expire_on:
          selectedData.registration_expire_on?.toString() || "",
        insurance_carrier: selectedData.insurance_carrier?.toString() || "",
        inspection_expire_on:
          selectedData.inspection_expire_on?.toString() || "",
        policy_number: selectedData.policy_number?.toString() || "",
        purchase_date: selectedData.purchase_date?.toString() || "",
        policy_expire_on: selectedData.policy_expire_on?.toString() || "",
        // Set Fields for Equipment date and reading time show from Allitems Tab
        e_hours_used:
          (selectedData as ICIDBEquipmentDetail)?.e_hours_used?.toString() ||
          "",
        last_meter_reading_date:
          (
            selectedData as ICIDBEquipmentDetail
          )?.last_meter_reading_date?.toString() || "",
        oil_changed_date:
          (
            selectedData as ICIDBEquipmentDetail
          )?.oil_changed_date?.toString() || "",
        oil_changed_time:
          (
            selectedData as ICIDBEquipmentDetail
          )?.oil_changed_time?.toString() || "",
        last_tire_rotation_date:
          (
            selectedData as ICIDBEquipmentDetail
          )?.last_tire_rotation_date?.toString() || "",
        last_tire_rotation_time:
          (
            selectedData as ICIDBEquipmentDetail
          )?.last_tire_rotation_time?.toString() || "",
        supplier_link: selectedData.supplier_link || "",
        description: selectedData.description || "",
        notes: selectedData.notes || "",
        internal_notes: selectedData.internal_notes || "",
        warranty_details: selectedData.warranty_details || "",
        operator,
        photos: selectedData.aws_files.map((awsFile) => ({
          ...awsFile,
          file_tags: awsFile.file_tags || undefined,
          file_url: awsFile.file_path,
          attach_item_id: awsFile.child_item_id,
          original_pdf_url: awsFile.original_file_path || "",
          projectId: awsFile.project_id || 0,
          project_id: awsFile.project_id || 0,
          name: awsFile.file_name,
          fileName: awsFile.file_name,
          annotation_data: awsFile.annotation_data || "",
          original_file_path: awsFile.original_file_path || "",
          image: awsFile.image || "",
          quickbook_attachable_id: Number(awsFile.quickbook_attachable_id),
          notes: awsFile.notes || "",
          project_name: awsFile.project_name || "",
          date_added: awsFile.date_added || "",
          id: awsFile.id || 0,
        })),
        is_favorite: Boolean(selectedData.is_favorite),
      };
    }
    return {
      name: "",
      sku: "",
      status: "0",
      product_image: undefined,
      supplier: undefined,
      variations: [],
      cost_code: "",
      unit_cost: undefined,
      unit: "",
      markup: markupDefaultValue || null,
      hidden_markup: null,
      supplier_link: "",
      description: "",
      notes: "",
      photos: [],
      is_favorite: false,
      operator: undefined,
      items: [],
    };
  }, [selectedData, supplier, operator, markupDefaultValue]);

  const isActiveButton = useMemo(() => {
    if (
      (isSelectedItemsActive && AGFilterParams.status?.toString() === "2") ||
      AGFilterParams.status?.toString() === "0"
    ) {
      return true;
    }
    return false;
  }, [isSelectedItemsActive, AGFilterParams?.status]);

  const getRenderedNodes = () =>
    (
      gridRef.current?.api.getRenderedNodes() as
        | IRowNode<ICIDBGetAllItemsListApiResponseData>[]
        | undefined
    )
      ?.filter(({ data }) =>
        data
          ? (
              document.querySelector(".item_" + data.id + " input") as
                | HTMLInputElement
                | undefined
            )?.checked
          : undefined
      )
      .map(({ data }) => data as ICIDBGetAllItemsListApiResponseData) || [];

  const updateOneBuildItem = async () => {
    try {
      const data = await getWebWorkerApiParams({
        otherParams: {
          sourceId: selectedData?.one_build_id,
          sourceUom: selectedData?.unit,
          sourceType: getAddAllItemsTypesForOneBuild({
            type:
              selectedData?.item_type?.toString().toLowerCase() ===
              "sub contract"
                ? "subcontractor"
                : selectedData?.item_type?.toString().toLowerCase() || "",
          }),
        },
      });

      const response = (await webWorkerApi({
        url: cidbItemRoutes.getImportedOneBuildItems,
        method: "post",
        data: data,
      })) as ICIDBImportOneBuildChangeMaterialItemApiResponse;

      if (
        response.success &&
        (!response.data.oneBuildResponse ||
          Object.keys(response.data.oneBuildResponse).length === 0)
      ) {
        const hasErrors =
          Array.isArray(response.data.oneBuildErrorResponse) &&
          response.data.oneBuildErrorResponse.length > 0;

        const errorMessage = hasErrors
          ? response?.data?.oneBuildErrorResponse?.[0]?.message ||
            "Failed to fetch OneBuild details"
          : "Failed to fetch OneBuild details";

        return {
          statusCode: 400,
          success: false,
          message: errorMessage,
        } as ICIDBImportOneBuildChangeMaterialItemApiResponse;
      }

      if (response.success) {
        const updatedResponse = await updateValue({
          item_id: selectedData?.id,
          unit_cost: response?.data?.price
            ? Number(response.data.price) * 100
            : 0,
          unit: HTMLEntities.encode(response?.data?.uom),
          cost_code_key: getItemTypeKey({
            type: getNormalizedItemType(selectedData?.item_type),
          }),
          nahbDivision: response.data?.oneBuildResponse?.nahbDivision,
          nahbCodeDescription:
            response.data?.oneBuildResponse?.nahbCodeDescription,
          nahbDivisionDescription:
            response.data?.oneBuildResponse?.nahbDivisionDescription,
          nahbCode: response.data?.oneBuildResponse?.nahbCode,
          csiDivisionNumber: response.data?.oneBuildResponse?.csiDivisionNumber,
          csiTitle: response.data?.oneBuildResponse?.csiTitle,
          csiDivisionName: response.data?.oneBuildResponse?.csiDivisionName,
          csiSection: response.data?.oneBuildResponse?.csiSection,
          name: response.data?.oneBuildResponse?.name,
          notes: response.data?.oneBuildResponse?.description,
          photo: response.data?.oneBuildResponse?.imagesUrls?.[0],
          update_to_original_one_build_item: 1,
        });
        if (updatedResponse.success) {
          const updatedData = updatedResponse.data as UpdatedResponseData;

          return {
            ...response,
            data: {
              ...response.data,
              cost_code_name: updatedData.cost_code_name,
              cost_code_id: updatedData.cost_code_id,
            },
          };
        } else {
          return {
            statusCode: updatedResponse.statusCode,
            success: updatedResponse.success,
            message: updatedResponse.message,
          } as ICIDBImportOneBuildChangeMaterialItemApiResponse;
        }
      } else {
        return response;
      }
    } catch (error) {
      return {
        statusCode: 500,
        success: false,
        message: "Failed to update data!",
      } as ICIDBImportOneBuildChangeMaterialItemApiResponse;
    }
  };

  const handleUpdateClick = async () => {
    const response = await updateOneBuildItem();
    if (
      response.success &&
      (!response.data.oneBuildResponse ||
        Object.keys(response?.data?.oneBuildResponse).length === 0)
    ) {
      const hasErrors =
        Array.isArray(response?.data?.oneBuildErrorResponse) &&
        response?.data?.oneBuildErrorResponse.length > 0;

      const errorMessage = hasErrors
        ? response?.data?.oneBuildErrorResponse?.[0]?.message ||
          "Failed to fetch OneBuild details"
        : "Failed to fetch OneBuild details";

      notification.error({ description: errorMessage });
      return;
    }

    if (response.success) {
      setSelectedData((prev) =>
        prev
          ? {
              ...prev,
              unit_cost: (Number(response.data.price) * 100).toString(),
              unit: response?.data?.uom,
              update_to_original_one_build_item: 1,
              ...(response.data.oneBuildResponse && {
                nahbDivision: response.data.oneBuildResponse.nahbDivision,
                nahbCodeDescription:
                  response.data.oneBuildResponse.nahbCodeDescription,
                nahbDivisionDescription:
                  response.data.oneBuildResponse.nahbDivisionDescription,
                nahbCode: response.data.oneBuildResponse.nahbCode,
                csiDivisionNumber:
                  response.data.oneBuildResponse.csiDivisionNumber,
                csiTitle: response.data.oneBuildResponse.csiTitle,
                csiDivisionName: response.data.oneBuildResponse.csiDivisionName,
                csiSection: response.data.oneBuildResponse.csiSection,
                name: response.data.oneBuildResponse.name,
                notes: response.data.oneBuildResponse.description,
                photo: response.data.oneBuildResponse.imagesUrls?.[0],
                cost_code_name: response?.data?.cost_code_name,
                cost_code_id: response?.data?.cost_code_id,
              }),
            }
          : prev
      );
    } else {
      notification.error({ description: response.message });
    }
  };

  const getStatusChangeAndDeleteAPIIds = (needIsInUse = true) => {
    const defaultIds = {
      materials: undefined as number[] | undefined,
      equipments: undefined as number[] | undefined,
      labors: undefined as number[] | undefined,
      sub_contractors: undefined as number[] | undefined,
      other_items: undefined as number[] | undefined,
    };
    try {
      let items = selectedData ? [selectedData] : [];
      if (!items.length) {
        let selectedRows = getRenderedNodes();
        if (selectedRows.length) {
          if (needIsInUse) {
            items = selectedRows || [];
          } else {
            items =
              selectedRows.filter(
                (item) => item.is_in_use?.toString() !== "1"
              ) || [];
          }
        }
      }
      if (!items.length) {
        return Object.entries(defaultIds).reduce((obj, [key, value]) => {
          if (value && value.length) {
            return Object.assign(obj, { [key]: value });
          }
          return obj;
        }, {} as Partial<typeof defaultIds>);
      }
      const itemIds = items.reduce((obj, item) => {
        switch (item?.item_type_id?.toString()) {
          case CFConfig.material_module_id?.toString():
            if (obj.materials) {
              obj.materials.push(item.id);
            } else {
              obj.materials = [item.id];
            }
            break;
          case CFConfig.labor_module_id?.toString():
            if (obj.labors) {
              obj.labors.push(item.id);
            } else {
              obj.labors = [item.id];
            }
            break;
          case CFConfig.other_items_module_id?.toString():
            if (obj.other_items) {
              obj.other_items.push(item.id);
            } else {
              obj.other_items = [item.id];
            }
            break;
          case CFConfig.equipment_module_id?.toString():
            if (obj.equipments) {
              obj.equipments.push(item.id);
            } else {
              obj.equipments = [item.id];
            }
            break;
          case CFConfig.subcontractor_module_id?.toString():
            if (obj.sub_contractors) {
              obj.sub_contractors.push(item.id);
            } else {
              obj.sub_contractors = [item.id];
            }
            break;

          default:
            console.error(`\n File: #allItems.tsx -> Line: #1107 ->  `, item);
            break;
        }
        return obj;
      }, defaultIds);
      return Object.entries(itemIds).reduce((obj, [key, value]) => {
        if (value && value.length) {
          return Object.assign(obj, { [key]: value });
        }
        return obj;
      }, {} as Partial<typeof itemIds>);
    } catch (error) {
      console.error(
        `\n File: #allItems.tsx -> Line: #1059 ->  `,
        (error as Error).message
      );
      return Object.entries(defaultIds).reduce((obj, [key, value]) => {
        if (value && value.length) {
          return Object.assign(obj, { [key]: value });
        }
        return obj;
      }, {} as Partial<typeof defaultIds>);
    }
  };

  const onColumnMoved = (event: ColumnMovedEvent) => {
    const { columnApi } = event;
    const columnState = columnApi.getColumnState();

    const getIndex = (colId: string) =>
      columnState.findIndex((col) => col.colId === colId);
    const swapColumns = (fromIndex: number, toIndex: number) => {
      if (fromIndex === -1 || toIndex === -1 || fromIndex === toIndex + 1)
        return;
      const [movedCol] = columnState.splice(fromIndex, 1);
      columnState.splice(toIndex, 0, movedCol);
    };

    const oneBuildIDIndex = getIndex("one_build_id");
    const skuIndex = getIndex("sku");
    const filePathIndex = getIndex("file_path");
    const nameIndex = getIndex("name");

    if (
      filePathIndex === -1 ||
      nameIndex === -1 ||
      oneBuildIDIndex === -1 ||
      skuIndex === -1
    )
      return;

    swapColumns(filePathIndex, nameIndex - 1); // Ensure "photo" comes before "name"
    swapColumns(oneBuildIDIndex, skuIndex - 1); // Ensure "one_build_id" comes before "sku"

    columnApi.applyColumnState({ state: columnState, applyOrder: true });

    const cols = columnState.map(
      (col) => event.api.getColumnDef(col.colId) || {}
    );
    colChange.cancel();
    colChange(cols);
  };

  const colChange = debounce((cols: ColDef[]) => {
    gridRef.current?.api.setColumnDefs(cols);
  }, 400);

  useEffect(() => {
    if (addEditSidebarOpen && selectedData) {
      const gridApi = gridRef?.current?.api;
      if (gridApi) {
        const rowCount = gridApi.getDisplayedRowCount();
        for (let i = 0; i < rowCount; i++) {
          const rowNode = gridApi.getDisplayedRowAtIndex(i);
          if (rowNode?.data?.id === selectedData.id) {
            setCurrentItemIndex(i);
            break;
          }
        }
      }
    }
  }, [addEditSidebarOpen, selectedData]);

  // Utility function to get updated fields
  const getUpdatedFields = (
    values: Partial<IAddItemsProps["formInitialValues"]>,
    formData: any
  ) => {
    const fieldsToCheck: Partial<IAddItemsProps["formInitialValues"]> = {
      sku: values.sku,
      status: values.status,
      supplier_link: values.supplier_link,
    };

    return Object.fromEntries(
      Object.entries(fieldsToCheck).filter(
        ([key, value]) => value !== formData[key]
      )
    );
  };

  const generateDataObject = async (
    selectedData: ICIDBGetAllItemsListApiResponseData | undefined,
    values: Partial<IAddItemsProps["formInitialValues"]>,
    formData: any
  ) => {
    return await getWebWorkerApiParams({
      otherParams: {
        item_id: selectedData?.id,
        cost_code_key: getItemTypeKey({
          type:
            selectedData?.item_type?.toString().toLowerCase() === "sub contract"
              ? "subcontractor"
              : selectedData?.item_type?.toString().toLowerCase() || "",
        }),
        ...getCIDBValidationFieldsUpdate(
          {
            name: values.name,
            unit: values.unit,
            unit_cost: values.unit_cost?.toString()
              ? (values.unit_cost * 100).toString()
              : null,
            markup: values.markup,
            hidden_markup: values.hidden_markup,
          },
          values.name,
          "name"
        ),
        ...getUpdatedFields(values, formData),
        ...(values.cost_code !== formData.cost_code
          ? { cost_code_id: values.cost_code }
          : {}),

        ...(values.notes !== formData.notes ? { notes: values.notes } : {}),
        ...(values.internal_notes !== formData.internal_notes
          ? { internal_notes: values.internal_notes }
          : {}),
        ...(values.warranty_details !== formData.warranty_details
          ? { warranty_details: values.warranty_details }
          : {}),
        ...(values.plate_number !== formData.plate_number
          ? { plate_number: values.plate_number }
          : {}),
        ...(values.registration_expire_on !== formData.registration_expire_on
          ? { registration_expire_on: values.registration_expire_on }
          : {}),
        ...(values.policy_expire_on !== formData.policy_expire_on
          ? { policy_expire_on: values.policy_expire_on }
          : {}),
        ...(values.insurance_carrier !== formData.insurance_carrier
          ? { insurance_carrier: values.insurance_carrier }
          : {}),
        ...(values.policy_number !== formData.policy_number
          ? { policy_number: values.policy_number }
          : {}),
        ...(values.purchase_date !== formData.purchase_date
          ? { purchase_date: values.purchase_date }
          : {}),
        ...(values.product_image !== formData.product_image
          ? { photo: values.product_image || "" }
          : {}),
        ...(values.supplier?.user_id
          ? values.supplier.user_id.toString() !==
            formData.supplier?.user_id?.toString()
            ? { supplier_id: values.supplier.user_id }
            : {}
          : { supplier_id: 0 }),
        ...(selectedData?.item_type === "Equipment"
          ? { operator_id: 0 }
          : values.operator?.user_id &&
            values.operator.user_id.toString() !==
              formData.operator?.user_id?.toString()
          ? { operator_id: values.operator.user_id }
          : {}),
        ...(values.variations?.join(", ") !== formData.variations?.join(", ")
          ? { item_variations: values.variations?.join(", ") }
          : {}),
        ...(values.is_favorite !== formData.is_favorite
          ? { is_favorite: Number(values.is_favorite) }
          : {}),
      },
    });
  };

  // Helper function to handle item update logic
  const handleItemUpdate = async (
    selectedData: ICIDBGetAllItemsListApiResponseData,
    values: Partial<IAddItemsProps["formInitialValues"]>,
    formData: any,
    isNavigationRef: React.MutableRefObject<boolean>,
    formikHelpers: FormikHelpers<IAddItemsProps["formInitialValues"]>,
    setAddEditSidebarOpen: (open: boolean) => void,
    setSelectedData: (
      data: ICIDBGetAllItemsListApiResponseData | undefined
    ) => void,
    refreshAgGrid: () => void
  ) => {
    const data = await generateDataObject(selectedData, values, formData);
    const apis = [updateValue(data)];

    // Filter existing and new files
    const existFiles = selectedData.aws_files.filter((record) =>
      values?.photos?.some((photo) => photo.file_path === record.file_path)
    );

    const removedFiles = selectedData.aws_files.filter(
      (record) =>
        !values?.photos?.some((photo) =>
          record.image_id
            ? photo.image_id === record.image_id
            : photo.file_path === record.file_path
        )
    );

    const newFiles = values?.photos?.filter(
      (photo) =>
        !existFiles.some((record) => photo.file_path === record.file_path)
    );

    // Add file upload API call if new files exist
    if (newFiles?.length) {
      apis.push(
        (async function () {
          try {
            let itemType =
              selectedData?.item_type?.toString()?.toLowerCase() || "";
            if (itemType === "sub contract") {
              itemType = "subcontractor";
            }
            const moduleId = getItemTypeKeyModuleId({ type: itemType });
            return (await webWorkerApi({
              url: apiRoutes.COMMON.add_file,
              method: "post",
              data: {
                module_id,
                primary_id: moduleId,
                item_id: selectedData.id,
                files: newFiles.map((photo) => ({
                  is_image: photo.is_image,
                  file_name: photo.file_name,
                  file_ext: photo.file_ext,
                  file_url: photo.file_path,
                  isheic: photo?.file_ext.toLowerCase() === "heic" ? 1 : 0,
                })),
              },
            })) as IApiCallResponse;
          } catch (error) {
            return {
              success: false,
              message: (error as Error).message,
            } as IApiCallResponse;
          }
        })()
      );
    } else {
      apis.push(
        (async function () {
          return { success: true } as IApiCallResponse;
        })()
      );
    }

    if (removedFiles.length) {
      apis.push(
        (async function () {
          try {
            let deleteParams: IUpdateFilesRequestBody = {};
            removedFiles.forEach(({ image_id, file_path }) => {
              if (image_id) {
                const imageIdNumber = image_id as number;
                if (deleteParams && deleteParams.fileIds) {
                  deleteParams = {
                    fileIds: [...deleteParams.fileIds, imageIdNumber],
                  };
                } else {
                  deleteParams = { fileIds: [imageIdNumber] };
                }
              } else {
                const filePath = file_path as string;
                if (deleteParams && deleteParams.fileUrl) {
                  deleteParams = {
                    fileUrl: [...deleteParams.fileUrl, filePath],
                  };
                } else {
                  deleteParams = { fileUrl: [filePath] };
                }
              }
            });
            return (await deleteFiles(deleteParams)) as IApiCallResponse;
          } catch (error) {
            return {
              success: false,
              message: (error as Error).message,
            } as IApiCallResponse;
          }
        })()
      );
    } else {
      apis.push(
        (async function () {
          return { success: true } as IApiCallResponse;
        })()
      );
    }

    // Execute all APIs in parallel
    const [response, updateFilesResponse, removedFilesResponse] =
      await Promise.all(apis);

    // Handle API responses
    if (!updateFilesResponse.success) {
      notification.error({
        description: updateFilesResponse.message,
      });
    } else if (!removedFilesResponse.success) {
      notification.error({
        description: removedFilesResponse.message,
      });
    }
    if (!response.success) {
      notification.error({
        description: response.message,
      });
    } else {
      if (!isNavigationRef.current) {
        formikHelpers.resetForm();
        setAddEditSidebarOpen(false);
        setSelectedData(undefined);
        refreshAgGrid();
      }
    }

    return { response, updateFilesResponse };
  };

  const onNavigate = useCallback(
    debounce(async (increment: number) => {
      if (!addItemsRef.current || isNavigatingRef.current) return;

      isNavigatingRef.current = true;

      try {
        const formik = addItemsRef.current.formik;
        const formModified = !isEqual(formik.values, formData);
        const isValid = await formik.validateForm();
        if (Object.keys(isValid).length !== 0) {
          const fields = Object.keys(formik.errors).map((name) => {
            switch (name) {
              case "unit":
                return "Unit";
              case "unit_cost":
                return "Unit Cost";
              case "name":
                return "Name";
              default:
                return name;
            }
          });
          let errorMessage = "";
          if (fields.length === 1) {
            errorMessage += fields[0] + " is required.";
          } else {
            const lastField = fields.pop();
            errorMessage += `${fields.join(
              ", "
            )} and ${lastField} are required.`;
          }
          notification.error({
            description: errorMessage,
          });
          return;
        }

        const formikHelpers: Partial<
          FormikHelpers<IAddItemsProps["formInitialValues"]>
        > = {
          resetForm: formik.resetForm,
        };

        if (formModified) {
          if (Object.keys(isValid).length === 0) {
            isNavigationRef.current = true;
            const values = formik.values;
            await generateDataObject(selectedData, values, formData);
            if (selectedData) {
              const { response } = await handleItemUpdate(
                selectedData,
                values,
                formData,
                isNavigationRef,
                formikHelpers as FormikHelpers<
                  IAddItemsProps["formInitialValues"]
                >,
                setAddEditSidebarOpen,
                setSelectedData,
                refreshAgGrid
              );
              if (response.success) {
                const gridApi = gridRef?.current?.api;
                const rowNode =
                  gridApi?.getDisplayedRowAtIndex(currentItemIndex);
                if (rowNode) {
                  const updatedRowData = {
                    ...rowNode.data,
                    ...formik.values,
                  } as ICIDBGetAllItemsListApiResponseData;
                  rowNode.setData(updatedRowData);
                }
                setHasChanges(true);
              }
            }
          } else {
            notification.error({
              description: "Please fix the form errors before navigating.",
            });
            return;
          }
        }

        const newIndex = currentItemIndex + increment;
        if (newIndex >= 0 && newIndex < rowCount) {
          setCurrentItemIndex(newIndex);
          const rowNode =
            gridRef?.current?.api.getDisplayedRowAtIndex(newIndex);
          if (rowNode) {
            const selectedData = rowNode.data;
            if (selectedData) {
              setSelectedData(selectedData);
              fetchAndSetData(selectedData);
            }
          }
        }
      } finally {
        isNavigatingRef.current = false;
      }
    }, 200),
    [
      addItemsRef,
      currentItemIndex,
      formData,
      gridRef,
      isNavigationRef,
      refreshAgGrid,
      setCurrentItemIndex,
      setSelectedData,
      fetchAndSetData,
      rowCount,
    ]
  );

  return (
    <>
      <DetailsTopBar
        sidebarCollapse={sidebarCollapse}
        refreshAgGrid={refreshAgGrid}
        searchProps={{
          onSearch: (value) => {
            setAGFilterParams((prev) => ({
              ...prev,
              search: value?.trim(),
            }));
          },
          onChange: (e) => {
            onSearchChange.cancel();
            onSearchChange(e);
          },
        }}
        statusSelectProps={{
          value: AGFilterParams.status?.toString() || "0",
          onChange: (value) => {
            if (typeof value === "string") {
              setAGFilterParams((prev) => ({
                ...prev,
                status: Number(value),
              }));
              if (gridRef.current) {
                const columnState = gridRef.current.columnApi.getColumnState();
                if (columnState.length) {
                  const newColumns = columnState.map((column) => {
                    let newColumn =
                      gridRef.current?.api.getColumnDef(column.colId) || {};
                    if (newColumn && Object.keys(newColumn).length) {
                      if (column.colId === "checkbox") {
                        newColumn.headerCheckboxSelection = !(
                          value?.toString() === "2"
                        );
                      }
                      // else if (column.colId === "status_name") {
                      //   gridRef.current?.api
                      //     ?.getFilterInstance(column.colId)
                      //     ?.setModel({
                      //       filterType: "set",
                      //       type: "set",
                      //       values: value && value !== "2" ? [value] : [],
                      //     });
                      // }
                    }
                    return newColumn;
                  });
                  colChange.cancel();
                  colChange(newColumns);
                  gridRef.current?.api.onFilterChanged();
                }

                const allColDefs = gridRef.current.api.getColumnDefs(); // This gives current ColDef[]
                if (allColDefs?.length) {
                  const newColDefs = allColDefs.map((colDef: any) => {
                    if (colDef?.field === "status_name") {
                      return {
                        ...colDef,
                        hide: value?.toString() !== "2",
                      };
                    }
                    return colDef;
                  });
                  colChange.cancel();
                  colChange(newColDefs);
                }
              }
            }
          },
        }}
        getMarkupItems={() => {
          const itemIds = getStatusChangeAndDeleteAPIIds();
          if (Object.values(itemIds).length) {
            return {
              ids: itemIds,
            };
          }
        }}
        otherOptionsDropdownProps={{
          options: Object.entries(MLESOITEMS_SELECT_OPTION_BY_KEYS).reduce(
            (arr, [key, value]) => {
              if (
                key === MLESOITEMS_SELECT_OPTION_KEYS.archive_selected_items
              ) {
                arr.push({
                  ...value,
                  label: isActiveButton
                    ? value.label
                    : value.label?.replace("Archive", "Activate"),
                  onClick: () => {
                    setConfirmArchiveDialogOpen(true);
                  },
                  disabled: !isItemsSelected,
                  itemClass: !isItemsSelected ? "hidden" : "",
                });
              } else if (
                key === MLESOITEMS_SELECT_OPTION_KEYS.delete_selected_items
              ) {
                arr.push({
                  ...value,
                  onClick: () => {
                    let selectedRows = getRenderedNodes();
                    if (selectedRows.length) {
                      if (
                        selectedRows.every(
                          (row) => row.is_in_use?.toString() === "1"
                        )
                      ) {
                        setInUsesSelectedItems("every_items_in_use");
                      } else if (
                        selectedRows.some(
                          (row) => row.is_in_use?.toString() === "1"
                        )
                      ) {
                        setInUsesSelectedItems("some_items_in_use");
                      } else {
                        setInUsesSelectedItems("no_items_in_use");
                      }
                    }
                    setConfirmDialogOpen(true);
                  },
                  disabled: !isItemsSelected,
                  itemClass: !isItemsSelected ? "hidden" : "",
                });
              } else if (
                MLESOITEMS_SELECT_OPTION_KEYS.view_import_template !== key
              ) {
                arr.push(value);
              }
              return arr;
            },
            [] as ICIDBDetailsTopBarProps["otherOptionsDropdownProps"]["options"]
          ),
        }}
        addNewComponent={
          module_access === "full_access" && (
            <>
              <DropdownMenu
                contentClassName="w-[180px] add-items-drop-down"
                options={ADD_ITEMS_OPTION.filter((option) =>
                  "moduleTabId" in option
                    ? option.moduleTabId
                      ? checkGlobalModulePermission(option.moduleTabId) ===
                        "full_access"
                      : false
                    : true
                )}
                buttonClass="w-fit h-[26px] m-0 !bg-blue-100 hover:!bg-blue-100 p-0 add-select-dropdown rounded-r-sm"
                onOptionClick={(data) => {
                  setSelectedItemType(data.value);
                  setAddEditSidebarOpen(true);
                }}
                children={
                  <div className="flex items-center gap-2">
                    <div className="h-[26px] w-6 flex items-center justify-center text-white bg-primary-900 rounded-l dark:!bg-dark-950">
                      <FontAwesomeIcon
                        icon="fa-regular fa-plus"
                        className="w-[13px] h-[13px] m-auto !text-white dark:!text-white"
                      />
                    </div>
                    <Typography className="text-13 text-primary-900 font-medium">
                      {_t("Item")}
                    </Typography>
                    <FontAwesomeIcon
                      className="pr-2 w-3 h-3 text-primary-900"
                      icon="fa-regular fa-chevron-down"
                    />
                  </div>
                }
              />
              {/* {isItemsSelected && (
                <div className="bg-[#F8F8F8] rounded-md flex items-center justify-end gap-1.5 px-2 py-1">
                  <ButtonWithTooltip
                    tooltipTitle={_t("Select Multiple Items to Delete")}
                    tooltipPlacement="top"
                    icon="fa-regular fa-trash-can"
                    onClick={() => {
                      setConfirmDialogOpen(true);
                    }}
                  />
                  <ButtonWithTooltip
                    tooltipTitle={
                      isActiveButton ? _t("Archive Items") : _t("Active Items")
                    }
                    tooltipPlacement="top"
                    icon={
                      isActiveButton
                        ? "fa-regular fa-ban"
                        : "fa-regular fa-circle-check"
                    }
                    onClick={() => {
                      setConfirmArchiveDialogOpen(true);
                    }}
                  />
                  <ButtonWithTooltip
                    tooltipTitle={_t("Cancel Items")}
                    tooltipPlacement="top"
                    icon="fa-regular fa-close"
                    onClick={() => {
                      gridRef.current?.api.deselectAll();
                      setIsSelectedItemsActive(false);
                      setIsItemsSelected(false);
                    }}
                  />
                </div>
              )} */}
            </>
          )
        }
        currentTab={MLESOITEMS_SELECTOPTION_TAB_KEYS.all_items}
        module={allItemsModule}
        refresh={refreshAgGrid}
        readOnly={module_access === "read_only"}
      />
      <div className="pt-2">
        <div
          className={`list-view-table ag-grid-cell-pointer ag-theme-alpine header-custom-height-remove ${
            module_access !== "full_access" ||
            currentOption?.access === "read_only"
              ? "h-[calc(100dvh-278px)]"
              : "h-[calc(100dvh-238px)]"
          }`}
        >
          <DynamicTable
            ref={gridRef}
            onGridReady={onGridReady}
            onSortChanged={onSortChanged}
            columnDefs={columnDefs}
            floatingFiltersHeight={40}
            suppressRowClickSelection={true} // Prevents row selection on row click
            rowSelection="multiple"
            className="mleso-items-table"
            onColumnMoved={onColumnMoved}
            cacheBlockSize={40}
            loadingCellRendererParams={{
              cacheBlockSize: 40,
            }}
            onFilterChanged={(event) => {
              const filterInstance =
                event?.api?.getFilterInstance("status_name");
              if (filterInstance) {
                const model = filterInstance.getModel();
                setAGFilterParams((prev) => ({
                  ...prev,
                  status: model?.values?.length
                    ? Number(model.values[0])
                    : prev.status,
                }));
              }
              event.api.deselectAll();
            }}
            onCellClicked={(event) => {
              const openAddEditSidebar = () => {
                const uneditableCellClick = getAGGridValidCellClick(event);
                if (uneditableCellClick) {
                  setSelectedData(event?.data);
                  setAddEditSidebarOpen(true);

                  if (event?.data) {
                    fetchAndSetData(event.data);
                  }
                }
              };
              if (checkReadOnly(event?.data?.item_type_id || "")) {
                openAddEditSidebar();
              } else {
                onSingleClick(() => {
                  openAddEditSidebar();
                });
              }
            }}
            onSelectionChanged={({ api }) => {
              const items = (
                api.getRenderedNodes() as IAGGridSelectHandlerFunItem<ICIDBGetAllItemsListApiResponseData>["rowNode"][]
              ).reduce((items, event) => {
                const rowNode =
                  event as IAGGridSelectHandlerFunItem<ICIDBGetAllItemsListApiResponseData>["rowNode"];
                items.push({
                  rowNode,
                  htmlElement: document.querySelector(
                    ".item_" + rowNode.data?.id + " input"
                  ) as HTMLInputElement | undefined,
                });
                return items;
              }, [] as IAGGridSelectHandlerFunItem<ICIDBGetAllItemsListApiResponseData>[]);

              const selectedItems =
                agGridSelectHandler<ICIDBGetAllItemsListApiResponseData>(
                  items,
                  AGFilterParams.status?.toString()
                );

              if (
                AGFilterParams.status?.toString() === "2" &&
                selectedItems.length
              ) {
                setIsSelectedItemsActive(
                  selectedItems[0].rowNode.data?.is_deleted?.toString() === "0"
                );
              } else {
                setIsSelectedItemsActive(false);
              }

              if (selectedItems.length) {
                setIsItemsSelected(true);
              } else {
                setIsItemsSelected(false);
              }
            }}
            noRowsOverlayComponent={() => (
              <NoRecords
                rootClassName="w-full max-w-[280px] responsive-image-size"
                image={`${window.ENV.CDN_URL}assets/images/create-record-list-view.svg`}
                text={
                  module_access === "full_access" ? (
                    <div className="flex items-center gap-1">
                      <DropdownMenu
                        contentClassName="w-[180px] add-items-drop-down"
                        options={ADD_ITEMS_OPTION.filter((option) =>
                          "moduleTabId" in option
                            ? option.moduleTabId
                              ? checkGlobalModulePermission(
                                  option.moduleTabId
                                ) === "full_access"
                              : false
                            : true
                        )}
                        buttonClass="w-fit h-[26px] m-0 !bg-transparent p-0 add-select-dropdown rounded-r-sm"
                        onOptionClick={(data) => {
                          setSelectedItemType(data.value);
                          setAddEditSidebarOpen(true);
                        }}
                        children={
                          <Typography className="sm:text-base text-xs underline underline-offset-1 text-black font-bold cursor-pointer">
                            {_t("Click here ")}
                          </Typography>
                        }
                      />
                      <Typography className="sm:text-base text-xs text-black font-semibold">
                        {_t("to Create a New Record")}
                      </Typography>
                    </div>
                  ) : (
                    <Typography className="sm:text-base text-xs text-black font-semibold">
                      {_t("No Record Found")}
                    </Typography>
                  )
                }
              />
            )}
          />
        </div>
      </div>
      {confirmArchiveDialogOpen && (
        <ConfirmModal
          isOpen={confirmArchiveDialogOpen}
          modaltitle={
            selectedData
              ? _t(
                  selectedData?.is_deleted?.toString() === "1"
                    ? "Active"
                    : "Archive"
                )
              : _t((!isActiveButton ? "Active" : "Archive") + " Items")
          }
          description={_t(
            `Are you sure you want to ${
              selectedData
                ? selectedData.is_deleted?.toString() === "1"
                  ? "Activate"
                  : "Archive"
                : !isActiveButton
                ? "Activate"
                : "Archive"
            } ${
              selectedData
                ? selectedData.is_deleted?.toString() === "1"
                  ? "this item?"
                  : "this item?"
                : !isActiveButton
                ? "these items?"
                : "these items?"
            } 
            `
          )}
          modalIcon={
            "fa-regular " +
            (selectedData
              ? selectedData.is_deleted?.toString() === "1"
                ? "fa-regular-active"
                : "fa-box-archive"
              : !isActiveButton
              ? "fa-regular-active"
              : "fa-box-archive")
          }
          isLoading={loading}
          onAccept={async () => {
            const itemIdsParams = getStatusChangeAndDeleteAPIIds();
            if (!Object.keys(itemIdsParams).length) {
              notification.error({
                description: "Something went wrong!",
              });
              return;
            }
            setLoading(true);
            try {
              const params = await getWebWorkerApiParams({
                otherParams: {
                  ids: itemIdsParams,
                  is_deleted:
                    selectedData?.is_deleted?.toString() === "0" ||
                    isActiveButton
                      ? 1
                      : 0,
                },
              });

              const response = (await webWorkerApi({
                url: cidbItemRoutes.item_status_change,
                method: "post",
                data: params,
              })) as ICIDBStatusChangeEquipmentItemApiResponse;
              if (response.success) {
                setConfirmArchiveDialogOpen(false);
                setSelectedData(undefined);
                refreshAgGrid();
                setIsSelectedItemsActive(false);
                setIsItemsSelected(false);
              } else {
                notification.error({
                  description: response.message,
                });
              }
            } catch (error) {
              notification.error({
                description:
                  (error as Error)?.message || "Something went wrong!",
              });
            }
            setLoading(false);
          }}
          onDecline={() => {
            setConfirmArchiveDialogOpen(false);
            setSelectedData(undefined);
          }}
          onCloseModal={() => {
            setConfirmArchiveDialogOpen(false);
            setSelectedData(undefined);
          }}
        />
      )}
      {confirmDialogOpen && (
        <ConfirmModal
          isOpen={confirmDialogOpen}
          modaltitle={
            rowNodeID
              ? _t("Source: 1build Update")
              : copyExistingItemID
              ? _t("Copy Existing Item")
              : selectedData
              ? _t("Delete")
              : _t("Delete Items")
          }
          description={
            rowNodeID
              ? _t("Are you sure you want to Update item as per 1build?")
              : copyExistingItemID
              ? _t(
                  "You have already copied this item, are you sure you want to make another copy?"
                )
              : selectedData
              ? _t(
                  `Are you sure you want to delete this item from your Cost Item Database? Any items that are in use will be Archived instead.`
                )
              : inUsesSelectedItems === "every_items_in_use"
              ? _t(
                  `The selected Items are currently in use (assigned within a record) and cannot be deleted. You can archive the used Items to hide them from view.`
                )
              : inUsesSelectedItems === "some_items_in_use"
              ? _t(
                  "Some of the selected Items are currently in use (assigned within a record) and cannot be deleted. You can archive the used items to hide them from view. Are you sure you want to delete the remaining unused Items from your Cost Items Database?"
                )
              : _t(
                  "Are you sure you want to delete the selected unused Items from your Cost Items Database?"
                )
          }
          noButtonLabel={
            copyExistingItemID
              ? _t("View Copy")
              : inUsesSelectedItems === "every_items_in_use"
              ? null
              : undefined
          }
          yesButtonLabel={
            inUsesSelectedItems === "every_items_in_use" ? "Ok" : undefined
          }
          modalIcon={
            rowNodeID
              ? "fa-regular fa-file-check"
              : copyExistingItemID
              ? "fa-regular fa-triangle-exclamation"
              : "fa-regular fa-trash-can"
          }
          isLoading={loading}
          additionalButton={copyExistingItemID ? _t("No") : undefined}
          onAccept={async () => {
            if (rowNodeID && selectedData) {
              setLoading(true);
              const response = await updateOneBuildItem();
              if (response.success) {
                const rowNode = gridRef.current?.api?.getRowNode(rowNodeID);
                if (rowNode) {
                  rowNode.setData({
                    ...selectedData,
                    unit_cost: (Number(response.data.price) * 100).toString(),
                    unit: response?.data?.uom,
                    name: response.data?.oneBuildResponse?.name,
                    notes: response.data?.oneBuildResponse?.description,
                    photo: response.data?.oneBuildResponse?.imagesUrls?.[0],
                    thumb_file_path:
                      response.data?.oneBuildResponse?.imagesUrls?.[0],
                    file_path: response.data?.oneBuildResponse?.imagesUrls?.[0],
                    cost_code_name: response?.data?.cost_code_name,
                    cost_code_id: response?.data?.cost_code_id,
                  });
                  setConfirmDialogOpen(false);
                  setCopyExistingItemID(undefined);
                  setSelectedData(undefined);
                  setRowNodeID(undefined);
                }
              } else {
                notification.error({
                  description: response.message,
                });
              }
            } else if (inUsesSelectedItems === "every_items_in_use") {
              setConfirmDialogOpen(false);
            } else if (copyExistingItemID) {
              setConfirmDialogOpen(false);
              setCopyExistingItemID(undefined);
              setSelectedData((prev) =>
                prev
                  ? {
                      ...prev,
                      name: "Copy of " + prev.name,
                    }
                  : prev
              );
              if (addItemsRef.current) {
                addItemsRef.current.formik.setFieldValue(
                  "name",
                  "Copy of " + addItemsRef.current.formik.values.name
                );
                addItemsRef.current.formik.submitForm();
              }
            } else {
              const itemIdsParams = getStatusChangeAndDeleteAPIIds(false);
              if (!Object.keys(itemIdsParams).length) {
                notification.error({
                  description: "Something went wrong!",
                });
                setConfirmDialogOpen(false);
                setCopyExistingItemID(undefined);
                return;
              }
              setLoading(true);
              try {
                const params = await getWebWorkerApiParams({
                  otherParams: {
                    ids: itemIdsParams,
                    check_in_use: 1, // 1 to check if item in use otherwise not required
                  },
                });

                const response = (await webWorkerApi({
                  url: cidbItemRoutes.delete_item,
                  method: "post",
                  data: params,
                })) as ICIDBDeleteAllItemsItemApiResponse;
                setConfirmDialogOpen(false);
                setCopyExistingItemID(undefined);
                if (response.success) {
                  setSelectedData(undefined);
                  refreshAgGrid();
                  setIsSelectedItemsActive(false);
                  setIsItemsSelected(false);
                } else {
                  notification.error({
                    description: response.message,
                  });
                }
              } catch (error) {
                setConfirmDialogOpen(false);
                setCopyExistingItemID(undefined);
                notification.error({
                  description:
                    (error as Error)?.message || "Something went wrong!",
                });
              }
            }
            setLoading(false);
          }}
          isNoLoading={copyExistingItemID ? fetchingCopyDataLoading : undefined}
          onAdditionButtonClick={() => {
            if (copyExistingItemID) {
              setConfirmDialogOpen(false);
              setAddEditSidebarOpen(false);
              setSelectedData(undefined);
              setSupplier(undefined);
              setIsCoping(false);
              setCopyExistingItemID(undefined);
            }
          }}
          onDecline={async () => {
            setConfirmDialogOpen(false);
            setCopyExistingItemID(undefined);
            setRowNodeID(undefined);
            if (copyExistingItemID) {
              setFetchingCopyDataLoading(true);
              // Fixed the Wrong Cost_CodeID being passed For ALL Items tab at view Copy time
              await fetchAndSetData({
                ...selectedData,
                id: copyExistingItemID,
              });
              setFetchingCopyDataLoading(false);
              setIsCoping(false);
            } else {
              setSelectedData(undefined);
            }
          }}
          onCloseModal={() => {
            setConfirmDialogOpen(false);
            setCopyExistingItemID(undefined);
            if (!copyExistingItemID) {
              setSelectedData(undefined);
              setRowNodeID(undefined);
            }
          }}
        />
      )}
      {addEditSidebarOpen && (
        <AddItems
          formType={selectedData && !isCoping ? "edit" : "add"}
          unitList={units}
          setUnitList={setUnits}
          addItemsOpen={addEditSidebarOpen}
          readOnly={
            (selectedData && !isCoping
              ? checkReadOnly(selectedData?.item_type_id || "")
              : module_access === "read_only") || supplierLoading
          }
          setAddItemsOpen={(open) => {
            setAddEditSidebarOpen(open);
            setSelectedData(undefined);
            setSupplier(undefined);
            setOperator(undefined);
            setSelectedItemType("");
            setIsCoping(false);
            if (isAnyFileRemoved) {
              refreshAgGrid();
              setIsAnyFileRemoved(false);
            }
          }}
          formInitialValues={formData}
          formValidationSchema={Yup.object().shape({
            name: Yup.string().trim().required("This field is required."),
            status: Yup.string().required("This field is required."),
            // Todo this https://app.clickup.com/t/86cz7686q
            // unit_cost:
            //   selectedItemType === "add_item_group" ||
            //   selectedItemType === "add_equipment"
            //     ? Yup.mixed().notRequired()
            //     : Yup.number()
            //         .typeError("Unit cost must be a number.")
            //         .required("This field is required.")
            //         .min(0.01, "Unit cost must be greater than zero."),
            // unit:
            //   selectedItemType === "add_item_group" ||
            //   selectedItemType === "add_equipment"
            //     ? Yup.mixed().notRequired()
            //     : Yup.string().required("This field is required."),

            // Todo this https://app.clickup.com/t/86czcyxyr
            // unit_cost: Yup.number()
            //   .typeError("Unit cost must be a number.")
            //   .min(0.01, "Unit cost must be greater than zero."),
            supplier_link: Yup.string()
              .optional()
              .matches(
                /^(https?:\/\/)?([\w-]+\.)+[\w-]+(\/[\w-./?%&=]*)?$/i,
                "Enter a valid URL"
              ),
          })}
          onRemoveFile={() => {
            if (selectedData) setIsAnyFileRemoved(true);
          }}
          formOnSubmit={async (values, formikHelpers) => {
            setFormLoading(true);
            try {
              let endpoint: string;
              let params: any = {};

              if (selectedItemType !== "add_item_group") {
                if (selectedData && !isCoping) {
                  isNavigationRef.current = false;
                  await generateDataObject(selectedData, values, formData);
                  await handleItemUpdate(
                    selectedData,
                    values,
                    formData,
                    isNavigationRef,
                    formikHelpers,
                    setAddEditSidebarOpen,
                    setSelectedData,
                    refreshAgGrid
                  );
                } else {
                  let attachImage = values.photos
                    .map((photo) => photo.image_id)
                    .filter(Boolean);
                  let awsFilesUrl = values.photos
                    .filter((photo) => !Boolean(photo.image_id))
                    .map((photo) => ({
                      file_ext: photo.file_ext,
                      file_name: photo.file_name,
                      file_path: photo.file_path,
                      file_url: photo.file_path,
                      is_image: photo.is_image,
                      image_id: photo.image_id,
                      isheic: photo?.file_ext.toLowerCase() === "heic" ? 1 : 0,
                    }));

                  const data = await getWebWorkerApiParams({
                    otherParams: {
                      costCodeKey: getKeyAddAllItems({
                        type: selectedItemType,
                      }),
                      name: HTMLEntities.encode(values.name),
                      unitCost: values.unit_cost?.toString()
                        ? (values.unit_cost * 100).toString()
                        : null,
                      unit: values.unit,
                      markup: values.markup || null,
                      costCodeId: values.cost_code,
                      hiddenMarkup: values.hidden_markup || null,
                      ...(values.notes ? { notes: values.notes } : {}),
                      ...(values.internal_notes
                        ? { internalNotes: values.internal_notes }
                        : {}),
                      ...(values.warranty_details
                        ? { warrantyDetails: values.warranty_details }
                        : {}),
                      ...(values.plate_number
                        ? {
                            plateNumber: values.plate_number,
                          }
                        : {}),
                      ...(values.registration_expire_on
                        ? {
                            registrationExpireOn: values.registration_expire_on,
                          }
                        : {}),
                      ...(values.insurance_carrier
                        ? {
                            insuranceCarrier: values.insurance_carrier,
                          }
                        : {}),
                      ...(values.policy_expire_on
                        ? {
                            policyExpireOn: values.policy_expire_on,
                          }
                        : {}),
                      ...(values.policy_number
                        ? {
                            policyNumber: values.policy_number,
                          }
                        : {}),
                      ...(values.purchase_date
                        ? {
                            purchaseDate: values.purchase_date,
                          }
                        : {}),
                      photo: values.product_image,
                      ...(attachImage.length ? { attachImage } : {}),
                      ...(awsFilesUrl.length ? { awsFilesUrl } : {}),
                      ...(values.sku ? { sku: values.sku } : {}),
                      status: values.status,
                      supplierId: values.supplier?.user_id,
                      ...(values.variations?.length
                        ? { itemVariations: values.variations.join(", ") }
                        : {}),
                      ...(values.supplier_link
                        ? { supplierLink: values.supplier_link }
                        : {}),
                      isFavorite: Number(values.is_favorite),
                      operatorId: values.operator?.user_id,
                    },
                  });
                  const response = (await webWorkerApi<IAddCostCodeDataApiRes>({
                    url: cidbItemRoutes.add_item,
                    method: "post",
                    data: data,
                  })) as IAddCostCodeDataApiRes;

                  if (response.success && !isNavigationRef.current) {
                    formikHelpers.resetForm();
                    setAddEditSidebarOpen(false);
                    setIsCoping(false);
                    setSupplier(undefined);
                    setOperator(undefined);
                    refreshAgGrid();
                    setSelectedData(undefined);
                  } else if (isCoping && response.data?.existing_record_id) {
                    setCopyExistingItemID(response.data.existing_record_id);
                    setConfirmDialogOpen(true);
                  } else {
                    notification.error({
                      description: response.message,
                    });
                  }
                }
              } else {
                if (selectedData && !isCoping) {
                  endpoint = cidbItemRoutes.GROUP.update(
                    selectedData.group_id.toString()
                  );
                  params = {
                    name: values.name,
                    status: values.status,
                    ...(values.internal_notes !== selectedData.internal_notes
                      ? { internal_notes: selectedData.internal_notes }
                      : {}),
                    items:
                      values.items?.map((item) => ({
                        item_type: item.item_type.toString(),
                        quantity: item.quantity,
                        reference_item_id: item.reference_item_id?.toString(),
                      })) || [],
                  };
                } else {
                  endpoint = cidbItemRoutes.add_group;
                  const data = await getWebWorkerApiParams({
                    otherParams: {
                      name: values.name,
                      status: values.status,
                      ...(values.internal_notes
                        ? { internal_notes: values.internal_notes }
                        : {}),
                      awsFilesUrl: values.photos.map((photo) => ({
                        file_ext: photo.file_ext,
                        file_name: photo.file_name,
                        file_path: photo.file_path,
                        file_url: photo.file_path,
                        is_image: photo.is_image,
                        image_id: photo.image_id,
                        isheic:
                          photo?.file_ext.toLowerCase() === "heic" ? 1 : 0,
                      })),
                      items:
                        values.items?.map((item) => ({
                          item_type: item.item_type.toString(),
                          quantity: item.quantity,
                          reference_item_id: item.reference_item_id?.toString(),
                        })) || [],
                    },
                  });
                  const response = (await webWorkerApi<IAddCostCodeDataApiRes>({
                    url: endpoint,
                    method: "post",
                    data,
                  })) as IAddCostCodeDataApiRes;

                  if (response.success) {
                    formikHelpers.resetForm();
                    setAddEditSidebarOpen(false);
                    if (selectedData) {
                      setSelectedData(undefined);
                    }
                  } else if (isCoping && response.data?.existing_record_id) {
                    setCopyExistingItemID(response.data.existing_record_id);
                    setConfirmDialogOpen(true);
                  } else {
                    notification.error({
                      description: response.message,
                    });
                  }
                }
              }
            } catch (error) {
              notification.error({
                description: (error as Error).message,
              });
            }

            setFormLoading(false);
            isNavigationRef.current = false;
          }}
          module={
            selectedItemType === "add_item_group"
              ? ({
                  module_id,
                  module_key,
                  original_module_name: "Item Group",
                  module_name: "Group item",
                  icon: CFConfig.group_icon,
                } as IModule)
              : allItemsModule
              ? {
                  ...allItemsModule,
                  module_name: "All items",
                  icon: selectedData
                    ? getItemTypeKeyIconModule({
                        type:
                          selectedData?.item_type?.toString().toLowerCase() ===
                          "sub contract"
                            ? "subcontractor"
                            : selectedData?.item_type
                                ?.toString()
                                .toLowerCase() || "",
                      })
                    : getAddAllItemsTypeIcon({
                        type:
                          selectedItemType?.toString().toLowerCase() ===
                          "sub contract"
                            ? "subcontractor"
                            : selectedItemType?.toString().toLowerCase() || "",
                      }),
                }
              : undefined
          }
          loading={formLoading}
          type={
            selectedData?.item_type_id?.toString()
              ? selectedData?.item_type_id?.toString() ===
                CFConfig.subcontractor_module_id?.toString()
                ? "sub_contractor"
                : selectedData?.item_type_id?.toString() ===
                  CFConfig.equipment_module_id?.toString()
                ? "equipment"
                : undefined
              : // selectedData?.item_type?.toString()?.toLowerCase() ||
                getAddAllItemsType({ type: selectedItemType })
          }
          onBuildId={!isCoping ? selectedData?.one_build_id : undefined}
          handleUpdateClick={handleUpdateClick}
          onNavigate={onNavigate}
          canNavigatePrevious={canNavigatePrevious}
          canNavigateNext={canNavigateNext}
          addItemsRef={addItemsRef}
          hasChanges={hasChanges}
          setHasChanges={setHasChanges}
          refreshAgGrid={refreshAgGrid}
        />
      )}
      {confirmCopyDialogOpen && (
        <ConfirmModal
          isOpen={confirmCopyDialogOpen}
          modaltitle={_t("Copy Item")}
          description={_t(
            "Are you sure you want to create a Copy of this Item?"
          )}
          modalIcon="fa-regular fa-copy"
          onAccept={async () => {
            setConfirmCopyDialogOpen(false);
            if (selectedData) {
              setIsCoping(true);
              setSelectedData({
                ...selectedData,
                name: "Copy of " + selectedData.name,
                supplier_link: "",
                internal_notes: "",
              });

              switch (selectedData.item_type_id?.toString()) {
                case CFConfig.material_module_id?.toString():
                  setSelectedItemType(ADD_ITEMS_OPTION_KEYS.material);
                  break;
                case CFConfig.equipment_module_id?.toString():
                  setSelectedItemType(ADD_ITEMS_OPTION_KEYS.equipment);
                  break;
                case CFConfig.labor_module_id?.toString():
                  setSelectedItemType(ADD_ITEMS_OPTION_KEYS.labor);
                  break;
                case CFConfig.subcontractor_module_id?.toString():
                  setSelectedItemType(ADD_ITEMS_OPTION_KEYS.subcontractor);
                  break;
              }

              setAddEditSidebarOpen(true);
              fetchAndSetData(selectedData, "new");
            }
          }}
          onDecline={() => {
            setConfirmCopyDialogOpen(false);
            setSelectedData(undefined);
          }}
          onCloseModal={() => {
            setConfirmCopyDialogOpen(false);
            setSelectedData(undefined);
          }}
        />
      )}
      {newUnitName.trim() && window.ENV.ENABLE_UNIT_DROPDOWN && (
        <ConfirmModal
          isOpen={Boolean(newUnitName.trim())}
          modalIcon="fa-regular fa-clipboard-list-check"
          modaltitle={_t("Add Unit To List")}
          description={_t(
            `This will add "${newUnitName}" to the list. Do you want to add it?`
          )}
          isLoading={isAddingCustomData}
          onCloseModal={() => {
            setNewUnitName("");
            setSelectedData(undefined);
          }}
          onAccept={async () => {
            if (!isAddingCustomData && newUnitName) {
              setIsAddingCustomData(true);
              const response = (await addUnit({
                name: newUnitName,
              })) as IUnitAddResponse & {
                data: {
                  data: IUnitListResponseDataAndStatusCode["units"][0];
                };
              };
              if (response.success) {
                const newUnits = [response.data.data, ...units];
                setUnits(newUnits);
                setNewUnitName("");
                setSelectedData(undefined);
                const api = gridRef.current?.api;
                if (!api) return;

                const renderedNodes = api.getRenderedNodes();

                if (renderedNodes) {
                  const currentRowNode = renderedNodes.find(
                    (node) => node.data?.id === selectedData?.id
                  );
                  if (currentRowNode && currentRowNode.data) {
                    const oldData = { ...currentRowNode.data };
                    currentRowNode.data.unit = newUnitName;
                    currentRowNode?.setData(currentRowNode.data);
                    const response = await updateValue({
                      item_id: currentRowNode.data.id,
                      ...getCIDBValidationFieldsUpdate(
                        oldData,
                        HTMLEntities.encode(newUnitName),
                        "unit"
                      ),
                      cost_code_key: getItemTypeKey({
                        type: getNormalizedItemType(
                          currentRowNode.data?.item_type
                        ),
                      }),
                    });
                    if (!response.success) {
                      currentRowNode?.setData(oldData);
                      notification.error({ description: response.message });
                    }
                  }
                }
                const existingColDefs = api.getColumnDefs();
                if (!existingColDefs) return;

                const updatedColDefs = existingColDefs.map((col) =>
                  "field" in col && col.field === "unit"
                    ? {
                        ...col,
                        filterParams: {
                          values:
                            newUnits.map((unit) => ({
                              label: unit.name?.toString(),
                              value: unit.name?.toString(),
                            })) ?? [],
                        },
                        cellEditorParams: {
                          ...col.cellEditorParams,
                          values: newUnits,
                        },
                      }
                    : col
                );

                api.setColumnDefs(updatedColDefs);

                // Ensure the grid header re-renders
                api.refreshHeader();
              } else {
                notification.error({
                  description: response.message,
                });
              }
              setIsAddingCustomData(false);
            }
          }}
          onDecline={() => {
            setNewUnitName("");
            setSelectedData(undefined);
          }}
        />
      )}
    </>
  );
}

export default AllItems;

export { ErrorBoundary };
