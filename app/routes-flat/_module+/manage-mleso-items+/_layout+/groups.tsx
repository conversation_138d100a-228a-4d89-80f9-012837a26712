import { useEffect, useMemo, useRef, useState } from "react";
// Atoms
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Typography } from "~/shared/components/atoms/typography";
import { Tag } from "~/shared/components/atoms/tag";

// molecules
import { DropdownMenu } from "~/shared/components/molecules/dropdownMenu";
import { ErrorBoundary } from "~/shared/components/molecules/errorBoundary/ErrorBoundary";
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { ConfirmModal } from "~/shared/components/molecules/confirmModal";
import { AddButton } from "~/shared/components/molecules/addButton";
import { DynamicTable } from "~/shared/components/molecules/dynamicTable";
import { AvatarProfile } from "~/shared/components/molecules/avatarProfile";
import { DateTimeCard } from "~/shared/components/molecules/dateTimeCard";
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";
import { TableRowDetailLoading } from "~/shared/components/molecules/tableRowDetailLoading";

// Organisms
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";

// Shared
import { MIDashboardRegularIconAdd } from "~/shared/utils/helper/fontAwesome/libraryAdd/module/mleso-items/regular";
import useTableGridData from "~/shared/hooks/useTableGridData";
import {
  getWebWorkerApiParams,
  webWorkerApi,
} from "~/shared/services/apiService/web-worker";
import { getItemCompanyTypeKeyIcon } from "~/shared/utils/helper/common";
import { getCDNOrigin } from "~/shared/utils/helper/cdnHelper";

// Zustand
import { getGlobalUser } from "~/zustand/global/user/slice";
import { getCurrentMenuModule } from "~/zustand/global/config/slice";

// Lodash
import debounce from "lodash/debounce";

// Formik
import * as Yup from "yup";

// Other
import DetailsTopBar from "~/modules/settings/costItemsDatabase/components/DetailsTopBar";
import {
  ITEMS_OPTIONS,
  MLESOITEMS_SELECT_OPTION_BY_KEYS,
  MLESOITEMS_SELECTOPTION_TAB_KEYS,
} from "~/modules/settings/costItemsDatabase/utils/constants";
import {
  ColDef,
  ColumnMovedEvent,
  GetDetailRowDataParams,
  GridReadyEvent,
  SortChangedEvent,
  IDetailCellRendererParams,
  ICellRendererParams,
  IRowNode,
} from "ag-grid-community";
import { cidbItemRoutes } from "~/route-services/cidb-item.routes";
import { Number, sanitizeString } from "~/helpers/helper";
import { useTranslation } from "~/hook";
import { AddItems } from "~/modules/settings/costItemsDatabase/components/sidebar/addItems";
import { apiRoutes } from "~/route-services/routes";
import {
  changeFloatingFilterDisabled,
  fetchCIDBItemsData,
  getAGGridValidCellClick,
  onSingleClick,
} from "~/modules/settings/costItemsDatabase/utils/helper";
import { getCommonSidebarCollapse, getGConfig } from "~/zustand";
import { deleteFiles } from "~/modules/document/fileAndPhoto/redux/action/filePhotoRightAction";
import { useOutletContext } from "@remix-run/react";
import { FormikProps } from "formik";

// Fort Awesome Library Add icons
MIDashboardRegularIconAdd();

function SubcontractorItems() {
  const { _t } = useTranslation();
  const sidebarCollapse: boolean | undefined = getCommonSidebarCollapse();
  const { currentOption } = useOutletContext<{
    options: ICIDBTabOption[];
    currentOption: ICIDBTabOption;
  }>();
  const currentModule: IInitialGlobalData["config"]["current_module"] =
    getCurrentMenuModule();
  const { module_id = 0, module_key = "", module_access } = currentModule || {};

  const { formatter } = useCurrencyFormatter();
  const gConfig: GConfig = getGConfig();
  const user: IInitialGlobalData["user"] = getGlobalUser();
  const { allow_delete_module_items = "0" } = user || {};
  const { datasource, gridRowParams } = useTableGridData();
  const searchParams = new URLSearchParams(window.location.search);
  const groupId = searchParams.get("groupId");
  const gridRef = useRef<ExtendedAgGridReact | null>(null);
  const isInitialized = useRef<boolean>(false);
  const [AGFilterParams, setAGFilterParams] = useState<{
    search: ISendCidbActionProps["search"];
    status: ISendCidbActionProps["status"];
  }>({ search: "", status: 0 });
  const [copyExistingItemID, setCopyExistingItemID] = useState<number>();
  const [selectedData, setSelectedData] = useState<
    ICIDBGetGroupListApiResponseData | ICIDBGetGroupDetailApiResponse["data"]
  >();
  const [selectedSubData, setSelectedSubData] =
    useState<ICIDBGetGroupDetailApiResponse["data"]["item"][0]>();
  const [confirmDialogOpen, setConfirmDialogOpen] = useState<boolean>(false);
  const [confirmArchiveDialogOpen, setConfirmArchiveDialogOpen] =
    useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [fetchingCopyDataLoading, setFetchingCopyDataLoading] =
    useState<boolean>(false);
  const [formLoading, setFormLoading] = useState<boolean>(false);
  const [addEditSidebarOpen, setAddEditSidebarOpen] = useState<boolean>(false);
  const [isCoping, setIsCoping] = useState<boolean>(false);
  const [isAnyFileRemoved, setIsAnyFileRemoved] = useState<boolean>(false);
  const [fetchingFormItemCount, setFetchingFormItemCount] = useState<number>(0);
  const addItemsRef = useRef<{
    formik: FormikProps<IAddItemsProps["formInitialValues"]>;
  }>(null);
  const [confirmCopyDialogOpen, setConfirmCopyDialogOpen] =
    useState<boolean>(false);

  const refreshAgGrid = () => {
    const gridParams = gridRowParams?.gridParams;
    if (gridParams) {
      gridParams.api.setServerSideDatasource({ getRows: () => {} });
      gridParams.api.setServerSideDatasource(datasource);
    }
  };

  useEffect(() => {
    (async function () {
      await fetchCIDBItemsData({
        moduleKey: CFConfig.group_item_company_type_key,
        filterParams: AGFilterParams,
        gridRowParams,
      });
      if (gridRowParams?.gridParams) {
        changeFloatingFilterDisabled(gridRowParams?.gridParams, false);
      }
    })();
    return () => {};
  }, [gridRowParams]);

  useEffect(() => {
    if (gridRef && gridRef.current && isInitialized.current) {
      refreshAgGrid();
    } else {
      isInitialized.current = true;
    }
  }, [AGFilterParams]);

  const onGridReady = (gridParams: GridReadyEvent) => {
    changeFloatingFilterDisabled(gridParams, true);
    gridParams?.api?.setServerSideDatasource(datasource);
    // Delay redrawRows until data is rendered
    gridParams.api.addEventListener("firstDataRendered", () => {
      gridParams.api.redrawRows();
    });
  };

  const onSortChanged = async (params: SortChangedEvent) => {
    params.api.setServerSideDatasource(datasource);
  };

  const onSearchChange = debounce((e: React.ChangeEvent<HTMLInputElement>) => {
    setAGFilterParams((prev) => ({
      ...prev,
      search: e.target.value?.trim(),
    }));
  }, 300);

  const updateValue = async (form: any, groupId: string) => {
    try {
      return (await webWorkerApi({
        url: cidbItemRoutes.GROUP.update(groupId),
        method: "post",
        data: form,
      })) as IApiCallResponse;
    } catch (error) {
      return {
        success: false,
        message: (error as Error).message,
      } as IApiCallResponse;
    }
  };

  const getGroupDetail = async (groupId: string) => {
    return (await webWorkerApi({
      url: cidbItemRoutes.detail(groupId),
      method: "post",
      data: {
        is_module_call: 1,
        status: AGFilterParams?.status,
      },
    })) as ICIDBGetGroupDetailApiResponse;
  };

  const updateSidebarOpen = (
    data:
      | (ICIDBGetGroupListApiResponseData &
          ICIDBGetGroupDetailApiResponse["data"])
      | undefined,
    node: IRowNode<
      ICIDBGetGroupListApiResponseData & ICIDBGetGroupDetailApiResponse["data"]
    >,
    action?: "new"
  ) => {
    const { item, group_id, item_cnt } = data || {};
    if (item) {
      setAddEditSidebarOpen(true);
      if (action === "new") {
        setSelectedData((prev) =>
          prev
            ? {
                ...prev,
                ...data,
                name: prev.name,
                notes: prev?.notes || "",
                internal_notes: prev?.internal_notes || "",
              }
            : data
        );
      } else {
        setSelectedData(data);
      }
    } else {
      if (Number(item_cnt) !== 0) {
        setFetchingFormItemCount(Number(item_cnt));
        setAddEditSidebarOpen(true);
        (async function () {
          const response = await getGroupDetail(group_id?.toString() || "");
          if (response?.success) {
            const updatedData = {
              ...(response.data || {}),
              ...(data || {}),
              grp_internal_notes: data?.internal_notes || null,
              added_by_icon: data?.added_by_icon || null,
              aws_files: data?.aws_files || [],
            };
            node.setData(updatedData);

            if (action === "new") {
              setSelectedData((prev) => ({
                ...updatedData,
                name: prev?.name || updatedData.name,
                notes: prev?.notes || "",
                internal_notes: prev?.internal_notes || "",
              }));
            } else {
              setSelectedData(updatedData);
            }
          } else {
            setSelectedData((prev) => (prev ? { ...prev, item: [] } : prev));
          }
          setFetchingFormItemCount(0);
        })();
      } else {
        setAddEditSidebarOpen(true);
        if (action === "new") {
          setSelectedData((prev) =>
            prev
              ? {
                  ...prev,
                  ...data,
                  name: prev.name,
                  notes: prev?.notes || "",
                  internal_notes: prev?.internal_notes || "",
                }
              : data
          );
        } else {
          setSelectedData(data);
        }
      }
    }
  };

  // NEW UI
  const columnDefs: ColDef<ICIDBGetGroupListApiResponseData>[] = useMemo(
    () => [
      {
        headerName: "",
        field: "" as keyof ICIDBGetGroupListApiResponseData,
        cellRenderer: "agGroupCellRenderer",
        suppressMenu: true,
        cellClass: "no-space-td expand-icon cursor-auto",
        maxWidth: 40,
        minWidth: 40,
        sortable: false,
        lockPosition: "left",
        suppressColumnsToolPanel: true,
        pinned: null,
        lockPinned: true,
      },
      {
        headerName: _t("Name"),
        field: "name",
        minWidth: 250,
        flex: 2,
        resizable: true,
        suppressMovable: false,
        suppressMenu: true,
        sortable: true,
        cellClass: "ag-cell-left cursor-pointer",
        headerClass: "ag-header-left",
        editable: module_access === "full_access",
        valueGetter: ({ data }) =>
          HTMLEntities.decode(sanitizeString(data?.name)),
        valueSetter: ({ data, newValue, node }) => {
          (async function () {
            if (!newValue) {
              notification.error({ description: "Name field is required." });
              return;
            }
            if (data.name !== newValue) {
              const oldData = { ...data };
              data.name = newValue;
              const response = await updateValue(
                {
                  name: HTMLEntities.encode(newValue),
                },
                data.group_id?.toString()
              );
              if (!response.success) {
                node?.setData(oldData);
                notification.error({ description: response.message });
              }
            }
          })();
          return false;
        },
        cellRenderer: ({
          value,
        }: {
          value: ICIDBGetGroupListApiResponseData["name"];
          data: ICIDBGetGroupListApiResponseData;
        }) => {
          return (
            <Tooltip title={value}>
              <Typography className="table-tooltip-text text-center">
                {value}
              </Typography>
            </Tooltip>
          );
        },
      },
      {
        headerName: "# " + _t("Items"),
        field: "item_cnt",
        minWidth: 250,
        maxWidth: 250,
        suppressMovable: false,
        suppressMenu: true,
        sortable: true,
        cellClass: "ag-cell-right cursor-pointer",
        headerClass: "ag-header-right",
        cellRenderer: ({
          value,
        }: {
          value: ICIDBGetGroupListApiResponseData["name"];
          data: ICIDBGetGroupListApiResponseData;
        }) => {
          return (
            <Tooltip title={value}>
              <Typography className="table-tooltip-text text-end">
                {value}
              </Typography>
            </Tooltip>
          );
        },
      },
      {
        headerName: _t("Added By"),
        field: "added_by",
        sortable: true,
        maxWidth: 270,
        minWidth: 270,
        suppressMovable: false,
        suppressMenu: true,
        headerClass: "ag-header-center",
        cellClass: "ag-cell-center cursor-pointer",
        cellRenderer: ({
          value,
          data,
        }: {
          value: ICIDBGetGroupListApiResponseData["added_by"];
          data: ICIDBGetGroupListApiResponseData;
        }) => {
          const name = HTMLEntities.decode(sanitizeString(value));

          const CDN_ORIGIN = getCDNOrigin(
            typeof window !== "undefined" ? window.location.host : ""
          );
          return (
            <Tooltip title={name}>
              <div className="w-fit mx-auto">
                <AvatarProfile
                  user={{
                    name,
                    image:
                      data.added_by_icon ||
                      CDN_ORIGIN + "/assets/images/user-default-avatar.svg",
                  }}
                  className="m-auto"
                  iconClassName="text-[11px] font-semibold"
                />
              </div>
            </Tooltip>
          );
        },
      },
      {
        headerName: _t("Date Added"),
        field: "date_added",
        minWidth: 170,
        maxWidth: 170,
        flex: 2,
        suppressMovable: false,
        suppressMenu: true,
        sortable: true,
        cellClass: "ag-cell-left cursor-pointer",
        headerClass: "ag-header-left default-sort",
        cellRenderer: ({
          value,
        }: {
          value: ICIDBGetGroupListApiResponseData["date_added"];
          data: ICIDBGetGroupListApiResponseData;
        }) => {
          return (
            <div>
              <DateTimeCard format="date" date={value} />
            </div>
          );
        },
      },
      {
        headerName: _t("Status"),
        field: "status_name",
        sortable: true,
        maxWidth: 130,
        minWidth: 130,
        suppressMenu: true,
        suppressMovable: false,
        pinned: null,
        hide: true,
        lockPinned: true,
        headerClass: "ag-header-center",
        cellClass: "ag-cell-center",
        cellEditor: "agRichSelectCellEditor",
        cellRenderer: ({
          value,
        }: ICellRendererParams<
          ICIDBGetAllItemsListApiResponseData,
          ICIDBGetAllItemsListApiResponseData["status_name"]
        >) => {
          return (
            <Tooltip title={value}>
              <div className="text-center">
                <Tag
                  color={
                    value === "Active"
                      ? "#00800026"
                      : value === "Archived"
                      ? "#FF000026"
                      : "#22355826"
                  }
                  className={`mx-auto text-13 type-badge common-tag ${
                    value === "Active"
                      ? "!text-[#008000]"
                      : value === "Archived"
                      ? "!text-[#FF0000]"
                      : "!text-[#223558]"
                  }`}
                >
                  {value}
                </Tag>
              </div>
            </Tooltip>
          );
        },
      },
      {
        headerName: "",
        field: "" as keyof ICIDBGetGroupListApiResponseData,
        minWidth: 90,
        maxWidth: 90,
        suppressMenu: true,
        sortable: false,
        lockPosition: "right",
        cellClass: "cell-click-event-none cursor-auto",
        suppressColumnsToolPanel: true,
        pinned: null,
        lockPinned: true,
        cellRenderer: ({
          data,
          node,
        }: ICellRendererParams<
          ICIDBGetGroupListApiResponseData &
            ICIDBGetGroupDetailApiResponse["data"]
        >) => {
          const { is_deleted, internal_notes, aws_files } = data || {};
          let options = [
            {
              label: "Copy This Group",
              icon: "fa-regular fa-copy",
              key: "copy",
            },
            ...ITEMS_OPTIONS.map((option) => ({
              ...option,
              content:
                typeof option.content === "function"
                  ? option.content("group")
                  : option.content?.replace("item", "group"),
            })),
          ];

          const isDeleted = data?.is_deleted?.toString() !== "1";
          options = options.filter((option) =>
            isDeleted ? option.key !== "active" : option.key !== "archive"
          );
          options = options.map((option) => ({
            ...option,
            label: option.label?.replace("item", "group"),
            disabled: module_access === "read_only" && option.key !== "preview",
            onClick: () => {
              if (option.key === "copy" && data) {
                setSelectedData(data);
                setConfirmCopyDialogOpen(true);
              } else {
                setSelectedData(data);
                if (option.key === "delete") {
                  setConfirmDialogOpen(true);
                } else if (
                  option.key === "active" ||
                  option.key === "archive"
                ) {
                  setConfirmArchiveDialogOpen(true);
                } else if (option.key === "preview") {
                  setSelectedData(data);
                  updateSidebarOpen(data, node);
                }
              }
            },
          }));

          return (
            <div className="flex items-center gap-0.5 justify-end">
              <div className="w-6 h-6 flex items-center justify-center">
                {internal_notes && (
                  <Tooltip
                    title={internal_notes}
                    rootClassName="lg:!max-w-[calc(50%-100px)] !max-w-[calc(100%-200px)]"
                  >
                    <FontAwesomeIcon
                      className="text-base w-4 h-4 text-primary-900/80"
                      icon="fa-regular fa-memo"
                    />
                  </Tooltip>
                )}
              </div>
              <div className="w-6 h-6 flex items-center justify-center">
                {Boolean(aws_files?.length) && (
                  <FontAwesomeIcon
                    className="text-base w-4 h-4 text-primary-900/80"
                    icon="fa-regular fa-paperclip"
                  />
                )}
              </div>
              <DropdownMenu
                options={options}
                icon="fa-regular fa-ellipsis-vertical"
                iconClassName="text-primary-900/80 group-hover/buttonHover:text-primary-900"
                buttonClass="m-0 hover:!bg-[#0000000f]"
              />
            </div>
          );
        },
      },
    ],
    [allow_delete_module_items]
  );

  const detailCellRendererParams: Partial<IDetailCellRendererParams> = useMemo(
    () => ({
      detailGridOptions: {
        suppressDragLeaveHidesColumns: true,
        columnDefs: [
          {
            headerName: _t("Type"),
            field: "item_type_key",
            sortable: false,
            // minwidth: 60,
            maxWidth: 60,
            suppressMovable: false,
            suppressMenu: true,
            headerClass: "ag-header-center",
            cellClass: "ag-center-aligned-cell cursor-auto",
            cellRenderer: ({
              value,
              data,
            }: {
              value: ICIDBGetGroupDetailApiResponse["data"]["item"][0]["item_type_key"];
              data: ICIDBGetGroupDetailApiResponse["data"]["item"][0];
            }) => {
              return (
                <Tooltip title={data.item_type_name}>
                  <FontAwesomeIcon
                    className="w-4 h-4 text-primary-900 mx-auto"
                    icon={getItemCompanyTypeKeyIcon({
                      type: value?.toString()?.toLowerCase(),
                    })}
                  />
                </Tooltip>
              );
            },
          },
          {
            headerName: _t("Item Name"),
            field: "subject",
            minWidth: 120,
            flex: 2,
            suppressMenu: true,
            resizable: true,
            suppressMovable: false,
            headerClass: "ag-header-left",
            cellClass: "ag-cell-left cursor-auto",
            cellRenderer: ({
              value,
            }: {
              value: ICIDBGetGroupDetailApiResponse["data"]["item"][0]["subject"];
              data: ICIDBGetGroupDetailApiResponse["data"]["item"][0];
            }) => {
              const name = HTMLEntities.decode(sanitizeString(value));
              return (
                <Tooltip title={name}>
                  <Typography className="table-tooltip-text text-center">
                    {name}
                  </Typography>
                </Tooltip>
              );
            },
          },
          {
            headerName: _t("Qty"),
            field: "quantity",
            suppressMovable: false,
            suppressMenu: true,
            sortable: false,
            minWidth: 150,
            maxWidth: 150,
            cellClass: "ag-cell-right cursor-auto",
            headerClass: "ag-header-right",
            cellRenderer: ({
              value,
            }: {
              value: ICIDBGetGroupDetailApiResponse["data"]["item"][0]["quantity"];
              data: ICIDBGetGroupDetailApiResponse["data"]["item"][0];
            }) => {
              return value?.toString() ? (
                <Tooltip title={value || 0}>
                  <Typography className="table-tooltip-text">
                    {value || 0}
                  </Typography>
                </Tooltip>
              ) : (
                "-"
              );
            },
          },
          {
            headerName: _t("Unit Cost"),
            field: "unit_cost",
            suppressMovable: false,
            suppressMenu: true,
            sortable: false,
            minWidth: 200,
            maxWidth: 200,
            cellClass: "ag-cell-right cursor-auto",
            headerClass: "ag-header-right",
            cellRenderer: ({
              value,
            }: {
              value: ICIDBGetGroupDetailApiResponse["data"]["item"][0]["unit_cost"];
              data: ICIDBGetGroupDetailApiResponse["data"]["item"][0];
            }) => {
              const unitCost = Number(value || "0")
                ? formatter((Number(value || "0") / 100).toFixed(2))
                    .value_with_symbol
                : "";
              return (
                <Tooltip title={unitCost}>
                  <Typography className="table-tooltip-text">
                    {unitCost || "-"}
                  </Typography>
                </Tooltip>
              );
            },
          },
          {
            headerName: _t("Unit"),
            field: "unit",
            minWidth: 100,
            maxWidth: 100,
            suppressMenu: true,
            suppressMovable: false,
            headerClass: "ag-header-left",
            cellClass: "ag-cell-left cursor-auto",
            cellRenderer: ({
              value,
            }: {
              value: ICIDBGetGroupDetailApiResponse["data"]["item"][0]["unit"];
              data: ICIDBGetGroupDetailApiResponse["data"]["item"][0];
            }) => {
              return (
                <Tooltip title={value}>
                  <Typography className="table-tooltip-text text-center">
                    {value || "-"}
                  </Typography>
                </Tooltip>
              );
            },
          },
          {
            headerName: _t("Total"),
            field:
              "" as keyof ICIDBGetGroupDetailApiResponse["data"]["item"][0],
            minWidth: 150,
            maxWidth: 150,
            suppressMenu: true,
            suppressMovable: false,
            cellClass: "ag-cell-right cursor-auto",
            headerClass: "ag-header-right",
            cellRenderer: ({
              data,
            }: {
              data: ICIDBGetGroupDetailApiResponse["data"]["item"][0];
            }) => {
              const unitCost = Number(data.unit_cost || 0) / 100;
              const quantity = Number(data.quantity || 0);
              const total = unitCost * quantity;

              const shouldShowTotal = unitCost > 0 && quantity > 0;

              return shouldShowTotal ? (
                <Tooltip title={formatter(total.toFixed(2)).value_with_symbol}>
                  <Typography className="table-tooltip-text text-center">
                    {formatter(total.toFixed(2)).value_with_symbol}
                  </Typography>
                </Tooltip>
              ) : (
                "-"
              );
            },
          },
          {
            headerName: _t("Status"),
            field: "status_name",
            maxWidth: 130,
            minWidth: 130,
            suppressMenu: true,
            resizable: true,
            hide: AGFilterParams?.status != 2,
            suppressMovable: false,
            headerClass: "ag-header-center",
            // cellClass: "cursor-auto",
            cellRenderer: ({
              data,
            }: {
              data: ICIDBGetGroupDetailApiResponse["data"]["item"][0];
            }) => {
              return (
                <Tooltip title={data?.status_name}>
                  <div className="text-center">
                    <Tag
                      color={
                        data?.status_name === "Active"
                          ? "#00800026"
                          : data?.status_name === "Archived"
                          ? "#FF000026"
                          : "#22355826"
                      }
                      className={`mx-auto text-13 type-badge common-tag ${
                        data?.status_name === "Active"
                          ? "!text-[#008000]"
                          : data?.status_name === "Archived"
                          ? "!text-[#FF0000]"
                          : "!text-[#223558]"
                      }`}
                    >
                      {data?.status_name}
                    </Tag>
                  </div>
                </Tooltip>
              );
            },
          },
          {
            headerName: "",
            field: "",
            width: 50,
            maxWidth: 50,
            minWidth: 50,
            suppressMenu: true,
            cellClass: "ag-cell-center cursor-auto",
            headerClass: "ag-header-center",
            cellRenderer: ({
              data,
            }: {
              data: ICIDBGetGroupDetailApiResponse["data"]["item"][0];
            }) => {
              if (allow_delete_module_items !== "0") {
                return (
                  <ButtonWithTooltip
                    tooltipTitle={_t("Delete")}
                    tooltipPlacement="top"
                    icon="fa-regular fa-trash-can"
                    onClick={() => {
                      setSelectedSubData(data);
                      setConfirmDialogOpen(true);
                    }}
                  />
                );
              }
              return null;
            },
          },
        ],
        noRowsOverlayComponent: () => (
          <NoRecords
            image={`${window.ENV.CDN_URL}assets/images/no-records-imports.svg`}
          />
        ),
        defaultColDef: { flex: 1 },
        loadingOverlayComponent: TableRowDetailLoading,
        suppressContextMenu: true,
      },
      getDetailRowData: async ({
        data,
        successCallback,
        node,
      }: GetDetailRowDataParams<
        ICIDBGetGroupListApiResponseData &
          ICIDBGetGroupDetailApiResponse["data"],
        ICIDBGetGroupDetailApiResponse["data"]["item"][0]
      >) => {
        try {
          const groupId = Number(data.group_id);
          if (!groupId || Number(data.item_cnt) === 0) {
            successCallback([]);
            return;
          }

          if (data.item) {
            successCallback(data.item);
            return;
          }
          const response = await getGroupDetail(groupId.toString());

          if (response.success) {
            const newData = {
              ...response.data,
              ...data,
            };
            node.setData(newData);
            successCallback(response.data.item || []);
            if (node.parent?.id) {
              const rowNode = gridRef.current?.api.getRowNode(node.parent?.id);
              if (rowNode) {
                rowNode.setData(newData);
                const rowCount = (response.data.item || []).length;
                node.setRowHeight(rowCount ? rowCount * 42 + 64 : 210);
                rowNode.setExpanded(false);
                rowNode.setExpanded(true);
              }
            }
          } else {
            successCallback([]);
          }
        } catch (error) {
          console.error("Error fetching import details:", error);
          successCallback([]);
        }
      },
    }),
    [allow_delete_module_items, AGFilterParams?.status, formatter]
  );

  const formData: IAddItemsProps["formInitialValues"] = useMemo(() => {
    if (selectedData) {
      return {
        name: HTMLEntities.decode(sanitizeString(selectedData.name)) || "",
        status: selectedData.is_deleted.toString() || "",
        notes: selectedData.internal_notes || "",
        internal_notes: selectedData.internal_notes || "",
        photos:
          "aws_files" in selectedData
            ? selectedData.aws_files?.map((awsFile) => ({
                ...awsFile,
                file_tags: awsFile.file_tags || undefined,
                file_url: awsFile.file_path,
                attach_item_id: awsFile.child_item_id,
                original_pdf_url: awsFile.original_file_path || "",
                projectId: awsFile.project_id || 0,
                project_id: awsFile.project_id || 0,
                name: awsFile.file_name,
                fileName: awsFile.file_name,
                annotation_data: awsFile.annotation_data || "",
                original_file_path: awsFile.original_file_path || "",
                image: awsFile.image || "",
                quickbook_attachable_id: Number(
                  awsFile.quickbook_attachable_id
                ),
                notes: awsFile.notes || "",
                project_name: awsFile.project_name || "",
                date_added: awsFile.date_added || "",
                id: awsFile.id || 0,
              })) || []
            : [],
        items:
          (selectedData as ICIDBGetGroupDetailApiResponse["data"]).item || [],
      };
    }
    return {
      name: "",
      status: "0",
      notes: "",
      photos: [],
      items: [],
    };
  }, [selectedData]);

  const onColumnMoved = (event: ColumnMovedEvent) => {
    const { columnApi, api } = event;
    const columnState = columnApi.getColumnState();
    colChange.cancel();
    colChange(columnState.map((col) => api.getColumnDef(col.colId) || {}));
  };

  useEffect(() => {
    const fetchEquipmentDetails = async () => {
      if (groupId) {
        try {
          const data = await getWebWorkerApiParams({
            otherParams: {
              record_id: CFConfig.group_id,
              item_id: groupId,
              moduleId: gConfig?.module_id,
              module_key: CFConfig.mleso_items_module,
            },
          });
          const [response, getCommonAttachmentsResponse] = await Promise.all([
            getGroupDetail(groupId.toString()),
            webWorkerApi({
              url: apiRoutes.COMMON.get_common_attachments,
              method: "post",
              data: data,
            }) as Promise<ICommonAttachmentRes>,
          ]);

          if (response.success) {
            setSelectedData((prev) =>
              response.data
                ? {
                    ...response.data,
                    aws_files:
                      (getCommonAttachmentsResponse.data
                        .aws_files as unknown as IAwsFile[]) || [],
                  }
                : prev
            );
            setAddEditSidebarOpen(true);
          }
        } catch (error) {
          console.error("Failed to fetch equipment details:", error);
        }
      }
    };
    fetchEquipmentDetails();
  }, [groupId]);

  const colChange = debounce((cols: ColDef[]) => {
    if (gridRef.current) {
      gridRef.current.api.setColumnDefs(cols);
    }
  }, 400);
  return (
    <>
      <DetailsTopBar
        sidebarCollapse={sidebarCollapse}
        refreshAgGrid={refreshAgGrid}
        searchProps={{
          onSearch: (value) => {
            setAGFilterParams((prev) => ({
              ...prev,
              search: value?.trim(),
            }));
          },
          onChange: (e) => {
            onSearchChange.cancel();
            onSearchChange(e);
          },
        }}
        statusSelectProps={{
          value: AGFilterParams.status?.toString() || "0",
          onChange: (value) => {
            if (typeof value === "string") {
              if (gridRef.current) {
                const allColDefs = gridRef.current.api.getColumnDefs(); // This gives current ColDef[]
                if (allColDefs?.length) {
                  const newColDefs = allColDefs.map((colDef: any) => {
                    if (colDef?.field === "status_name") {
                      return {
                        ...colDef,
                        hide: value?.toString() !== "2",
                      };
                    }
                    return colDef;
                  });
                  colChange.cancel();
                  colChange(newColDefs);
                }
              }

              setAGFilterParams((prev) => ({
                ...prev,
                status: Number(value),
              }));
            }
          },
        }}
        otherOptionsDropdownProps={{
          options: [MLESOITEMS_SELECT_OPTION_BY_KEYS.cost_codes_items],
        }}
        addNewComponent={
          module_access === "full_access" && (
            <AddButton
              onClick={() => setAddEditSidebarOpen(true)}
              className="font-medium"
            >
              {_t("Item Group")}
            </AddButton>
          )
        }
        currentTab={MLESOITEMS_SELECTOPTION_TAB_KEYS.groups}
        readOnly={module_access !== "full_access"}
      />
      <div className="pt-2">
        <div
          className={`list-view-table ag-grid-cell-pointer ant-collapse ag-theme-alpine ${
            module_access !== "full_access" ||
            currentOption?.access === "read_only"
              ? "h-[calc(100dvh-278px)]"
              : "h-[calc(100dvh-238px)]"
          }`}
        >
          <DynamicTable
            ref={gridRef}
            onGridReady={onGridReady}
            onSortChanged={onSortChanged}
            columnDefs={columnDefs}
            className="multi-list-table static-multi-table static-table group-item-table"
            masterDetail={true}
            onCellClicked={(event) => {
              onSingleClick(() => {
                const uneditableCellClick = getAGGridValidCellClick(event);
                if (uneditableCellClick && event) {
                  setSelectedData(event.data);
                  updateSidebarOpen(event.data, event.node);
                }
              });
            }}
            onColumnMoved={onColumnMoved}
            // detailRowAutoHeight={true}
            detailCellRendererParams={detailCellRendererParams}
            icons={{
              groupExpanded: `<div class="ag-icon-tree-open cell-renderer-icon relative">
                               <div class="ant-tooltip custom-tooltip tooltip-placement-right left-6">
                                 <div class="ant-tooltip-arrow absolute"></div>
                                 <div class="ant-tooltip-content relative">
                                   <div class="ant-tooltip-inner" role="tooltip">Collapse</div>
                                 </div>
                               </div>
                               <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 512 512"><path d="M233.4 105.4c12.5-12.5 32.8-12.5 45.3 0l192 192c12.5 12.5 12.5 32.8 0 45.3s-32.8 12.5-45.3 0L256 173.3 86.6 342.6c-12.5 12.5-32.8 12.5-45.3 0s-12.5-32.8 0-45.3l192-192z"/></svg>
                             </div>
                           `,
              groupContracted: `<div class="ag-icon-tree-closed cell-renderer-icon relative">
                               <div class="ant-tooltip custom-tooltip tooltip-placement-right left-6">
                                 <div class="ant-tooltip-arrow absolute"></div>
                                 <div class="ant-tooltip-content relative">
                                   <div class="ant-tooltip-inner" role="tooltip">Expand</div>
                                 </div>
                               </div>
                               <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 512 512"><path d="M233.4 406.6c12.5 12.5 32.8 12.5 45.3 0l192-192c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L256 338.7 86.6 169.4c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3l192 192z"/></svg>
                             </div>
                           `,
            }}
            noRowsOverlayComponent={() => (
              <NoRecords
                rootClassName="w-full max-w-[280px] responsive-image-size"
                image={`${window.ENV.CDN_URL}assets/images/create-record-list-view.svg`}
                text={
                  module_access === "full_access" ? (
                    <div className="flex items-center gap-1">
                      <Typography
                        className="sm:text-base text-xs underline underline-offset-1 text-black font-bold cursor-pointer"
                        onClick={() => setAddEditSidebarOpen(true)}
                      >
                        {_t("Click here ")}
                      </Typography>
                      <Typography className="sm:text-base text-xs text-black font-semibold">
                        {_t("to Create a New Record")}
                      </Typography>
                    </div>
                  ) : (
                    <Typography className="sm:text-base text-xs text-black font-semibold">
                      {_t("No Record Found")}
                    </Typography>
                  )
                }
              />
            )}
            // Use `getRowHeight` for dynamic row height calculation
            getRowHeight={(params) => {
              if (params.node && params.node.detail) {
                // Detail row height calculation
                const items = params.data?.item ?? [];
                if (items.length) {
                  return items.length * 42.4 + 64; // 42 is row height and 64 is header height
                } else {
                  return 210;
                }
              }
              // Default height for main rows
              return undefined; // Default height for parent rows
            }} // Attach dynamic row height
          />
        </div>
      </div>
      {confirmArchiveDialogOpen && selectedData && (
        <ConfirmModal
          isOpen={confirmArchiveDialogOpen}
          modaltitle={_t(
            selectedData.is_deleted?.toString() === "1" ? "Active" : "Archive"
          )}
          description={_t(
            `Are you sure you want to ${
              selectedData.is_deleted?.toString() === "1"
                ? "Activate"
                : "Archive"
            } this group? ${
              selectedData.is_deleted?.toString() === "0"
                ? "To view it or Activate it later, set the filter to show Archived items."
                : ""
            }`
          )}
          modalIcon={
            selectedData.is_deleted?.toString() === "1"
              ? "fa-regular fa-regular-active"
              : "fa-regular fa-box-archive"
          }
          isLoading={loading}
          onAccept={async () => {
            setLoading(true);
            try {
              const params = await getWebWorkerApiParams({
                otherParams: {
                  ids: {
                    database_items: [selectedData.group_id],
                  },
                  is_deleted:
                    selectedData.is_deleted?.toString() === "0" ? 1 : 0,
                },
              });
              const response = (await webWorkerApi({
                url: cidbItemRoutes.item_status_change,
                method: "post",
                data: params,
              })) as ICIDBStatusChangeMaterialItemApiResponse;
              if (response.success) {
                setSelectedData(undefined);
                setConfirmArchiveDialogOpen(false);
                refreshAgGrid();
              } else {
                notification.error({
                  description: response.message,
                });
              }
            } catch (error) {
              notification.error({
                description: "Something went wrong!",
              });
            }
            setLoading(false);
          }}
          onDecline={() => {
            setConfirmArchiveDialogOpen(false);
            setSelectedData(undefined);
          }}
          onCloseModal={() => {
            setConfirmArchiveDialogOpen(false);
            setSelectedData(undefined);
          }}
        />
      )}
      {confirmDialogOpen && (selectedData || selectedSubData) && (
        <ConfirmModal
          isOpen={confirmDialogOpen}
          modaltitle={
            copyExistingItemID ? _t("Copy Existing Group") : _t("Delete")
          }
          description={_t(
            copyExistingItemID
              ? _t(
                  "You have already copied this group, are you sure you want to make another copy?"
                )
              : selectedSubData
              ? "Are you sure you want to delete this Item?"
              : "Are you sure you want to delete this group from your Cost Item Database? Any group that is in use will be Archived instead."
          )}
          noButtonLabel={copyExistingItemID ? _t("View Copy") : undefined}
          modalIcon={
            copyExistingItemID
              ? "fa-regular fa-triangle-exclamation"
              : undefined
          }
          isLoading={loading}
          additionalButton={copyExistingItemID ? _t("No") : undefined}
          onAccept={async () => {
            if (copyExistingItemID) {
              setConfirmDialogOpen(false);
              setCopyExistingItemID(undefined);
              setSelectedData((prev) =>
                prev
                  ? {
                      ...prev,
                      name: "Copy of " + prev.name,
                    }
                  : prev
              );
              if (addItemsRef.current) {
                addItemsRef.current.formik.setFieldValue(
                  "name",
                  "Copy of " + addItemsRef.current.formik.values.name
                );
                addItemsRef.current.formik.submitForm();
              }
            } else {
              setLoading(true);
              try {
                if (selectedSubData) {
                  const params = await getWebWorkerApiParams({
                    otherParams: {
                      group_id: selectedSubData.group_id,
                      group_item_ids: [selectedSubData.item_id],
                      reference_item_ids: [selectedSubData.reference_item_id],
                    },
                  });
                  const response = (await webWorkerApi({
                    url: cidbItemRoutes.delete_group_item,
                    method: "post",
                    data: params,
                  })) as IApiCallResponse;
                  if (response.success) {
                    setConfirmDialogOpen(false);
                    setCopyExistingItemID(undefined);
                    setSelectedSubData(undefined);
                    refreshAgGrid();
                  } else {
                    notification.error({
                      description: response.message,
                    });
                  }
                } else if (selectedData) {
                  const params = await getWebWorkerApiParams({
                    otherParams: {
                      group_id: selectedData.group_id,
                    },
                  });
                  const response = (await webWorkerApi({
                    url: cidbItemRoutes.delete_group,
                    method: "post",
                    data: params,
                  })) as IApiCallResponse;
                  if (response.success) {
                    setConfirmDialogOpen(false);
                    setCopyExistingItemID(undefined);
                    setSelectedData(undefined);
                    refreshAgGrid();
                  } else {
                    notification.error({
                      description: response.message,
                    });
                  }
                }
              } catch (error) {
                notification.error({
                  description: "Something went wrong!",
                });
              }
              setLoading(false);
            }
          }}
          isNoLoading={copyExistingItemID ? fetchingCopyDataLoading : undefined}
          onAdditionButtonClick={() => {
            if (copyExistingItemID) {
              setConfirmDialogOpen(false);
              setAddEditSidebarOpen(false);
              setSelectedData(undefined);
              setIsCoping(false);
              setCopyExistingItemID(undefined);
            }
          }}
          onDecline={async () => {
            setConfirmDialogOpen(false);
            setCopyExistingItemID(undefined);
            if (copyExistingItemID) {
              setFetchingCopyDataLoading(true);
              // await fetchAndSetData({ labor_id: copyExistingItemID });
              const response = await getGroupDetail(
                copyExistingItemID?.toString()
              );
              // Called to fetch Files data for the Copied item
              const data = await getWebWorkerApiParams({
                otherParams: {
                  record_id: CFConfig.group_id,
                  item_id: selectedData?.group_id,
                  moduleId: gConfig?.module_id,
                  module_key: CFConfig.mleso_items_module,
                },
              });
              const getCommonAttachmentsResponse = (await webWorkerApi({
                url: apiRoutes.COMMON.get_common_attachments,
                method: "post",
                data: data,
              })) as Promise<ICommonAttachmentRes>;

              setSelectedData((prev) =>
                response.data
                  ? {
                      ...response.data,
                      aws_files:
                        (getCommonAttachmentsResponse?.data
                          .aws_files as unknown as IAwsFile[]) || [],
                    }
                  : prev
              );
              // setSelectedData(response.data || {});
              setFetchingCopyDataLoading(false);
              setIsCoping(false);
            } else {
              setSelectedData(undefined);
            }
          }}
          onCloseModal={() => {
            setConfirmDialogOpen(false);
            setCopyExistingItemID(undefined);
            if (!copyExistingItemID) {
              setSelectedData(undefined);
            }
          }}
        />
      )}
      {addEditSidebarOpen && (
        <AddItems
          formType={selectedData && !isCoping ? "edit" : "add"}
          type="group"
          addItemsOpen={addEditSidebarOpen}
          readOnly={module_access !== "full_access"}
          setAddItemsOpen={(open) => {
            setAddEditSidebarOpen(open);
            setSelectedData(undefined);
            setIsCoping(false);
            if (isAnyFileRemoved) {
              refreshAgGrid();
              setIsAnyFileRemoved(false);
            }
          }}
          formInitialValues={formData}
          formValidationSchema={Yup.object().shape({
            name: Yup.string().trim().required("This field is required."),
            status: Yup.string().required("This field is required."),
          })}
          onRemoveFile={() => {
            if (selectedData) setIsAnyFileRemoved(true);
          }}
          formOnSubmit={async (values, formikHelpers) => {
            setFormLoading(true);

            try {
              if (selectedData && !isCoping) {
                const { existItems, removedItems } =
                  "item" in selectedData
                    ? selectedData.item?.reduce(
                        (acc, item) => {
                          if (
                            values.items?.some(
                              (i) => i.item_id === item.item_id
                            )
                          ) {
                            acc.existItems.push(item);
                          } else {
                            acc.removedItems.push(item);
                          }
                          return acc;
                        },
                        {
                          existItems:
                            [] as ICIDBGetGroupDetailApiResponse["data"]["item"],
                          removedItems:
                            [] as ICIDBGetGroupDetailApiResponse["data"]["item"],
                        }
                      )
                    : {};

                const newItems =
                  values.items?.filter(
                    (item) =>
                      !existItems?.some((i) => i.item_id === item.item_id)
                  ) || [];

                const updateItems =
                  values.items?.filter(
                    (item) =>
                      !existItems?.some(
                        (i) =>
                          i.item_id === item.item_id &&
                          i.quantity?.toString() === item.quantity?.toString()
                      ) && !newItems?.some((i) => i.item_id === item.item_id)
                  ) || [];

                const dataItemsArray = [
                  ...newItems.map((item) => ({
                    item_type: item.item_type.toString(),
                    quantity: item.quantity,
                    reference_item_id: item.reference_item_id?.toString(),
                  })),
                  ...updateItems.map((item) => ({
                    quantity: item.quantity,
                    item_id: item.item_id?.toString(),
                  })),
                ];

                const existFiles =
                  "aws_files" in selectedData
                    ? selectedData.aws_files.filter((record) =>
                        values.photos.some(
                          (photo) => photo.file_path === record.file_path
                        )
                      )
                    : [];

                const removedFiles =
                  "aws_files" in selectedData
                    ? selectedData.aws_files.filter(
                        (record) =>
                          !values.photos.some((photo) =>
                            record.image_id
                              ? photo.image_id === record.image_id
                              : photo.file_path === record.file_path
                          )
                      )
                    : [];

                const newFiles = values.photos.filter(
                  (photo) =>
                    !existFiles.some(
                      (record) => photo.file_path === record.file_path
                    )
                );
                const newData = {
                  ...(values.name !==
                  HTMLEntities.encode(sanitizeString(formData.name))
                    ? {
                        name: HTMLEntities.encode(values.name),
                      }
                    : {}),
                  ...((values.status || "") !==
                  (selectedData.is_deleted.toString() || "")
                    ? {
                        status: values.status,
                      }
                    : {}),
                  ...((values.internal_notes || "") !==
                  (selectedData.internal_notes || "")
                    ? {
                        internal_notes: values.internal_notes,
                      }
                    : {}),
                  ...(dataItemsArray.length
                    ? {
                        items: dataItemsArray,
                      }
                    : {}),
                };
                const apis = [];
                if (Object.keys(newData).length) {
                  const data = await getWebWorkerApiParams({
                    otherParams: newData,
                  });
                  apis.push(
                    updateValue(data, selectedData.group_id.toString())
                  );
                } else {
                  apis.push(
                    (async function () {
                      return { success: true } as IApiCallResponse;
                    })()
                  );
                }

                if (newFiles.length) {
                  apis.push(
                    (async function () {
                      try {
                        return (await webWorkerApi({
                          url: apiRoutes.COMMON.add_file,
                          method: "post",
                          data: {
                            module_id,
                            item_id: selectedData.group_id,
                            files: newFiles.map((photo) => ({
                              is_image: photo.is_image,
                              file_name: photo.file_name,
                              file_ext: photo.file_ext,
                              file_url: photo.file_path,
                              isheic:
                                photo?.file_ext.toLowerCase() === "heic"
                                  ? 1
                                  : 0,
                            })),
                          },
                        })) as IApiCallResponse;
                      } catch (error) {
                        return {
                          success: false,
                          message: (error as Error).message,
                        } as IApiCallResponse;
                      }
                    })()
                  );
                } else {
                  apis.push(
                    (async function () {
                      return { success: true } as IApiCallResponse;
                    })()
                  );
                }

                if (removedFiles.length) {
                  apis.push(
                    (async function () {
                      try {
                        let deleteParams: IUpdateFilesRequestBody = {};
                        removedFiles.forEach(({ image_id, file_path }) => {
                          if (image_id) {
                            const imageIdNumber = image_id as number;
                            if (deleteParams && deleteParams.fileIds) {
                              deleteParams = {
                                fileIds: [
                                  ...deleteParams.fileIds,
                                  imageIdNumber,
                                ],
                              };
                            } else {
                              deleteParams = { fileIds: [imageIdNumber] };
                            }
                          } else {
                            const filePath = file_path as string;
                            if (deleteParams && deleteParams.fileUrl) {
                              deleteParams = {
                                fileUrl: [...deleteParams.fileUrl, filePath],
                              };
                            } else {
                              deleteParams = { fileUrl: [filePath] };
                            }
                          }
                        });
                        return await deleteFiles(deleteParams);
                      } catch (error) {
                        return {
                          success: false,
                          message: (error as Error).message,
                        } as IFilePhotoDeleteApiRes;
                      }
                    })()
                  );
                } else {
                  apis.push(
                    (async function () {
                      return { success: true } as IApiCallResponse;
                    })()
                  );
                }

                if (removedItems?.length) {
                  apis.push(
                    (async function () {
                      try {
                        const params = await getWebWorkerApiParams({
                          otherParams: {
                            group_id: selectedData.group_id,
                            group_item_ids: removedItems?.map(
                              (item) => item.item_id
                            ),
                            reference_item_ids: removedItems?.map(
                              (item) => item.reference_item_id
                            ),
                          },
                        });
                        return (await webWorkerApi({
                          url: cidbItemRoutes.delete_group_item,
                          method: "post",
                          data: params,
                        })) as IApiCallResponse;
                      } catch (error) {
                        return {
                          success: true,
                          message: (error as Error).message,
                        } as IApiCallResponse;
                      }
                    })()
                  );
                } else {
                  apis.push(
                    (async function () {
                      return { success: true } as IApiCallResponse;
                    })()
                  );
                }

                const [
                  response,
                  newFilesResponse,
                  removedFilesResponse,
                  removedItemsResponse,
                ] = await Promise.all(apis);
                if (!response.success) {
                  notification.error({
                    description: response.message,
                  });
                } else if (!newFilesResponse.success) {
                  notification.error({
                    description: newFilesResponse.message,
                  });
                } else if (!removedItemsResponse.success) {
                  notification.error({
                    description: newFilesResponse.message,
                  });
                } else if (!removedFilesResponse.success) {
                  notification.error({
                    description: removedFilesResponse.message,
                  });
                }
                if (response.success) {
                  formikHelpers.resetForm();
                  setAddEditSidebarOpen(false);
                  setSelectedData(undefined);
                  refreshAgGrid();
                }
              } else {
                let attachImage = values.photos
                  .map((photo) => photo.image_id)
                  .filter(Boolean);
                let awsFilesUrl = values.photos
                  .filter((photo) => !Boolean(photo.image_id))
                  .map((photo) => ({
                    file_ext: photo.file_ext,
                    file_name: photo.file_name,
                    file_path: photo.file_path,
                    file_url: photo.file_path,
                    is_image: photo.is_image,
                    image_id: photo.image_id,
                    isheic: photo?.file_ext.toLowerCase() === "heic" ? 1 : 0,
                  }));

                const data = await getWebWorkerApiParams({
                  otherParams: {
                    name: HTMLEntities.encode(values.name),
                    status: values.status,
                    ...(values.internal_notes
                      ? {
                          internal_notes: values.internal_notes,
                        }
                      : {}),
                    ...(attachImage.length ? { attachImage } : {}),
                    ...(awsFilesUrl.length ? { awsFilesUrl } : {}),
                    items:
                      values.items?.map((item) => ({
                        item_type: item.item_type.toString(),
                        quantity: item.quantity,
                        reference_item_id: item.reference_item_id?.toString(),
                      })) || [],
                  },
                });
                const response = (await webWorkerApi<IAddCostCodeDataApiRes>({
                  url: cidbItemRoutes.add_group,
                  method: "post",
                  data: data,
                })) as IAddCostCodeDataApiRes;
                if (response.success) {
                  formikHelpers.resetForm();
                  setAddEditSidebarOpen(false);
                  setIsCoping(false);
                  refreshAgGrid();
                } else if (isCoping && response.data?.existing_record_id) {
                  setCopyExistingItemID(response.data.existing_record_id);
                  setConfirmDialogOpen(true);
                } else {
                  notification.error({
                    description: response.message,
                  });
                }
              }
            } catch (error) {
              notification.error({
                description: (error as Error).message,
              });
              console.error(
                `\n File: #group.tsx -> Line: #36 ->  `,
                (error as Error).message
              );
            }

            setFormLoading(false);
          }}
          module={
            {
              module_id,
              module_key,
              module_name: "Group item",
              original_module_name: "Item Group",
              icon: CFConfig.group_icon,
            } as IModule
          }
          loading={formLoading}
          fetchingFormItemCount={fetchingFormItemCount}
        />
      )}
      {confirmCopyDialogOpen && (
        <ConfirmModal
          isOpen={confirmCopyDialogOpen}
          modaltitle={_t("Copy Group")}
          description={_t(
            "Are you sure you want to create a Copy of this Group?"
          )}
          modalIcon="fa-regular fa-copy"
          onAccept={async () => {
            setConfirmCopyDialogOpen(false);
            if (selectedData) {
              setIsCoping(true);
              setSelectedData({
                ...selectedData,
                name: "Copy of " + selectedData.name,
                internal_notes: "",
              });

              // Find the node for the selected data
              const rowNodes = gridRef.current?.api.getRenderedNodes() || [];
              const node = rowNodes.find(
                (n) => n.data?.group_id === selectedData.group_id
              );

              if (node) {
                updateSidebarOpen(
                  selectedData as ICIDBGetGroupListApiResponseData &
                    ICIDBGetGroupDetailApiResponseData,
                  node,
                  "new"
                );
              }
            }
          }}
          onDecline={() => {
            setConfirmCopyDialogOpen(false);
            setSelectedData(undefined);
          }}
          onCloseModal={() => {
            setConfirmCopyDialogOpen(false);
            setSelectedData(undefined);
          }}
        />
      )}
    </>
  );
}

export default SubcontractorItems;

export { ErrorBoundary };
