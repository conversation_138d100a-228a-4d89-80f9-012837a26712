import { useEffect, useMemo, useRef, useState } from "react";
import isEmpty from "lodash/isEmpty";
import isEqual from "lodash/isEqual";
import {
  GridReadyEvent,
  ICellRendererParams,
  IServerSideGetRowsParams,
  SortChangedEvent,
} from "ag-grid-community";
// Atoms
import { Typography } from "~/shared/components/atoms/typography";
import { Tag } from "~/shared/components/atoms/tag";
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// Molecules
import { AvatarProfile } from "~/shared/components/molecules/avatarProfile";
import { DropdownMenu } from "~/shared/components/molecules/dropdownMenu";
import { DynamicTable } from "~/shared/components/molecules/dynamicTable";
import { NoRecords } from "~/shared/components/molecules/noRecords";

import { routes } from "~/route-services/routes";
// hooks
import {
  getGConfig,
  getGModuleFilters,
  getGProject,
  useGModules,
} from "~/zustand";
import { defaultConfig } from "~/data";
import { useModuleDataSearch } from "~/zustand/global-module-filter/hook";
import { useTranslation } from "~/hook";
import { STATUS_CODE } from "~/shared/constants";
// Redux And utils
import { removeAllSpaces } from "~/shared/utils/helper/common";
import {
  actionMenuOptList,
  dirTypeKeyByKey,
  dirTypeKeys,
} from "~/modules/people/directory/utils/constasnts";
import { getDirListApi } from "~/modules/people/directory/redux/action/driDashAction";
import { escapeHtmlEntities, sanitizeString } from "~/helpers/helper";

import DirListAction from "./DirListAction";
import { updateDirDetailApi } from "../../redux/action/dirDetailsAction";
import { getGlobalUser } from "~/zustand/global/user/slice";
import ToolTipCell from "~/shared/components/molecules/table/ToolTipCell";

let timeout: NodeJS.Timeout;
const DirectoryList = ({
  filterParams,
  onIsDataLoading,
  dirList,
  onContactAction,
  isReloadList,
  setIsReloadList,
}: IDirectoryListProps) => {
  const { _t } = useTranslation();
  const { checkModuleAccessByKey } = useGModules();
  const user: IInitialGlobalData["user"] = getGlobalUser();
  const { allow_delete_module_items = "0" } = user || {};
  const { project_id }: GProject = getGProject();
  const agGridRef = useRef<ExtendedAgGridReact | null>(null);
  const { module_id }: GConfig = getGConfig();

  const { search }: IModuleDataSearchParams = useModuleDataSearch(
    defaultConfig.directory_module
  );
  const filterSrv = getGModuleFilters() as
    | Partial<DirectoryModuleFilter>
    | undefined;

  const filter = useMemo(() => {
    return {
      tags: filterSrv?.tags || "",
      service: filterSrv?.service || "",
      status: filterSrv?.status || "0",
    };
  }, [filterSrv]);

  const [gridRowParams, setGridRowParams] = useState<IGridParamsCus | null>(
    null
  );

  const previousValues = useRef({
    filterParamsDirectory: JSON.stringify(filterParams?.directory),
    filter: JSON.stringify(filter),
    search,
    app_users: filterParams?.app_users,
  });

  const previousValuesTwo = useRef({
    isProjectContactCall: filterParams?.is_project_contact_call,
    projectId: project_id || "0",
  });

  const datasource = {
    async getRows(gridParams: IServerSideGetRowsParams) {
      const request = gridParams?.request;
      let changeGridParams: ChangeGridParams = {
        start: request?.startRow ?? 0,
        length: (request?.endRow ?? 0) - (request?.startRow ?? 0),
        order_by_name: "",
        order_by_dir: "",
      };
      const sortModel = request?.sortModel;
      if (sortModel.length) {
        const { colId, sort } = sortModel[0];
        changeGridParams = {
          ...changeGridParams,
          order_by_name: colId,
          order_by_dir: sort,
        };
      }
      setGridRowParams({
        changeGridParams,
        gridParams,
      });
    },
  };

  const fetchDirList = async () => {
    let gridData: { rowCount?: number; rowData: IDirectoryData[] } = {
      rowData: [],
    };
    const { changeGridParams, gridParams } = gridRowParams ?? {};
    const length = changeGridParams?.length ?? 0;

    const tempFil: IDirTempFil = {
      status: STATUS_CODE.ACTIVE,
    };
    if (filter?.tags) {
      tempFil.tags = filter.tags || "";
    }
    if (filter?.service) {
      tempFil.service = filter.service || "";
    }
    if (filter?.status) {
      tempFil.status = filter.status;
    }
    if (filter?.status === STATUS_CODE.ALL) {
      delete tempFil.status;
    }

    if (project_id && project_id != "0") {
      filterParams.global_project = project_id;
    }

    let dataParams: IDirListParmas = {
      length,
      ...filterParams,
      search: escapeHtmlEntities(search),
      filter: tempFil,
      page: changeGridParams?.start
        ? Math.floor(changeGridParams?.start / length)
        : 0,
    };
    if (search === "") {
      delete dataParams.search;
    }
    if (isEmpty(tempFil)) {
      delete dataParams.filter;
    }

    if (changeGridParams?.order_by_name) {
      dataParams.order_by_name = changeGridParams.order_by_name;
    }
    if (changeGridParams?.order_by_dir) {
      dataParams.order_by_dir = changeGridParams.order_by_dir;
    }

    try {
      if (dataParams?.page > 0) {
        onIsDataLoading(false);
      }
      gridParams?.api.hideOverlay();
      const resData = (await getDirListApi(dataParams)) as IDirectoryApiRes;
      onIsDataLoading(false);

      const rowCount = gridParams?.api?.getDisplayedRowCount() ?? 0;
      if (resData?.data?.length < length) {
        gridData = {
          ...gridData,
          rowCount: rowCount + (resData?.data?.length ?? 0) - 1,
        };
      }
      gridData = { ...gridData, rowData: resData?.data ?? [] };
      gridParams?.success(gridData);
      if (
        (!resData?.success || gridData.rowData.length <= 0) &&
        dataParams?.page === 0
      ) {
        gridParams?.api.showNoRowsOverlay();
      } else if (resData?.success && gridData.rowData.length > 0) {
        gridParams?.api.hideOverlay();
      }
    } catch (err) {
      onIsDataLoading(false);
      gridParams?.success({ rowCount: 0, rowData: [] });
      gridParams?.api.showNoRowsOverlay();
      gridParams?.fail();
    }
  };

  const refreshAgGrid = () => {
    const gridParams = gridRowParams?.gridParams;
    if (gridParams) {
      gridParams.api.setServerSideDatasource({ getRows: () => {} });
      gridParams.api.setServerSideDatasource(datasource);
    }
  };

  useEffect(() => {
    if (isReloadList) {
      refreshAgGrid();
      setIsReloadList?.(false);
    }
  }, [isReloadList]);

  useEffect(() => {
    if (gridRowParams?.changeGridParams) {
      if (timeout) {
        clearTimeout(timeout);
      }
      onIsDataLoading(true);
      timeout = setTimeout(() => {
        fetchDirList();
      }, 500);
    }
  }, [gridRowParams?.changeGridParams]);

  useEffect(() => {
    const currentValues = {
      filterParamsDirectory: JSON.stringify(filterParams?.directory),
      filter: JSON.stringify(filter),
      search,
      app_users: filterParams?.app_users,
    };

    if (!isEqual(previousValues.current, currentValues)) {
      previousValues.current = currentValues;
      refreshAgGrid();
    }
  }, [
    search,
    JSON.stringify(filterParams?.directory),
    JSON.stringify(filter),
    filterParams?.app_users,
  ]);

  useEffect(() => {
    const currentValuesTwo = {
      isProjectContactCall: filterParams?.is_project_contact_call,
      projectId: project_id || "0",
    };

    if (
      !isEqual(previousValuesTwo.current, currentValuesTwo) ||
      filterParams.is_project_contact_call === 1
    ) {
      previousValuesTwo.current = currentValuesTwo;
      refreshAgGrid();
    } else if (
      previousValuesTwo.current?.isProjectContactCall === 1 &&
      filterParams.is_project_contact_call === undefined &&
      previousValuesTwo.current?.projectId &&
      previousValuesTwo.current.projectId != "0" &&
      !project_id
    ) {
      previousValuesTwo.current = currentValuesTwo;
      refreshAgGrid();
    }
  }, [filterParams?.is_project_contact_call, project_id]);

  const onGridReady = (gridParams: GridReadyEvent) => {
    gridParams?.api?.setServerSideDatasource(datasource);
  };
  const onSortChanged = async (params: SortChangedEvent) => {
    params.api.setServerSideDatasource({ getRows: () => {} });
    params.api.setServerSideDatasource(datasource);
  };

  const handleFavorite = async (data: {
    status: boolean;
    id: string;
    params: ICellRendererParams;
  }) => {
    const { status, id, params } = data;
    let newData = { is_favorite: status ? 0 : 1 };
    const oldVal = params.data.is_favorite;
    if (params && params?.colDef && params.colDef?.field) {
      params.node.setDataValue(params.colDef.field, status ? 0 : 1);
    }
    const resApi = (await updateDirDetailApi({
      directory_id: id || "",
      type: params.data.type || "",
      module_id: module_id,
      ...newData,
    })) as IDirDetailsUpdateApiRes;

    if (!resApi.success) {
      if (params && params?.colDef && params.colDef?.field) {
        params.node.setDataValue(params.colDef.field, oldVal);
      }
      notification.error({
        description: resApi.message ?? "Something went wrong",
      });
    }
  };
  const updatedActionMenuOptList = useMemo(() => {
    return actionMenuOptList.map((option) => {
      if (option.key === "delete") {
        return { ...option, disabled: allow_delete_module_items === "0" };
      }
      return option;
    });
  }, [allow_delete_module_items, actionMenuOptList]);

  const allReadOnly = useMemo(() => {
    return dirList.every((item) => {
      return checkModuleAccessByKey(item.key) === "read_only";
    });
  }, [dirList]);

  const columnDefs = [
    {
      headerName: "",
      field: "image",
      sortable: false,
      minWidth: 50,
      maxWidth: 50,
      cellRenderer: (params: IDirTableCellRenderer) => {
        const { data } = params;
        const name = data.last_name
          ? `${removeAllSpaces(data.first_name || "")}  ${data.last_name}`
          : removeAllSpaces(data.first_name || "") || "";

        const avatarSrc = data.image || "";
        return name || avatarSrc ? (
          <Tooltip
            title={`${HTMLEntities.decode(sanitizeString(data?.emp_name))}`}
          >
            <div>
              <AvatarProfile
                user={{
                  name: HTMLEntities.decode(sanitizeString(name)),
                  image: avatarSrc,
                }}
                className="m-auto"
                iconClassName="text-[11px] font-semibold"
              />
            </div>
          </Tooltip>
        ) : (
          <div className="text-center">-</div>
        );
      },
    },
    {
      headerName: _t("Company"),
      field: "company_name",
      minWidth: 150,
      flex: 1,
      sortable: true,
      resizable: true,
      suppressMovable: false,
      cellRenderer: (params: IDirTableCellRenderer) => {
        const { data } = params;

        const companyName = HTMLEntities.decode(
          sanitizeString(data?.company_name)
        );
        return companyName ? (
          <Tooltip
            title={companyName && companyName !== "null" ? companyName : ""}
          >
            <Typography className="table-tooltip-text">
              {companyName && companyName !== "null" ? companyName : ""}
            </Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("Name"),
      field: "emp_name",
      minWidth: 150,
      sortable: true,
      resizable: true,
      suppressMovable: false,
      flex: 1,
      cellRenderer: (params: IDirTableCellRenderer) => {
        const { data } = params;
        const fullName = HTMLEntities.decode(sanitizeString(data?.emp_name));
        return fullName ? (
          <Tooltip title={fullName}>
            <Typography className="table-tooltip-text">{fullName}</Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("Phone"),
      field: "phone",
      sortable: true,
      suppressMovable: false,
      minWidth: 130,
      maxWidth: 150,
      flex: 1,
      cellRenderer: ToolTipCell,
    },
    {
      headerName: _t("Cell"),
      field: "cell",
      sortable: true,
      suppressMovable: false,
      minWidth: 130,
      maxWidth: 150,
      flex: 1,
      cellRenderer: ToolTipCell,
    },
    {
      headerName: _t("Address"),
      field: "address",
      minWidth: 150,
      resizable: true,
      suppressMovable: false,
      sortable: true,
      flex: 1,
      cellRenderer: ({ data }: IDirTableCellRenderer) => {
        const address1 = data?.address1;
        const address2 = data?.address2;
        const city = data?.city;
        const state = data?.state;
        const zip = data?.zip;

        let addressParts = [address1, address2, city].filter(Boolean);
        if (state || zip) {
          addressParts.push([state, zip].filter(Boolean).join(" "));
        }

        const fullAddress = HTMLEntities.decode(
          sanitizeString(addressParts.join(", "))
        );

        return fullAddress ? (
          <Tooltip title={fullAddress}>
            <Typography className="table-tooltip-text">
              {fullAddress}
            </Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("Type"),
      field: "type_name",
      maxWidth: 120,
      minWidth: 120,
      headerClass: "ag-header-center",
      suppressMovable: false,
      sortable: true,
      cellRenderer: (params: IDirTableCellRenderer) => {
        return (
          <Tooltip
            title={
              params?.data
                ? params.data.app_access === 1
                  ? params.data.type_name + _t(" With App Access")
                  : ""
                : ""
            }
          >
            <div className="text-center">
              <Tag
                icon={
                  params?.data?.app_access === 1 && (
                    <FontAwesomeIcon icon="fa-solid fa-user" />
                  )
                }
                color="#EBF1F9"
                className="!text-primary-900 mx-auto text-13 type-badge common-tag"
              >
                {params?.data?.type_name}
              </Tag>
            </div>
          </Tooltip>
        );
      },
    },
    {
      headerName: "",
      field: "is_favorite",
      maxWidth: 80,
      minWidth: 80,
      suppressMenu: true,
      cellClass: "!cursor-auto",
      cellRenderer: (params: ICellRendererParams) => {
        const { is_favorite, type_key, user_id } = params?.data;
        const isReadOnly =
          checkModuleAccessByKey(dirTypeKeys[type_key || ""]) === "read_only";
        return (
          <div className="flex items-center gap-3 justify-center">
            <div className="w-6 flex items-center justify-center">
              {(type_key === dirTypeKeyByKey.contractor ||
                type_key === dirTypeKeyByKey.vendor ||
                type_key === dirTypeKeyByKey.misc_contact ||
                type_key === dirTypeKeyByKey.lead) && (
                <Tooltip title={Boolean(is_favorite) ? _t("Favorites") : ""}>
                  <FontAwesomeIcon
                    className={`w-4 h-4 ${
                      Boolean(is_favorite)
                        ? "text-deep-orange-500"
                        : "text-primary-900"
                    } ${isReadOnly ? "" : "cursor-pointer"}`}
                    icon={
                      Boolean(is_favorite)
                        ? "fa-solid fa-star"
                        : "fa-regular fa-star"
                    }
                    onClick={() => {
                      if (!isReadOnly) {
                        handleFavorite({
                          status: Boolean(is_favorite),
                          id: params.data?.user_id?.toString() || "",
                          params,
                        });
                      }
                    }}
                  />
                </Tooltip>
              )}
            </div>
            {!isReadOnly && (
              <div className="w-6 flex items-center justify-center">
                <DirListAction
                  paramsData={params?.data as IDirectoryData}
                  updatedActionMenuOptList={updatedActionMenuOptList}
                  refreshAgGridData={() => {
                    refreshAgGrid();
                  }}
                />
              </div>
            )}
          </div>
        );
      },
    },
  ];

  return (
    <>
      <div className="md:h-[calc(100dvh-270px)] list-view-table h-[calc(100dvh-270px)] ag-grid-cell-pointer ag-theme-alpine">
        <DynamicTable
          ref={agGridRef}
          columnDefs={columnDefs}
          onGridReady={onGridReady}
          onSortChanged={onSortChanged}
          noRowsOverlayComponent={() => (
            <NoRecords
              rootClassName="w-full max-w-[280px]"
              image={`${window.ENV.CDN_URL}assets/images/create-record-directory.svg`}
              imageWSize="280"
              imageHSize="227"
              text={
                !allReadOnly ? (
                  <div>
                    <DropdownMenu
                      buttonClass="w-fit mr-1"
                      placement="bottomRight"
                      options={dirList?.map((item) => {
                        let isDisabled = item?.disabled;
                        if (!item?.disabled) {
                          isDisabled =
                            checkModuleAccessByKey(item?.key || "") ===
                            "read_only";
                        }
                        return {
                          ...item,
                          disabled: isDisabled,
                          label: `+ ${item.labelSingular}`,
                          onClick: () => {
                            onContactAction(item);
                          },
                        };
                      })}
                    >
                      <Typography className="sm:text-base text-xs underline underline-offset-1 text-black font-bold cursor-pointer">
                        {_t("Click here")}
                      </Typography>
                    </DropdownMenu>
                    <Typography className="sm:text-base text-xs text-black font-semibold">
                      {_t(" to Create a New Record")}
                    </Typography>
                  </div>
                ) : (
                  <Typography className="sm:text-base text-xs text-black font-semibold">
                    {_t("No Record Found")}
                  </Typography>
                )
              }
            />
          )}
          enableOpenInNewTab={window.ENV.ENABLE_ALL_CLICK}
          generateOpenInNewTabUrl={(data) =>
            `${routes.MANAGE_DIRECTORY.url}/${data?.user_id}?type=${data?.type}`
          }
          restrictOpenInNewTabFields={["email", "is_favorite"]}
        />
      </div>
    </>
  );
};
export default DirectoryList;
