import { useEffect, useMemo, useRef, useState } from "react";
import delay from "lodash/delay";
import isEmpty from "lodash/isEmpty";
// Atoms
import { Popover } from "~/shared/components/atoms/popover";
import { Button } from "~/shared/components/atoms/button";
import { Typography } from "~/shared/components/atoms/typography";
// Molecules
import { GoogleMap } from "~/shared/components/molecules/googleMap";
import { FieldLabel } from "~/shared/components/molecules/fieldLabel";
import { InlineField } from "~/shared/components/molecules/inlineField";
import { FieldStatus } from "~/shared/components/molecules/fieldStatus";
import { CrudCommonCard } from "~/shared/components/molecules/crudCommonCard";
// Other
import { useAppDispatch, useAppSelector } from "../../../redux/store";
import { updateDirDetailApi } from "../../../redux/action/dirDetailsAction";
import { getGConfig, useGModules } from "~/zustand";
import {
  dirAddrLatLong,
  dirTypeKeys,
  fieldStatus,
} from "../../../utils/constasnts";

import {
  getStatusForField,
  getStatusActionForField,
} from "~/shared/utils/helper/common";
import { getAddressComponent } from "~/shared/utils/helper/locationAddress";
import { useTranslation } from "~/hook";
import { useParams } from "@remix-run/react";
import { updateDirSalesDetail } from "../../../redux/slices/dirSalesSlice";

const AddressInformation = () => {
  const { _t } = useTranslation();
  const gConfig: GConfig = getGConfig();
  const params: RouteParams = useParams();

  const { checkModuleAccessByKey } = useGModules();
  const dispatch = useAppDispatch();
  const loadingStatusRef = useRef(fieldStatus);
  const addressInfoRef = useRef<HTMLDivElement | null>(null);
  const [viewUnit, setViewUnit] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const toggleAddressInfo = () => {
    if (!loading) setViewUnit((current) => !current);
  };
  const [contactAddress, setContactAddress] = useState<boolean>(false);

  const { salesDetail, isSalesLoading }: IDirSalesInitialState = useAppSelector(
    (state) => state.dirSales
  );

  const { details }: IDirInitialState = useAppSelector(
    (state) => state.dirDetails
  );
  const { addressInfo }: IDirInitialState = useAppSelector(
    (state) => state.dirDetails
  );
  const {
    street1,
    street2,
    sales_city,
    sales_state,
    sales_zip,
    latitude,
    longitude,
    temperature_scale,
  } = salesDetail;

  const { address1, address2, city, state, zip } = addressInfo;

  const contactAddressChange = (newOpen: boolean) => {
    if (address1 === "" && city === "" && state === "" && zip === "") {
      notification.error({
        description: _t("Contact address not provided."),
      });
      return;
    }
    setContactAddress(newOpen);
  };

  const initialValues: IDirSalesDetails = useMemo(
    () => ({
      street1: street1 || "",
      street2: street2 || "",
      sales_city: sales_city || "",
      sales_state: sales_state || "",
      latitude: Number(latitude) || 0,
      longitude: Number(longitude) || 0,
      sales_zip: sales_zip || "",
      user_id: params?.id || "",
    }),
    [salesDetail]
  );

  useEffect(() => {
    setInputValues((prev: IDirSalesDetails) => ({
      ...prev,
      street1: salesDetail?.street1 ?? "",
      street2: salesDetail?.street2 ?? "",
      sales_city: salesDetail?.sales_city ?? "",
      sales_state: salesDetail?.sales_state ?? "",
      sales_zip: salesDetail?.sales_zip ?? "",
      latitude: salesDetail?.latitude || 0,
      longitude: salesDetail?.longitude || 0,
    }));
  }, [salesDetail, viewUnit]);

  const isReadOnly = useMemo(
    () =>
      checkModuleAccessByKey(dirTypeKeys[details?.type_key || ""]) ===
      "read_only",
    [details?.type_key]
  );

  const [inputValues, setInputValues] =
    useState<IDirSalesDetails>(initialValues);
  const [loadingStatus, setLoadingStatus] =
    useState<Array<IFieldStatus>>(fieldStatus);
  const [locationLatLong, setLocationLatLong] =
    useState<IDirAddrLatLong>(dirAddrLatLong);
  const hasDirStreet1 = Boolean(inputValues?.street1);
  const hasDirStreet2 = Boolean(inputValues?.street2);
  const hasDirCity = Boolean(inputValues?.sales_city);
  const hasDirState = Boolean(inputValues?.sales_state);
  const hasDirZip = Boolean(inputValues?.sales_zip);
  useEffect(() => {
    if (addressInfoRef?.current) {
      const mouseEventHandler = (e: MouseEvent) => {
        if (!addressInfoRef?.current?.contains(e.target as Node)) {
          setViewUnit(false);
        }
      };
      window.addEventListener("mousedown", mouseEventHandler);
      return () => {
        window.removeEventListener("mousedown", mouseEventHandler);
      };
    }
  }, [addressInfoRef]);

  const handleChangeFieldStatus = ({ field, status, action }: IFieldStatus) => {
    const checkStatus = loadingStatus.find(
      (item: IFieldStatus) => item?.field === field
    );
    if (
      !(
        (checkStatus?.status === "loading" ||
          checkStatus?.status === "success" ||
          checkStatus?.status === "save") &&
        (action === "ME" || action === "ML")
      )
    ) {
      setLoadingStatus((prevState: IFieldStatus[]) => {
        const newState = prevState.map((item) =>
          item.field === field ? { ...item, status: status, action } : item
        );
        loadingStatusRef.current = newState;
        return newState;
      });
    }
  };

  const handleInputChange = ({
    target: { value, name },
  }: React.ChangeEvent<HTMLInputElement>) => {
    setInputValues({ ...inputValues, [name]: value });
  };

  const handleSelectedLocation = async (place: IdirAddrPlaceDetails) => {
    setLocationLatLong({
      ...locationLatLong,
      latitude: place?.geometry?.location?.lat(),
      longitude: place?.geometry?.location?.lng(),
    });
    const streetNumber = getAddressComponent(place, "street_number")
      ? `${getAddressComponent(place, "street_number")} `
      : "";
    const updateValues: IDirSalesDetails = {
      street1: `${streetNumber}${getAddressComponent(place, "route")}`,
      street2: "",
      sales_city: getAddressComponent(place, "locality"),
      sales_state: getAddressComponent(place, "administrative_area_level_1"),
      sales_zip: getAddressComponent(place, "postal_code"),
      latitude: place?.geometry?.location?.lat(),
      longitude: place?.geometry?.location?.lng(),
    };
    setInputValues(updateValues);
    await updateAddressInfo(updateValues);
  };

  const handleInputBlur = async (data?: boolean) => {
    if (viewUnit && data != true) {
      return false;
    }
    const updateValues: IDirSalesDetails = {
      ...inputValues,
    };
    await updateAddressInfo(updateValues);
  };

  async function updateAddressInfo(updateInfo: IDirSalesDetails) {
    if (
      updateInfo &&
      updateInfo.street1?.trim() === street1?.trim() &&
      updateInfo.street2?.trim() === street2?.trim() &&
      updateInfo.sales_state?.trim() === sales_state?.trim() &&
      updateInfo.sales_city?.trim() === sales_city?.trim() &&
      updateInfo.sales_zip?.trim() === sales_zip?.trim()
    ) {
      return;
    }
    setLoading(true);
    setViewUnit(false);
    handleChangeFieldStatus({
      field: "address_info",
      status: "loading",
      action: "API",
    });
    const updateRes = (await updateDirDetailApi({
      directory_id: params?.id || "",
      type: "",
      module_id: gConfig?.module_id,
      ...updateInfo,
    })) as IDirSalesDetailsUpdateApiRes;
    if (updateRes?.success) {
      handleChangeFieldStatus({
        field: "address_info",
        status: "success",
        action: "API",
      });
      dispatch(updateDirSalesDetail(updateInfo));
    } else {
      handleChangeFieldStatus({
        field: "address_info",
        status: "error",
        action: "API",
      });
      setInputValues((prev: IDirSalesDetails) => ({
        ...prev,
        street1: salesDetail?.street1 ?? "",
        street2: salesDetail?.street2 ?? "",
        sales_city: salesDetail?.sales_city ?? "",
        sales_state: salesDetail?.sales_state ?? "",
        sales_zip: salesDetail?.sales_zip ?? "",
        latitude: salesDetail?.latitude || 0,
        longitude: salesDetail?.longitude || 0,
      }));
      notification.error({
        description: updateRes?.message,
      });
    }
    setLoading(false);
    delay(() => {
      const fieldAction = getStatusActionForField(
        loadingStatusRef.current,
        "address_info"
      );
      handleChangeFieldStatus({
        field: "address_info",
        status: "FOCUS" === fieldAction ? "save" : "button",
        action: fieldAction || "API",
      });
    }, 3000);
  }

  const copyToClipboard = () => {
    setInputValues((prev: IDirSalesDetails) => ({
      ...prev,
      street1: address1 ?? "",
      street2: address2 ?? "",
      sales_city: city ?? "",
      sales_state: state ?? "",
      sales_zip: zip ?? "",
    }));
    setViewUnit(true);
    updateAddressInfo({
      street1: address1,
      street2: address2,
      sales_city: city,
      sales_state: state,
      sales_zip: zip,
    });
    setContactAddress(false);
  };

  return (
    <>
      <CrudCommonCard
        headerTitle={_t("Project Address")}
        headerRightButton={
          !street1 && !isReadOnly && !isSalesLoading ? (
            <Popover
              content={
                <div className="min-w-[272px]">
                  <Typography className="block text-sm py-2 px-3.5 bg-gray-50 relative before:w-full before:h-px before:bg-[linear-gradient(177deg,#a5a5a53d_24%,#fafafa_100%)] before:bottom-0 before:left-1/2 before:-translate-x-1/2 before:absolute">
                    {_t("Copy Contact Address to Project Address?")}
                  </Typography>
                  <div className="py-2 px-3.5">
                    <Typography className="block text-13">
                      <Typography className="font-medium text-13">
                        {_t("Street")}
                        {": "}
                      </Typography>
                      {address1}
                    </Typography>
                    <Typography className="block text-13">
                      <Typography className="font-medium text-13">
                        {_t("Street")}
                        {" 2: "}
                      </Typography>
                      {address2}
                    </Typography>
                    <Typography className="block text-13">
                      <Typography className="font-medium text-13">
                        {_t("City")}/{_t("ST")}/{_t("Zip")}
                        {": "}
                      </Typography>
                      {city}
                      {city && state ? `, ${state}` : state}
                      {zip ? ` ${zip}` : ""}
                    </Typography>
                    <div className="flex gap-2 justify-center mt-3">
                      <Button
                        type="primary"
                        className="w-fit primary-btn"
                        onClick={copyToClipboard}
                      >
                        {_t("Yes")}
                      </Button>
                      <Button
                        className="hover:!border-primary-900 hover:!text-primary-900 active:!border-primary-900 active:!text-primary-900"
                        onClick={() => setContactAddress(false)}
                      >
                        {_t("No")}
                      </Button>
                    </div>
                  </div>
                </div>
              }
              placement="left"
              trigger="click"
              open={contactAddress}
              onOpenChange={contactAddressChange}
            >
              <Button
                type="primary"
                className="px-2 h-[30px] justify-center font-medium !bg-[#EBF1F9] !text-primary-900 !border-0 dark:text-white/90 dark:!bg-dark-400"
              >
                {_t("Copy from Contact Address?")}
              </Button>
            </Popover>
          ) : (
            <></>
          )
        }
        iconProps={{
          icon: "fa-solid fa-address-card",
          containerClassName:
            "bg-[linear-gradient(180deg,#B08CAB1a_0%,#734B6D1a_100%)]",
          id: "address_information_icon",
          colors: ["#B08CAB", "#734B6D"],
        }}
        children={
          <>
            <ul className="pt-2">
              <li>
                <InlineField
                  label={_t("Address")}
                  labelPlacement="left"
                  labelClass="sm:w-[100px] sm:max-w-[100px]"
                  field={
                    <ul
                      className={`grid items-start w-full ${
                        viewUnit ? "" : "xl:grid-cols-2 gap-2"
                      }`}
                    >
                      <li
                        className={`w-full gap-1 flex justify-between p-1.5 pt-2 ${
                          isReadOnly
                            ? "cursor-default sm:bg-transparent bg-[#f4f5f6]"
                            : "hover:bg-[#f4f5f6] min-[768px]:bg-transparent bg-[#f4f5f6] cursor-pointer"
                        } ${viewUnit ? "hidden" : ""}`}
                        onClick={() => {
                          if (isReadOnly) {
                            return false;
                          }
                          toggleAddressInfo();
                        }}
                        onMouseEnter={() => {
                          handleChangeFieldStatus({
                            field: "address_info",
                            status: "edit",
                            action: "ME",
                          });
                        }}
                        onMouseLeave={() => {
                          handleChangeFieldStatus({
                            field: "address_info",
                            status: "button",
                            action: "ML",
                          });
                        }}
                      >
                        <ul className="w-full">
                          {isEmpty(street1) &&
                          isEmpty(street2) &&
                          isEmpty(sales_city) &&
                          isEmpty(sales_state) &&
                          isEmpty(sales_zip) ? (
                            "-"
                          ) : (
                            <>
                              <li className="text-primary-900 text-sm">
                                {street1}
                                {(hasDirStreet1 && hasDirStreet2) ||
                                (hasDirStreet1 &&
                                  (hasDirCity || hasDirState || hasDirZip))
                                  ? ", "
                                  : ""}
                              </li>
                              <li className="text-primary-900 text-sm">
                                {street2}
                                {hasDirStreet2 &&
                                (hasDirCity || hasDirState || hasDirZip)
                                  ? ", "
                                  : ""}
                              </li>
                              <li>
                                <Typography className="text-primary-900 text-sm">
                                  {sales_city}
                                </Typography>
                                <Typography className="text-primary-900 text-sm">
                                  {hasDirCity && hasDirState
                                    ? `, ${sales_state}`
                                    : sales_state}
                                  {""}
                                </Typography>
                                <Typography className="text-primary-900 text-sm">
                                  {hasDirZip && (hasDirState || hasDirCity)
                                    ? `, ${sales_zip}`
                                    : sales_zip}
                                </Typography>
                              </li>
                            </>
                          )}
                        </ul>
                        {!isReadOnly && (
                          <FieldStatus
                            status={getStatusForField(
                              loadingStatus,
                              "address_info"
                            )}
                          />
                        )}
                      </li>
                      <li className="w-full grid 2xl:grid-cols-2 gap-2">
                        <GoogleMap
                          key={isSalesLoading ? "map_loading" : "map_loaded"}
                          ref={addressInfoRef}
                          cssStyle={{ height: "150px" }}
                          addressInfo={inputValues}
                          mapAddress={{
                            address1: salesDetail?.street1 || "",
                            address2: salesDetail?.street2 || "",
                            city: salesDetail?.sales_city || "",
                            state: salesDetail?.sales_state || "",
                            zip: salesDetail?.sales_zip || "",
                          }}
                          temperature_scale={temperature_scale}
                          handleInputChange={handleInputChange}
                          handleSelectedLocation={handleSelectedLocation}
                          isEditable={viewUnit}
                          handleInputBlur={handleInputBlur}
                          title={[
                            street1,
                            street2,
                            sales_city,
                            sales_state,
                            sales_zip,
                          ]
                            .filter((value) => !!value)
                            .join(", ")}
                        />
                      </li>
                    </ul>
                  }
                />
              </li>
            </ul>
          </>
        }
      />
    </>
  );
};

export default AddressInformation;
