import { useEffect, useMemo, useRef, useState } from "react";
// Atoms
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// Molecules
import { InputField } from "~/shared/components/molecules/inputField";
import { InlineField } from "~/shared/components/molecules/inlineField";
import { SelectField } from "~/shared/components/molecules/selectField";
import { CrudCommonCard } from "~/shared/components/molecules/crudCommonCard";
import { DatePickerField } from "~/shared/components/molecules/datePickerField";
import { InputNumberField } from "~/shared/components/molecules/inputNumberField";
import { EstimatesFieldRedirectionIcon } from "~/shared/components/molecules/fieldRedirect/estimatesFieldRedirectionIcon";
// Organisms
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";
// Hook
import { useTranslation } from "~/hook";
// Other
import { IOppotunitiesSalesDetailsProps } from "../../../utils/types";
import {
  filterOptionBySubstring,
  getStatusForField,
  onKeyDownCurrency,
} from "~/shared/utils/helper/common";
import { getGConfig, getGSettings } from "~/zustand";
import { useAppOPPSelector } from "../../../redux/store";
import escape from "lodash/escape";
import dayjs from "dayjs";
import { formatAmount, sanitizeString } from "~/helpers/helper";

const scoreOptions = [
  { label: "5 (Likely to Purchase)", value: "5" },
  { label: "4", value: "4" },
  { label: "3", value: "3" },
  { label: "2", value: "2" },
  { label: "1 (Not Likely to Purchase)", value: "1" },
];

const OppotunitiesSalesDetails = ({
  details,
  inputValues,
  setInputValues,
  handleUpdateField,
  loadingStatus,
}: IOppotunitiesSalesDetailsProps) => {
  const { _t } = useTranslation();
  const { module_access } = getGConfig();
  const { date_format } = getGSettings();
  const { oppDetail, estimateData, isEstimateDataLoading } = useAppOPPSelector(
    (state) => state.opportunityDetails
  );
  const [isDatePVisible, setIsDatePVisible] = useState<boolean>(false);
  const dtDivRef = useRef<HTMLLIElement>(null);
  const [dateBeingChanged, setDateBeingChanged] = useState<boolean>(false);
  const [isEndDatePickerOpened, setIsEndDatePickerOpened] =
    useState<boolean>(false);
  const [isFocusBudgetAmountVal, setIsFocusBudgetAmountVal] = useState(false);
  const [openStartDateCalender, setOpenStartDateCalender] = useState(false);
  const { inputFormatter, unformatted } = useCurrencyFormatter();
  const inputBlurStatus = useRef(false);

  const handleChangeFieldStatus = ({ field, status, action }: IFieldStatus) => {
    const checkStatus = loadingStatus.find(
      (item: IFieldStatus) => item?.field === field
    );

    if (
      !(
        (checkStatus?.status === "loading" ||
          checkStatus?.status === "success" ||
          checkStatus?.status === "save") &&
        (action === "ME" || action === "ML")
      )
    ) {
    }
  };

  // estimate data dropdown Options
  const estimateDataOptions = useMemo(
    () =>
      estimateData.length
        ? estimateData.map((item) => {
            return {
              label: `#${HTMLEntities.decode(
                sanitizeString(
                  item.company_estimate_id
                    ? item.company_estimate_id.trim()
                    : ""
                )
              )} ${
                item.project_name
                  ? `(${HTMLEntities.decode(
                      sanitizeString(
                        item.project_name ? item.project_name.trim() : ""
                      )
                    )})`
                  : ""
              } `,
              value: String(item.estimate_id),
            };
          })
        : [],
    [estimateData]
  );
  const handleDateFieldOutsideClick = (event: MouseEvent) => {
    const clickedElement = event.target as HTMLElement;
    const targetElement = document.querySelector(".ant-picker-dropdown");

    if (targetElement && targetElement.contains(clickedElement)) {
      return;
    }

    if (
      dtDivRef.current &&
      dtDivRef.current.contains(clickedElement) &&
      clickedElement.tagName.toLowerCase() === "svg" &&
      !isEndDatePickerOpened
    ) {
      setIsDatePVisible(false);
      return;
    }

    if (
      dtDivRef.current &&
      !dtDivRef.current.contains(event.target as Node) &&
      !dateBeingChanged
    ) {
      setIsDatePVisible(false);
    }
  };

  useEffect(() => {
    document.addEventListener("mousedown", handleDateFieldOutsideClick);
    return () => {
      document.removeEventListener("mousedown", handleDateFieldOutsideClick);
    };
  }, []);

  return (
    <CrudCommonCard
      headerTitle={_t("Sales Details")}
      iconProps={{
        icon: "fa-solid fa-square-list",
        containerClassName:
          "bg-[linear-gradient(180deg,#7FA3FF1a_0%,#3387FD1a_100%)]",
        id: "sales_details_icon",
        colors: ["#7FA3FF", "#3387FD"],
      }}
      children={
        <div className="pt-2">
          <ul className="w-full flex flex-col gap-1 mt-[3px]">
            <li>
              <SelectField
                label={_t("Score")}
                placeholder={_t("Select Score")}
                labelPlacement="left"
                value={
                  inputValues.score
                    ? inputValues?.score === 0
                      ? ""
                      : scoreOptions.filter((item) => {
                          return (
                            String(inputValues.score) === String(item?.value)
                          );
                        })
                    : []
                }
                editInline={true}
                iconView={true}
                readOnly={module_access === "read_only"}
                options={scoreOptions}
                allowClear={true}
                showSearch={true}
                fixStatus={getStatusForField(loadingStatus, "score")}
                onChange={(value) => {
                  setInputValues({
                    ...inputValues,
                    score: Number(value),
                  });
                }}
                onSelect={(e) => {
                  const value = e.toString();
                  if (oppDetail && String(value) !== String(oppDetail.score)) {
                    handleUpdateField({
                      score: value ? value : "",
                    });
                  } else {
                    handleChangeFieldStatus({
                      field: "score",
                      status: "button",
                      action: "BLUR",
                    });
                  }
                }}
                onClear={() => {
                  handleUpdateField({
                    score: "",
                  });
                  handleUpdateField({
                    score: "",
                  });
                }}
              />
            </li>
            <li>
              <InputNumberField
                label={_t("Estimated Value")}
                placeholder={inputFormatter("0.00").value}
                value={
                  inputValues.budget_amount === "0.00"
                    ? ""
                    : inputValues.budget_amount
                }
                name="amount"
                labelPlacement="left"
                editInline={true}
                iconView={true}
                readOnly={module_access === "read_only"}
                disabled={module_access === "read_only"}
                fixStatus={getStatusForField(loadingStatus, "budget_amount")}
                labelClass="dark:text-white/90"
                // check if nothing is breaking
                // formatter={(value, info) => {
                //   if (inputBlurStatus.current) {
                //     inputBlurStatus.current = false;
                //     info.input = "";

                //     return inputFormatter("").value;
                //   }

                //   const inputValue = info.input.trim();

                //   const valueToFormat =
                //     inputValue.length > 0
                //       ? unformatted(inputValue)
                //       : value
                //       ? Number(value).toFixed(2)
                //       : String(value);

                //   return inputFormatter(valueToFormat).value;
                // }}
                formatter={(value, info) => {
                  const inputValue = info.input.trim();
                  const valueToFormat =
                    inputValue !== "0" && inputValue.length > 0
                      ? unformatted(inputValue)
                      : String(value);

                  return isFocusBudgetAmountVal
                    ? inputFormatter(valueToFormat).value
                    : !!value
                    ? inputFormatter(Number(value)?.toFixed(2)).value
                    : "";
                }}
                onChange={(value) => {
                  setInputValues({
                    ...inputValues,
                    budget_amount: value,
                  });
                }}
                parser={(value) => {
                  if (!value) return "";
                  const inputValue = unformatted(value.toString());
                  return inputValue;
                }}
                onKeyDown={(event) =>
                  onKeyDownCurrency(event, {
                    integerDigits: 8,
                    decimalDigits: 2,
                    unformatted,
                    allowNegative: false,
                    decimalSeparator: inputFormatter().decimal_separator,
                  })
                }
                prefix={inputFormatter().currency_symbol}
                onMouseEnter={() => {
                  handleChangeFieldStatus({
                    field: "budget_amount",
                    status: "edit",
                    action: "ME",
                  });
                }}
                onMouseLeaveDiv={() => {
                  handleChangeFieldStatus({
                    field: "budget_amount",
                    status: "button",
                    action: "ML",
                  });
                }}
                onFocus={() => {
                  handleChangeFieldStatus({
                    field: "budget_amount",
                    status: "save",
                    action: "FOCUS",
                  });
                  setIsFocusBudgetAmountVal(true);
                }}
                onBlur={(e) => {
                  inputBlurStatus.current = true;
                  const inputValue = e.target.value.trim();
                  const value = !!inputValue ? unformatted(inputValue) : "0.00";
                  const detailsValue = !!oppDetail?.budget_amount
                    ? oppDetail?.budget_amount.toString().includes(".")
                      ? oppDetail?.budget_amount
                      : (Number(oppDetail?.budget_amount) / 100).toFixed(2)
                    : "0.00";
                  if (value !== detailsValue) {
                    handleUpdateField({
                      budget_amount: escape(
                        Number(value)?.toFixed(2).toString()
                      ),
                    });
                  }
                  setIsFocusBudgetAmountVal(false);
                  handleChangeFieldStatus({
                    field: "budget_amount",
                    status: "button",
                    action: "BLUR",
                  });
                }}
              />

              {/* <InputCurrencyField
                label={_t("Estimated Value")}
                placeholder="0.00"
                name="amount"
                labelPlacement="left"
                value={inputValues?.budget_amount ?? ""}
                editInline={true}
                iconView={true}
                fixStatus={getStatusForField(loadingStatus, "budget_amount")}
                onChange={(e) => {
                  const budgetAmount = Number(e.target.value);
                  setFormData({
                    ...formData,
                    budget_amount: budgetAmount,
                  });
                }}
                onBlur={() => {
                  if (formData.budget_amount !== oppDetail.budget_amount) {
                    handleUpdate(
                      {
                        budget_amount: formData.budget_amount,
                      },
                      "budget_amount"
                    );
                  } else {
                    handleChangeFieldStatus({
                      field: "budget_amount",
                      status: "button",
                      action: "BLUR",
                    });
                  }
                }}
                onMouseEnter={() => {
                  handleChangeFieldStatus({
                    field: "budget_amount",
                    status: "edit",
                    action: "ME",
                  });
                }}
                onMouseLeave={() => {
                  handleChangeFieldStatus({
                    field: "budget_amount",
                    status: "button",
                    action: "ML",
                  });
                }}
              /> */}
            </li>
            <li>
              <DatePickerField
                label={_t("Est. Sales Date")}
                name="est_sales_date"
                labelPlacement="left"
                placeholder="Select Date"
                editInline={true}
                iconView={true}
                format={date_format}
                required={false}
                allowClear={true}
                readOnly={module_access === "read_only"}
                value={
                  inputValues &&
                  inputValues.est_sales_date &&
                  !inputValues.est_sales_date?.includes("0000")
                    ? dayjs(inputValues.est_sales_date, date_format)
                    : undefined
                }
                disabled={
                  getStatusForField(loadingStatus, "est_sales_date") ===
                  "loading"
                }
                fixStatus={getStatusForField(loadingStatus, "est_sales_date")}
                onChange={(date, dateString) => {
                  handleUpdateField({
                    est_sales_date: dateString
                      ? String(date.format(date_format))
                      : null,
                  });
                }}
                onMouseLeave={() => {
                  handleChangeFieldStatus({
                    field: "est_sales_date",
                    status: "button",
                    action: "ML",
                  });
                }}
              />
            </li>
            <li>
              <DatePickerField
                label={_t("Bid Due")}
                name="bid_due_date"
                labelPlacement="left"
                placeholder="Select Date"
                editInline={true}
                iconView={true}
                format={date_format}
                required={false}
                allowClear={true}
                readOnly={module_access === "read_only"}
                value={
                  inputValues &&
                  inputValues.bid_due_date &&
                  !inputValues.bid_due_date?.includes("00")
                    ? dayjs(inputValues.bid_due_date, date_format)
                    : undefined
                }
                disabled={
                  getStatusForField(loadingStatus, "bid_due_date") === "loading"
                }
                fixStatus={getStatusForField(loadingStatus, "bid_due_date")}
                onChange={(date, dateString) => {
                  handleUpdateField({
                    bid_due_date: dateString
                      ? String(date.format(date_format))
                      : null,
                  });
                }}
                onMouseLeave={() => {
                  handleChangeFieldStatus({
                    field: "bid_due_date",
                    status: "button",
                    action: "ML",
                  });
                }}
              />
            </li>
            <li
              className={`overflow-hidden flex ${
                !isDatePVisible ? "hidden" : ""
              }`}
              ref={dtDivRef}
            >
              <InlineField
                label={_t("Start/End Date")}
                labelPlacement="left"
                field={
                  <div className="grid grid-cols-2 gap-1 items-center w-full">
                    <DatePickerField
                      label={""}
                      name="start_date"
                      labelPlacement="left"
                      placeholder={_t("Select Start Date")}
                      editInline={true}
                      iconView={true}
                      format={date_format}
                      required={false}
                      allowClear={true}
                      readOnly={module_access === "read_only"}
                      value={
                        inputValues && inputValues.start_date
                          ? dayjs(inputValues.start_date, date_format)
                          : undefined
                      }
                      disabled={
                        getStatusForField(loadingStatus, "start_date") ===
                        "loading"
                      }
                      fixStatus={getStatusForField(loadingStatus, "start_date")}
                      disabledDate={(current) => {
                        // Prevent selecting start_date greater than end_date
                        const endDate =
                          inputValues && inputValues.end_date
                            ? dayjs(inputValues.end_date, date_format)
                            : null;
                        return endDate ? current.isAfter(endDate) : false;
                      }}
                      onChange={(date) => {
                        handleUpdateField({
                          start_date: date
                            ? String(dayjs(date).format(date_format))
                            : "",
                        });
                        setIsDatePVisible(true);
                      }}
                      open={openStartDateCalender}
                      onOpenChange={setOpenStartDateCalender}
                      onMouseLeave={() => {
                        handleChangeFieldStatus({
                          field: "start_date",
                          status: "button",
                          action: "ML",
                        });
                      }}
                    />
                    <DatePickerField
                      label={""}
                      name="end_date"
                      labelPlacement="left"
                      placeholder={_t("Select End Date")}
                      editInline={true}
                      iconView={true}
                      format={date_format}
                      required={false}
                      allowClear={true}
                      readOnly={module_access === "read_only"}
                      value={
                        inputValues && inputValues.end_date
                          ? dayjs(inputValues.end_date, date_format)
                          : undefined
                      }
                      disabled={
                        getStatusForField(loadingStatus, "end_date") ===
                        "loading"
                      }
                      fixStatus={getStatusForField(loadingStatus, "end_date")}
                      disabledDate={(current) => {
                        // Prevent selecting end_date less than start_date
                        const startDate =
                          inputValues && inputValues.start_date
                            ? dayjs(inputValues.start_date, date_format)
                            : null;
                        return startDate ? current.isBefore(startDate) : false;
                      }}
                      onChange={(date) => {
                        handleUpdateField({
                          end_date: date
                            ? String(dayjs(date).format(date_format))
                            : "",
                        });
                        setIsEndDatePickerOpened(false);
                      }}
                      onMouseLeave={() => {
                        handleChangeFieldStatus({
                          field: "end_date",
                          status: "button",
                          action: "ML",
                        });
                      }}
                      onBlur={() => {
                        setIsEndDatePickerOpened(false);
                      }}
                    />
                  </div>
                }
              />
            </li>
            <li className={`overflow-hidden ${isDatePVisible ? "hidden" : ""}`}>
              <InlineField
                label={_t("Start/End Date")}
                labelPlacement="left"
                field={
                  <div className="relative w-full group/edit">
                    <InputField
                      labelPlacement="left"
                      placeholder={_t("Select Start/End Date")}
                      required={false}
                      editInline={true}
                      iconView={true}
                      readOnly={module_access === "read_only"}
                      readOnlyClassName="!h-[34px]"
                      value={
                        !!inputValues.start_date && !!inputValues.end_date
                          ? dayjs(inputValues.start_date, date_format).format(
                              date_format
                            ) +
                            " - " +
                            dayjs(inputValues.end_date, date_format).format(
                              date_format
                            )
                          : !!inputValues?.start_date
                          ? dayjs(inputValues.start_date, date_format).format(
                              date_format
                            )
                          : !!inputValues?.end_date
                          ? dayjs(inputValues.end_date, date_format).format(
                              date_format
                            )
                          : ""
                      }
                      fixStatus={getStatusForField(
                        loadingStatus,
                        "start_end_date"
                      )}
                      onChange={() => {}}
                      onFocus={() => {
                        setIsDatePVisible(true);
                        setOpenStartDateCalender(true);
                      }}
                      onMouseEnter={() => {
                        handleChangeFieldStatus({
                          field: "start_end_date",
                          status: "edit",
                          action: "ME",
                        });
                      }}
                      onMouseLeaveDiv={() => {
                        handleChangeFieldStatus({
                          field: "start_end_date",
                          status: "button",
                          action: "ML",
                        });
                      }}
                    />
                    {(!!inputValues.start_date || !!inputValues.end_date) &&
                    !(module_access === "read_only") &&
                    !["loading", "success", "error"].includes(
                      getStatusForField(loadingStatus, "start_end_date")
                    ) ? (
                      <div className="absolute z-10 top-1/2 -translate-y-1/2 right-2.5 opacity-0 group-hover/edit:opacity-100">
                        <FontAwesomeIcon
                          icon="fa-solid fa-circle-xmark"
                          className="text-[#00000040] hover:text-[#00000073] cursor-pointer w-3 h-3"
                          onClick={() => {
                            handleUpdateField({ start_date: "" });
                            handleUpdateField({ end_date: "" });
                            setInputValues((prevValues) => ({
                              ...prevValues,
                              start_date: "",
                              end_date: "",
                            }));
                          }}
                        />
                      </div>
                    ) : null}
                  </div>
                }
              />
            </li>
            <li className="overflow-hidden">
              <InlineField
                label={_t("Associated Estimate")}
                labelPlacement="left"
                field={
                  <div className="flex items-center w-full sm:max-w-[calc(100%-171px)]">
                    <div
                      className={`${
                        module_access === "read_only"
                          ? ""
                          : "hover:w-full focus-within:w-full focus:w-full"
                      } ${
                        !inputValues.associated_estimate_id ||
                        ["loading", "success", "error"].includes(
                          getStatusForField(
                            loadingStatus,
                            "associated_estimate_id"
                          )
                        )
                          ? "w-full"
                          : "max-w-[calc(100%-24px)]"
                      }`}
                    >
                      <SelectField
                        name="associated_estimate_id"
                        placeholder={_t("Select Estimate")}
                        labelPlacement="left"
                        value={
                          inputValues.associated_estimate_id
                            ? estimateDataOptions.filter((item) => {
                                return (
                                  String(inputValues.associated_estimate_id) ===
                                  String(item?.value)
                                );
                              })
                            : []
                        }
                        editInline={true}
                        iconView={true}
                        readOnly={module_access === "read_only"}
                        options={estimateDataOptions}
                        allowClear={true}
                        showSearch={true}
                        fixStatus={getStatusForField(
                          loadingStatus,
                          "associated_estimate_id"
                        )}
                        disabled={
                          isEstimateDataLoading ||
                          getStatusForField(
                            loadingStatus,
                            "associated_estimate_id"
                          ) === "loading"
                        }
                        optionFilterProp="children"
                        filterOption={(input, option) =>
                          filterOptionBySubstring(
                            input,
                            option?.label as string
                          )
                        }
                        onChange={(value: string | string[]) => {
                          // if (typeof value === "number") {
                          setInputValues({
                            ...inputValues,
                            associated_estimate_id: value,
                          });
                          // }
                        }}
                        onSelect={(e) => {
                          const value = e.toString();
                          if (
                            oppDetail &&
                            String(value) !==
                              String(oppDetail.associated_estimate_id)
                          ) {
                            handleUpdateField({
                              associated_estimate_id: value ? value : "",
                            });
                          } else {
                            handleChangeFieldStatus({
                              field: "associated_estimate_id",
                              status: "button",
                              action: "BLUR",
                            });
                          }
                        }}
                        onClear={() => {
                          handleUpdateField({
                            associated_estimate_id: "",
                          });
                          handleUpdateField({
                            associated_estimate_id: "",
                          });
                        }}
                      />
                    </div>
                    {estimateDataOptions.some(
                      (item) =>
                        String(item.value) ===
                        String(inputValues.associated_estimate_id)
                    ) && (
                      <EstimatesFieldRedirectionIcon
                        estimatesId={`${
                          inputValues.associated_estimate_id
                            ? inputValues.associated_estimate_id
                            : ""
                        }`}
                      />
                    )}
                  </div>
                }
              />
            </li>
          </ul>
        </div>
      }
    />
  );
};

export default OppotunitiesSalesDetails;
