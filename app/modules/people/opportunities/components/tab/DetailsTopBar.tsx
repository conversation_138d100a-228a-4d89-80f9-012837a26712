// Hooks
import { useTranslation } from "~/hook";
// React + ag-grid
import { useEffect, useMemo, useRef, useState } from "react";
// Atoms
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
import { Toolt<PERSON> } from "~/shared/components/atoms/tooltip";
import { Typography } from "~/shared/components/atoms/typography";
import { Dropdown } from "~/shared/components/atoms/dropDown";
import { Popover } from "~/shared/components/atoms/popover";
import { CheckBox } from "~/shared/components/atoms/checkBox";
import { Button } from "~/shared/components/atoms/button";
// Molecules
import { ButtonField } from "~/shared/components/molecules/buttonField";
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";
import { ContactDetailsButton } from "~/shared/components/molecules/contactDetailsButton";
import { IconButton } from "~/shared/components/molecules/iconButton";
import { InputField } from "~/shared/components/molecules/inputField";
import { ProgressBarHeader } from "~/shared/components/molecules/ProgressBarHeader";
import { TopBarSkeleton } from "~/shared/components/molecules/topBarSkeleton";
import { DirectoryFieldRedirectionIcon } from "~/shared/components/molecules/fieldRedirect/directoryFieldRedirectionIcon";
import { FieldStatus } from "~/shared/components/molecules/fieldStatus";
// Organisms
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";
// Other
import {
  getGConfig,
  setCommonSidebarCollapse,
  getGModuleDashboard,
  useGModules,
  getGSettings,
} from "~/zustand";
import { parseParamsFromURL } from "~/components/page/$url/helper";
import { useNavigate, useParams, useRevalidator } from "@remix-run/react";
import { oppDetailType, STATUS_MAP } from "../../utils/constants";
import { IOpportunitiesTopBarProps, IOpportunity } from "../../utils/types";
import { formatAmount, sanitizeString } from "~/helpers/helper";
import { useAppOPPDispatch, useAppOPPSelector } from "../../redux/store";
import {
  filterOptionBySubstring,
  getStatusForField,
} from "~/shared/utils/helper/common";
import escape from "lodash/escape";
import OpportunityTableActionsDetailPage from "../dashboard/OpportunitiesTableActionDetailPage";
import OpportunitiesTableDropdownItems from "../dashboard/OpportunitiesTableDropdownItems";
import {
  addOppCustomData,
  fetchOppActivity,
  fetchOppStageData,
} from "../../redux/action/opportunityDetailsActions";
import {
  addOppCustomDataAct,
  setOppDetailLoading,
  updateOppAddr,
} from "../../redux/slices/opportunityDetailsSlice";
import { SelectField } from "~/shared/components/molecules/selectField";
import { faSquare } from "@fortawesome/pro-solid-svg-icons";
import { addItemObject } from "~/modules/people/directory/utils/constasnts";
import { ConfirmModal } from "~/shared/components/molecules/confirmModal";
import { faClipboardListCheck } from "@fortawesome/pro-regular-svg-icons";
import { getGlobalUser } from "~/zustand/global/user/slice";
import { onEnterSelectSearchValue } from "~/shared/components/molecules/selectField/units";
import { resetPage } from "~/redux/slices/commonNoteSlice";
import { getCurrentMenuModule } from "~/zustand/global/config/slice";

const DetailsTopBar = ({
  sidebarCollapse,
  activeStep,
  setActiveStep,
  handleStepClick,
  loadingStatus,
  setIsOpenCustomer,
  customer,
  setIsOpenCustomerContactDetails,
  inputValues,
  handleUpdateField,
  setInputValues,
  handleChangeFieldStatus,
  contactAddress,
  setContactAddress,
}: IOpportunitiesTopBarProps) => {
  const { _t } = useTranslation();
  const { id: opportunity_id }: RouteParams = useParams(); // This type already declare.
  const currentModule = getCurrentMenuModule();

  const { module_key, module_access, page_is_iframe }: GConfig = getGConfig();
  const { is_custom_opportunity_id }: GSettings = getGSettings();
  const { checkModuleAccessByKey } = useGModules();
  const navigate = useNavigate();
  const isReadOnly = useMemo(
    () => checkModuleAccessByKey(module_key) === "read_only",
    [module_key]
  );
  const gModuleDashboard = getGModuleDashboard();
  const [isFocused, setIsFocused] = useState(false);
  const revalidator = useRevalidator();
  const user: IInitialGlobalData["user"] = getGlobalUser();
  const { allow_delete_module_items = "0" } = user || {};
  const dispatch = useAppOPPDispatch();
  const shareLinkModalOpen = useAppOPPSelector(
    (state) => state.dropdownOpportunity.shareLinkModelOpen
  );
  const confirmDialogOpen = useAppOPPSelector(
    (state) => state.dropdownOpportunity.confirmDialogOpen
  );
  const confirmArchiveDialogOpen = useAppOPPSelector(
    (state) => state.dropdownOpportunity.confirmArchiveDialogOpen
  );
  const { formatter } = useCurrencyFormatter();
  const [isStageSearchEnable, setIsStageSearchEnable] =
    useState<boolean>(false);
  const statuses = gModuleDashboard?.module_setting?.module_status;
  // const dispatch = useAppOPPDispatch();
  const {
    oppDetail,
    isOppDataLoading,
    stageDropDownData,
    isStageDropDownDataLoading,
    stageData,
  } = useAppOPPSelector((state) => {
    return state.opportunityDetails;
  });

  const statesToShow = useMemo(
    () =>
      (stageDropDownData || [])
        ?.filter((status) => {
          return (
            typeof status?.key === "string" &&
            status?.key?.startsWith("opportunity")
          );
        })
        .sort((a, b) => Number(a.sort_order) - Number(b.sort_order)),
    [stageDropDownData]
  );

  const statusNames = statesToShow?.map((item) => item.name);
  const [customDataAdd, setCustomDataAdd] = useState<ICommonCustomDataFrm>({});
  const [isConfirmDialogOpen, setIsConfirmDialogOpen] =
    useState<boolean>(false);
  const closeConfirmationModal = () => {
    previousOppVal.current = "";
    setIsConfirmDialogOpen(false);
  };
  const [isAddingCustomData, setIsAddingCustomData] = useState<boolean>(false);
  const previousOppVal = useRef("");

  const statusesKeyLabelMap = useMemo(() => {
    return (stageDropDownData?.reduce((acc, item) => {
      if (item.key && item.name) {
        acc[item.key] = item.name;
      }
      return acc;
    }, {} as Record<string, string>) || {}) as Record<string, string>;
  }, [stageDropDownData]);

  const filteredStates = useMemo(() => {
    if (String(inputValues.stage) === "opportunity_stage_awarded") {
      return stageDropDownData.map((item) => ({
        ...item,
        label: HTMLEntities.decode(sanitizeString(item.name)) || "",
        value: (item.key?.toString() as string) || item.item_id.toString(),
        default_color: item.status_color,
        icon: item.key
          ? STATUS_MAP[item.key as keyof typeof STATUS_MAP]?.icon ||
            "fa-regular fa-circle-pause"
          : "fa-regular fa-circle-pause",
        isActive: !(
          statusNames.indexOf(item.name || "") >
          statusNames.indexOf(
            statusesKeyLabelMap[inputValues?.stage?.toString() || ""] || ""
          )
        ),
      }));
    } else {
      return stageDropDownData
        .filter((item) => String(item.key) !== "opportunity_stage_awarded")
        .map((item) => ({
          ...item,
          key: item.key?.toString(),
          label: HTMLEntities.decode(sanitizeString(item.name)) || "",
          value: (item.key?.toString() as string) || item.item_id.toString(),
          default_color: item.status_color,
          icon: item.key
            ? STATUS_MAP[item.key as keyof typeof STATUS_MAP]?.icon ||
              "fa-regular fa-circle-pause"
            : "fa-regular fa-circle-pause",
          isActive: !(
            statusNames.indexOf(item.name || "") >
            statusNames.indexOf(
              statusesKeyLabelMap[inputValues?.stage?.toString() || ""] || ""
            )
          ),
        }));
    }
  }, [stageDropDownData, inputValues]);

  const fetchActivity = () => {
    dispatch(
      fetchOppActivity({
        id: oppDetail?.customer_id || "",
        requestFromModule: "opportunities",
        opportunityId: opportunity_id,
      })
    );
  };
  useEffect(() => {
    if (oppDetail?.customer_id) {
      fetchActivity();
    }
  }, [oppDetail?.customer_id]);

  const selectedStatus = useMemo(() => {
    if (inputValues?.stage) {
      if (
        filteredStates.some(
          (item) => item.key === inputValues?.stage?.toString()
        )
      ) {
        let data = filteredStates.find(
          (item) => item.key === inputValues?.stage?.toString()
        );
        return data && data.value === "opportunity_stage_awarded"
          ? {
              ...data,
              default_color: "#2235581d",
              icon: "fa-regular fa-door-open",
            }
          : data;
      } else if (inputValues?.stage_name) {
        return {
          label: oppDetail?.is_stage_archive
            ? inputValues?.stage_name + " (Archived)"
            : inputValues?.stage_name,
          value: inputValues?.stage?.toString(),
          default_color: "bg-primary-900",
          icon: "fa-regular fa-door-open",
        };
      }
    }
  }, [filteredStates, inputValues]);

  const status = useMemo(() => {
    const statusList = filteredStates?.map((item) => ({
      label: HTMLEntities.decode(sanitizeString(item.label)),
      key: item?.key?.toString() ?? "",
      icon: (
        <FontAwesomeIcon
          icon="fa-solid fa-square"
          className="h-3.5 w-3.5"
          style={{
            color: item?.default_color,
          }}
        />
      ),
    }));

    const selectStatus = (
      <Tooltip title={selectedStatus?.label}>
        <div
          className={`py-0.5 rounded flex items-center justify-center w-full status-dropdown-block group/status-dropdown px-2.5 ${
            isReadOnly ? "" : "hover:px-1 cursor-pointer"
          }`}
          style={{
            backgroundColor:
              selectedStatus?.value === "opportunity_stage_awarded"
                ? "#2235581d"
                : selectedStatus?.default_color + "1d",
          }}
        >
          <Typography
            style={{
              color:
                selectedStatus?.value === "opportunity_stage_awarded"
                  ? "#223558"
                  : selectedStatus?.default_color,
            }}
            className="text-xs whitespace-nowrap truncate"
          >
            {selectedStatus?.label}
          </Typography>
          {!isReadOnly && (
            <FontAwesomeIcon
              className="w-2.5 h-2.5 pl-0.5 group-hover/status-dropdown:flex hidden ease-in-out duration-300"
              style={{
                color: selectedStatus?.default_color,
              }}
              icon="fa-regular fa-chevron-down"
            />
          )}
        </div>
      </Tooltip>
    );
    return { statusList, selectStatus };
  }, [filteredStates, selectedStatus]);

  const progressBarStates = useMemo(() => {
    return filteredStates?.filter(
      (item) => !!Number(item.show_in_progress_bar)
    );
  }, [filteredStates]);

  const canShowProgressBar = useMemo(() => {
    return (
      progressBarStates.filter(
        (item) => item.key?.toString() === oppDetail?.stage?.toString()
      ).length > 0
    );
  }, [progressBarStates, oppDetail]);

  const [checkboxState, setCheckboxState] = useState({
    address: true,
    qualityScore: true,
    stage: true,
    referralSource: true,
    estimatedValue: true,
    estimatedSalesDate: true,
  });

  const handleCheckboxChange = (field: keyof typeof checkboxState) => {
    setCheckboxState((prev) => ({ ...prev, [field]: !prev[field] }));
  };

  const copyToClipboard = () => {
    if (inputValues) {
      const updatedFields: Record<string, any> = {};

      if (checkboxState.address) {
        dispatch(
          updateOppAddr({
            address1: customer.address1,
            address2: customer.address2,
            city: customer.city,
            state: customer.state,
            zip: customer.zip,
          })
        );
        updatedFields.address1 = customer.address1 ? customer.address1 : "";
        updatedFields.address2 = customer.address2 ? customer.address2 : "";
        updatedFields.city = customer.city ? customer.city : "";
        updatedFields.state = customer.state ? customer.state : "";
        updatedFields.zip = customer.zip ? customer.zip : "";
      }
      if (checkboxState.qualityScore) {
        updatedFields.score = customer.quality ? customer?.quality : "0";
      }
      if (checkboxState.stage) {
        if (
          isNaN(Number(customer.stage)) &&
          customer.stage &&
          customer.orig_type &&
          !(customer.stage === "lead_stage_awarded")
        ) {
          updatedFields.stage = customer.stage.replace("lead", "opportunity");
        } else if (!customer.stage && customer.orig_type) {
          updatedFields.stage = "";
        } else {
          updatedFields.stage = "opportunity_stage_received";
        }
      }
      if (checkboxState.referralSource) {
        if (
          isNaN(Number(customer.referral_source)) &&
          customer.referral_source
        ) {
          updatedFields.referral_source =
            customer.referral_source.toString() === "angi_leads"
              ? "opportunity_angi_leads"
              : customer.referral_source.replace("lead", "opportunity");
        } else {
          updatedFields.referral_source = "";
        }
      }
      if (checkboxState.estimatedValue) {
        updatedFields.budget_amount = Number(customer.lead_value)
          ? (Number(customer?.lead_value) / 100).toString()
          : null;
      }
      if (checkboxState.estimatedSalesDate) {
        updatedFields.est_sales_date = customer.estimate_sales_date
          ? customer?.estimate_sales_date
          : "";
      }

      setInputValues({ ...inputValues, ...updatedFields });
      handleUpdateField(updatedFields);
    }
    setContactAddress(false);
    setCheckboxState({
      address: true,
      qualityScore: true,
      stage: true,
      referralSource: true,
      estimatedValue: true,
      estimatedSalesDate: true,
    });
  };

  const requiredFieldExceptionFilter = (fieldName: string, field: string) => {
    notification.error({ description: `${fieldName} field is required.` });

    setInputValues({
      ...inputValues,
      [field]: oppDetail?.[field as keyof IOpportunity],
    });
  };

  useEffect(() => {
    if (revalidator.state === "loading") {
      dispatch(setOppDetailLoading(true));
    } else {
      dispatch(setOppDetailLoading(false));
    }
  }, [revalidator.state]);

  const handlekeyDown = (
    event: React.KeyboardEvent<HTMLInputElement | HTMLTextAreaElement>,
    itemType: number
  ) => {
    if (event.key === "Enter") {
      const value = event?.currentTarget?.value?.trim();
      const newType = onEnterSelectSearchValue(event, filteredStates || []);
      if (newType) {
        previousOppVal.current = event.currentTarget.value;
        setCustomDataAdd({
          itemType,
          name: HTMLEntities.encode(event?.currentTarget?.value),
        });
        setIsConfirmDialogOpen(true);
      } else if (value) {
        notification.error({
          description: "Records already exist, no new records were added.",
        });
      }
    }
  };

  const handleAddCustomData = async () => {
    if (customDataAdd?.name?.includes("\\")) {
      const availableOption = customDataAdd?.name.split("\\")[0];
      let isLabelAvailable = false;
      isLabelAvailable = filteredStates?.some(
        (type: Option) =>
          type?.label.toLowerCase().trim() ===
          availableOption.toLowerCase().trim()
      );

      if (isLabelAvailable) {
        notification.error({
          description: "Records already exist - no new records were added.",
        });

        previousOppVal.current = "";
        setInputValues((prev: IOpportunity) => ({
          ...prev,
          stage: prev.stage,
        }));

        setIsConfirmDialogOpen(false);
        return;
      }
    }
    if (!isAddingCustomData && customDataAdd?.name) {
      setIsAddingCustomData(true);

      const cDataRes = (await addOppCustomData({
        itemType: customDataAdd?.itemType,
        name: customDataAdd?.name,
      })) as ICustomDataAddUpRes;

      if (cDataRes?.success) {
        dispatch(addOppCustomDataAct(cDataRes?.data));
        let newData = {};
        newData = { stage: cDataRes?.data?.item_id };

        setInputValues({
          ...inputValues,
          ...newData,
        });
        handleUpdateField({ ...newData, module_key });
        setIsConfirmDialogOpen(false);
        previousOppVal.current = "";
      } else {
        notification.error({ description: cDataRes.message });
        previousOppVal.current = "";
      }
      setIsAddingCustomData(false);
    }
  };

  return (
    <>
      <div className="sticky top-0 z-[99] bg-[#F8F8F9] p-[15px] pb-0 mb-[15px]">
        <div className="flex items-center bg-white dark:bg-dark-800 py-[5px] px-3.5 shadow-[0_4px_24px_0] shadow-[#22292f1a] rounded-md">
          <div className="w-full flex md:flex-row flex-col-reverse md:items-center justify-between sm:gap-2 gap-1.5">
            {isOppDataLoading ? (
              <TopBarSkeleton statusList={canShowProgressBar} num={5} />
            ) : (
              <>
                <div className="flex items-center xl:flex-[1_0_0%] !mr-auto xl:w-[calc(35%-190px)] md:w-[calc(100%-150px)] w-full">
                  {selectedStatus && (
                    <ProgressBarHeader
                      option={selectedStatus}
                      isActive
                      hideName
                    />
                  )}
                  <div className="flex flex-col gap-0.5 w-[calc(100%-44px)] pl-2.5">
                    <Tooltip
                      title={
                        inputValues?.project_name
                          ? `Title: ${HTMLEntities.decode(
                              sanitizeString(inputValues?.project_name)
                            )}`
                          : ""
                      }
                      placement="topLeft"
                    >
                      <InputField
                        placeholder={_t("Title")}
                        labelPlacement="left"
                        editInline={true}
                        iconView={true}
                        name="project_name"
                        readOnly={module_access === "read_only"}
                        value={HTMLEntities.decode(
                          sanitizeString(inputValues.project_name)
                        )}
                        fixStatus={getStatusForField(
                          loadingStatus,
                          "project_name"
                        )}
                        onChange={({ target: { name, value } }) => {
                          setInputValues({ ...inputValues, [name]: value });
                        }}
                        onMouseEnter={() => {
                          handleChangeFieldStatus({
                            field: "project_name",
                            status: "edit",
                            action: "ME",
                          });
                        }}
                        onMouseLeaveDiv={() => {
                          handleChangeFieldStatus({
                            field: "project_name",
                            status: "button",
                            action: "ML",
                          });
                        }}
                        onFocus={() =>
                          handleChangeFieldStatus({
                            field: "project_name",
                            status: "save",
                            action: "FOCUS",
                          })
                        }
                        onBlur={(e) => {
                          const value = e.target.value.trim();
                          if (value) {
                            if (
                              value !==
                              HTMLEntities.decode(
                                sanitizeString(oppDetail?.project_name)
                              )
                            ) {
                              handleUpdateField({
                                project_name: escape(value),
                              });
                            } else {
                              handleChangeFieldStatus({
                                field: "project_name",
                                status: "button",
                                action: "BLUR",
                              });
                              setInputValues({
                                ...inputValues,
                                project_name: oppDetail.project_name,
                              });
                            }
                          } else {
                            requiredFieldExceptionFilter(
                              "Title",
                              "project_name"
                            );
                          }
                        }}
                        className="h-6 py-0 text-base font-medium"
                        formInputClassName="ellipsis-input-field"
                        readOnlyClassName="text-base h-6 !font-medium whitespace-nowrap truncate sm:block flex"
                        inputStatusClassName="!w-4 !h-4"
                      />
                    </Tooltip>
                    <Popover
                      overlayClassName="!top-[187px]"
                      content={
                        <div className="min-w-[272px]">
                          <Typography className="block text-sm py-2 px-3.5 rounded-t-lg bg-gray-50 relative before:w-full before:h-px before:bg-[linear-gradient(177deg,#a5a5a53d_24%,#fafafa_100%)] before:bottom-0 before:left-1/2 before:-translate-x-1/2 before:absolute">
                            {_t(
                              `Copy Contact Address to ${
                                currentModule?.singular_name || "Opportunity"
                              } Address?`
                            )}
                          </Typography>
                          <div className="py-2 px-3.5">
                            <div>
                              <div>
                                <Typography className="flex items-center text-13">
                                  <CheckBox
                                    className="gap-1.5 text-primary-900 font-medium"
                                    name="address"
                                    checked={checkboxState.address}
                                    onChange={() =>
                                      handleCheckboxChange("address")
                                    }
                                  >
                                    {_t("Address:")}
                                  </CheckBox>
                                  <Typography> {customer.address1} </Typography>
                                  {customer.address2 && (
                                    <Typography>
                                      {customer.address1 ? ", " : ""}
                                      {customer.address2}
                                    </Typography>
                                  )}
                                  {customer.city && (
                                    <Typography>
                                      {customer.address1 || customer.address2
                                        ? ", "
                                        : ""}
                                      {customer.city}
                                    </Typography>
                                  )}
                                  {customer.state && (
                                    <Typography>
                                      {customer.address1 ||
                                      customer.address2 ||
                                      customer.city
                                        ? ", "
                                        : ""}
                                      {customer.state}
                                    </Typography>
                                  )}
                                  {customer.zip && (
                                    <Typography>
                                      {customer.address1 ||
                                      customer.address2 ||
                                      customer.city ||
                                      customer.state
                                        ? ", "
                                        : ""}
                                      {customer.zip}
                                    </Typography>
                                  )}

                                  {!customer.address1 &&
                                    !customer.address2 &&
                                    !customer.city &&
                                    !customer.state &&
                                    !customer.zip && (
                                      <Typography>
                                        {"-"}
                                        {customer.zip}
                                      </Typography>
                                    )}
                                </Typography>
                                <Typography className="flex items-center text-13">
                                  <CheckBox
                                    className="gap-1.5 text-primary-900 font-medium"
                                    name="qualityScore"
                                    checked={checkboxState.qualityScore}
                                    onChange={() =>
                                      handleCheckboxChange("qualityScore")
                                    }
                                  >
                                    {_t("Score:")}
                                  </CheckBox>
                                  <Typography>
                                    {Number(customer.quality)
                                      ? customer.quality
                                      : "-"}
                                  </Typography>
                                </Typography>
                                <Typography className="flex items-center text-13">
                                  <CheckBox
                                    className="gap-1.5 text-primary-900 font-medium"
                                    name="stage"
                                    checked={checkboxState.stage}
                                    onChange={() =>
                                      handleCheckboxChange("stage")
                                    }
                                  >
                                    {_t("Stage:")}
                                  </CheckBox>
                                  <Typography>
                                    {customer.stage &&
                                    !(
                                      customer.stage === "lead_stage_awarded"
                                    ) &&
                                    customer.orig_type &&
                                    isNaN(Number(customer.stage))
                                      ? customer.stage_name
                                      : (!customer.stage ||
                                          !customer.stage_name) &&
                                        customer.orig_type
                                      ? "-"
                                      : "Received"}
                                  </Typography>
                                </Typography>
                                {isNaN(Number(customer.referral_source)) &&
                                  customer.referral_source && (
                                    <Typography className="flex items-center text-13">
                                      <CheckBox
                                        className="gap-1.5 text-primary-900 font-medium"
                                        name="referralSource"
                                        checked={checkboxState.referralSource}
                                        onChange={() =>
                                          handleCheckboxChange("referralSource")
                                        }
                                      >
                                        {_t("Referral Source:")}
                                      </CheckBox>
                                      <Typography>
                                        {customer.referral_source_name
                                          ? customer.referral_source_name
                                          : "-"}
                                      </Typography>
                                    </Typography>
                                  )}
                                <Typography className="flex items-center text-13">
                                  <CheckBox
                                    className="gap-1.5 text-primary-900 font-medium"
                                    name="estimatedValue"
                                    checked={checkboxState.estimatedValue}
                                    onChange={() =>
                                      handleCheckboxChange("estimatedValue")
                                    }
                                  >
                                    {_t("Estimated Value:")}
                                  </CheckBox>
                                  <Typography>
                                    {customer.lead_value
                                      ? formatter(
                                          formatAmount(
                                            Number(
                                              Number(customer.lead_value) / 100
                                            )
                                          )
                                        ).value_with_symbol
                                      : "-"}
                                  </Typography>
                                </Typography>
                                <Typography className="flex items-center text-13">
                                  <CheckBox
                                    className="gap-1.5 text-primary-900 font-medium"
                                    name="estimatedSalesDate"
                                    checked={checkboxState.estimatedSalesDate}
                                    onChange={() =>
                                      handleCheckboxChange("estimatedSalesDate")
                                    }
                                  >
                                    {_t("Est. Sales Date:")}
                                  </CheckBox>
                                  <Typography>
                                    {customer.estimate_sales_date
                                      ? customer.estimate_sales_date
                                      : "-"}
                                  </Typography>
                                </Typography>
                              </div>
                            </div>

                            <div className="flex gap-2 justify-center mt-3">
                              <Button
                                type="primary"
                                className="w-fit"
                                onClick={copyToClipboard}
                              >
                                {_t("Yes")}
                              </Button>
                              <Button onClick={() => setContactAddress(false)}>
                                {_t("No")}
                              </Button>
                            </div>
                          </div>
                        </div>
                      }
                      placement="bottomLeft"
                      trigger="click"
                      open={contactAddress}
                      onOpenChange={(newOpen: boolean) => {
                        setContactAddress(newOpen);
                      }}
                    />
                    <ButtonField
                      labelProps={{
                        labelClass: "!hidden",
                      }}
                      placeholder={_t("Select Customer")}
                      name="customer_id"
                      labelPlacement="left"
                      editInline={true}
                      iconView={true}
                      onClick={() => {
                        setIsOpenCustomer(true);
                      }}
                      mainReadOnlyClassName="sm:w-fit max-w-full"
                      className="h-6 py-0 w-full gap-0"
                      readOnlyClassName="text-sm h-6 !font-medium whitespace-nowrap truncate sm:block flex"
                      inputClassName="w-fit"
                      fieldClassName="w-auto"
                      spanWidthClass="w-fit"
                      buttonClassName="!text-sm font-medium"
                      required={true}
                      statusProps={{
                        className: "right-6",
                        iconProps: {
                          className: "!w-[15px] !h-[15px]",
                        },
                        status: getStatusForField(loadingStatus, "customer_id"),
                      }}
                      value={HTMLEntities.decode(
                        sanitizeString(customer.display_name)
                      )}
                      headerTooltip={`Contact: ${HTMLEntities.decode(
                        sanitizeString(customer.display_name)
                      )}`}
                      isDisabled={
                        isReadOnly ||
                        getStatusForField(loadingStatus, "customer_id") ===
                          "loading"
                      }
                      rightIcon={
                        customer.user_id && customer.display_name ? (
                          <div className="flex gap-1 items-center">
                            <ContactDetailsButton
                              onClick={() => {
                                setIsOpenCustomerContactDetails(true);
                              }}
                            />
                            {customer.user_id && (
                              <DirectoryFieldRedirectionIcon
                                className="!w-5 !h-5"
                                directoryId={customer.user_id.toString()}
                                directoryTypeKey={
                                  customer?.type_key !== "contact"
                                    ? customer?.type_key || ""
                                    : customer?.parent_type_key || ""
                                }
                              />
                            )}
                          </div>
                        ) : (
                          <></>
                        )
                      }
                    />
                    <div className="flex items-center gap-2">
                      <div className="flex gap-2 items-center">
                        <Tooltip
                          title={
                            filteredStates?.find(
                              (stageItem) => stageItem.key == inputValues.stage
                            )?.label
                          }
                        >
                          <SelectField
                            labelPlacement="left"
                            placeholder={_t("Select Stage")}
                            formInputClassName="w-fit overflow-visible"
                            containerClassName="overflow-visible"
                            fieldClassName="before:hidden w-fit"
                            className="h-[21px] directory-stage header-select-status-dropdown !rounded"
                            popupClassName="min-w-[260px]"
                            readOnly={isReadOnly}
                            showSearch={isStageSearchEnable}
                            onDropdownVisibleChange={(open) => {
                              if (open) {
                                setIsStageSearchEnable(true);
                              } else {
                                setIsStageSearchEnable(false);
                              }
                            }}
                            title=""
                            options={filteredStates}
                            optionRender={(oriOption) => {
                              const option = filteredStates?.find(
                                (item) => item.value === oriOption.value
                              );
                              return (
                                <div className="flex items-center gap-2">
                                  <FontAwesomeIcon
                                    icon={faSquare}
                                    className="h-3.5 w-3.5"
                                    style={{
                                      color: option?.status_color,
                                    }}
                                  />
                                  <span>{option?.label}</span>
                                </div>
                              );
                            }}
                            addItem={addItemObject}
                            onClear={() => {
                              handleChangeFieldStatus({
                                field: "stage",
                                status: "loading",
                                action: "API",
                              });
                              handleUpdateField({
                                stage: "",
                              });
                            }}
                            value={
                              previousOppVal && previousOppVal.current
                                ? {
                                    label: `${previousOppVal.current.trim()}`,
                                    value: "",
                                  }
                                : !isStageDropDownDataLoading && inputValues
                                ? inputValues.stage
                                  ? filteredStates.filter((item) => {
                                      return (
                                        String(inputValues.stage) ===
                                        String(item?.value)
                                      );
                                    }).length
                                    ? filteredStates.filter((item) => {
                                        return (
                                          String(inputValues.stage) ===
                                          String(item?.value)
                                        );
                                      })
                                    : inputValues.stage_name
                                    ? {
                                        label: `${
                                          inputValues.stage_name
                                            ? oppDetail?.is_stage_archive
                                              ? HTMLEntities.decode(
                                                  sanitizeString(
                                                    inputValues.stage_name.trim()
                                                  )
                                                ) + " (Archived)"
                                              : HTMLEntities.decode(
                                                  sanitizeString(
                                                    inputValues.stage_name.trim()
                                                  )
                                                )
                                            : ""
                                        }`,
                                        value: inputValues?.stage,
                                      }
                                    : []
                                  : []
                                : []
                            }
                            filterOption={(input, option) =>
                              filterOptionBySubstring(
                                input,
                                option?.label
                                  ?.toString()
                                  ?.toLowerCase() as string
                              )
                            }
                            onChange={(value: string | string[], event) => {
                              setInputValues({
                                ...inputValues,
                                stage: value,
                              });
                            }}
                            onSelect={(e, selectedValue) => {
                              if (selectedValue.key !== inputValues?.stage) {
                                const isValid = filteredStates.find(
                                  (item) => item.value === selectedValue.key
                                );
                                if (isValid) {
                                  handleUpdateField({
                                    stage: selectedValue.key,
                                    module_key,
                                  });
                                } else {
                                  notification.error({
                                    description: "You cannot set this status",
                                  });
                                }
                              }
                            }}
                            onInputKeyDown={(e) => {
                              handlekeyDown(e, oppDetailType.stageId);
                            }}
                            style={{
                              backgroundColor:
                                !!filteredStates?.find(
                                  (item) => item.key == inputValues?.stage
                                )?.status_color &&
                                filteredStates?.find(
                                  (item) => item.key == inputValues?.stage
                                )?.value !== "opportunity_stage_awarded"
                                  ? `${
                                      filteredStates?.find(
                                        (item) => item.key == inputValues?.stage
                                      )?.status_color
                                    }1d`
                                  : "#2235581d",
                              color:
                                !!filteredStates?.find(
                                  (item) => item.key == inputValues?.stage
                                )?.status_color &&
                                filteredStates?.find(
                                  (item) => item.key == inputValues?.stage
                                )?.value !== "opportunity_stage_awarded"
                                  ? `${
                                      filteredStates?.find(
                                        (item) => item.key == inputValues?.stage
                                      )?.status_color
                                    }`
                                  : "#223558",
                            }}
                          />
                        </Tooltip>

                        {["loading", "success", "error"].includes(
                          getStatusForField(loadingStatus, "stage")
                        ) && (
                          <FieldStatus
                            className="flex items-center"
                            iconProps={{
                              className: "!w-[15px] !h-[15px]",
                            }}
                            status={getStatusForField(loadingStatus, "stage")}
                          />
                        )}
                      </div>
                      <div className="w-full max-w-[250px]">
                        <Tooltip
                          title={
                            isFocused
                              ? HTMLEntities.decode(
                                  sanitizeString(
                                    inputValues?.project_id
                                      ? String(inputValues.project_id)?.trim()
                                      : ""
                                  )
                                )
                              : `Opp. #${HTMLEntities.decode(
                                  sanitizeString(
                                    inputValues?.project_id
                                      ? String(inputValues.project_id)?.trim()
                                      : ""
                                  )
                                )}`
                          }
                          placement="topLeft"
                        >
                          <InputField
                            placeholder={_t("Opp.") + " #"}
                            labelPlacement="left"
                            editInline={true}
                            iconView={true}
                            name={`project_id`}
                            readOnly={module_access === "read_only"}
                            className="h-[22px] py-0 text-13 font-medium"
                            disabled={is_custom_opportunity_id === 0}
                            value={
                              isFocused
                                ? HTMLEntities.decode(
                                    sanitizeString(
                                      oppDetail?.project_id
                                        ? String(inputValues?.project_id)
                                        : ""
                                    )
                                  )
                                : `Opp. #${HTMLEntities.decode(
                                    sanitizeString(
                                      inputValues?.project_id
                                        ? String(inputValues.project_id)
                                        : ""
                                    )
                                  )}`
                            }
                            fixStatus={getStatusForField(
                              loadingStatus,
                              "project_id"
                            )}
                            onChange={({
                              target: { value, name },
                            }: React.ChangeEvent<
                              HTMLInputElement | HTMLTextAreaElement
                            >) => {
                              setInputValues({
                                ...inputValues,
                                [name]: value,
                              });
                            }}
                            onMouseEnter={() => {
                              handleChangeFieldStatus({
                                field: "project_id",
                                status: "edit",
                                action: "ME",
                              });
                            }}
                            onMouseLeaveDiv={() => {
                              handleChangeFieldStatus({
                                field: "project_id",
                                status: "button",
                                action: "ML",
                              });
                            }}
                            onFocus={() => {
                              setIsFocused(true);
                              handleChangeFieldStatus({
                                field: "project_id",
                                status: "save",
                                action: "FOCUS",
                              });
                            }}
                            onBlur={(e) => {
                              setIsFocused(false);
                              const value = e.target.value.trim();
                              if (value) {
                                if (
                                  String(value) !==
                                    HTMLEntities.decode(
                                      sanitizeString(
                                        String(oppDetail?.project_id)
                                      )
                                    ) &&
                                  String(value) !==
                                    HTMLEntities.decode(
                                      sanitizeString(
                                        String(oppDetail?.project_id)
                                      )
                                    )
                                ) {
                                  handleUpdateField({
                                    project_id: escape(value),
                                  });
                                } else {
                                  handleChangeFieldStatus({
                                    field: "project_id",
                                    status: "button",
                                    action: "BLUR",
                                  });
                                  setInputValues({
                                    ...inputValues,
                                    [e.target.name]:
                                      oppDetail && oppDetail.project_id
                                        ? oppDetail.project_id
                                        : "",
                                  });
                                }
                              } else {
                                requiredFieldExceptionFilter(
                                  "Opp. #",
                                  "project_id"
                                );
                              }
                            }}
                          />
                        </Tooltip>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="flex-[0_0_auto] w-auto">
                  {canShowProgressBar ? (
                    <ul className="items-center justify-center 2xl:-ml-3.5 w-[calc(100%-0px)] xl:flex hidden">
                      {progressBarStates?.map((items) => {
                        return (
                          <li
                            className={`relative 2xl:min-w-[125px] xl:min-w-24 lg:w-24 w-20 grid justify-end first:before:hidden before:absolute before:h-[2px] before:!w-[calc(100%-54px)]  ${
                              items.isActive
                                ? "before:bg-primary-900"
                                : "before:bg-[#ACAEAF]"
                            } before:top-[30%] lg:before:left-[-22px] before:left-[-13px]`}
                          >
                            <ProgressBarHeader
                              option={items}
                              isActive={items.isActive}
                              onClick={(option) => {
                                if (
                                  oppDetail?.stage !== option.value &&
                                  option.value &&
                                  !isReadOnly
                                ) {
                                  const isValid = filteredStates.find(
                                    (item) => item.value === option.value
                                  );
                                  if (isValid) {
                                    handleUpdateField({
                                      stage: option.value,
                                      module_key,
                                    });
                                  } else {
                                    notification.error({
                                      description: "You cannot set this status",
                                    });
                                  }
                                }
                              }}
                            />
                          </li>
                        );
                      })}
                    </ul>
                  ) : null}
                </div>
                <div className="xl:flex-[1_0_0%] xl:w-[calc(35%-190px)] md:w-fit w-full">
                  <div className="flex justify-between">
                    <div className="flex gap-2.5">
                      {!window.ENV.PAGE_IS_IFRAME && (
                        <div
                          className="flex items-center cursor-pointer md:!hidden"
                          onClick={() => {
                            const params: Partial<IframeRouteParams> =
                              parseParamsFromURL(window?.location?.pathname);
                            if (params?.page && params?.id) {
                              navigate("/" + params?.page);
                            }
                          }}
                        >
                          <IconButton
                            htmlType="button"
                            variant="default"
                            className="md:!hidden group/module-menu relative w-[34px] h-[34px] !border-0 !shadow-[0px_1px_3px] !shadow-primary-200 hover:!bg-deep-orange-500/5"
                            iconClass="text-primary-900 group-hover/module-menu:text-deep-orange-500"
                            icon="fa-regular fa-chevron-left"
                          />
                        </div>
                      )}
                      <div>
                        <IconButton
                          htmlType="button"
                          variant="default"
                          className="md:!hidden group/module-menu relative w-[34px] h-[34px] !border-0 !shadow-[0px_1px_3px] !shadow-primary-200 hover:!bg-deep-orange-500/5"
                          iconClass="text-primary-900 group-hover/module-menu:text-deep-orange-500"
                          icon="fa-regular fa-bars"
                          onClick={() =>
                            setCommonSidebarCollapse(!sidebarCollapse)
                          }
                        />
                      </div>
                    </div>
                    <ul className="flex justify-end gap-2.5">
                      <li>
                        <ButtonWithTooltip
                          tooltipTitle={_t("Refresh")}
                          tooltipPlacement="top"
                          icon="fa-regular fa-arrow-rotate-right"
                          iconClassName="!text-primary-900 group-hover/buttonHover:!text-deep-orange-500"
                          className="!w-[34px] !h-[34px] !shadow-[0px_1px_3px] !shadow-primary-200 hover:!bg-deep-orange-500/5"
                          onClick={() => {
                            revalidator.revalidate();
                            fetchActivity();
                            dispatch(resetPage());
                          }}
                        />
                      </li>

                      {module_access !== "read_only" && (
                        <li>
                          <OpportunityTableActionsDetailPage
                            data={oppDetail}
                            dispatch={dispatch}
                            tooltipcontent={_t("More")}
                            iconClassName="text-primary-900 group-hover/buttonHover:text-deep-orange-500"
                            isDeleteAccess={
                              allow_delete_module_items === "0" ||
                              page_is_iframe
                            }
                            buttonClass="!w-[34px] !h-[34px] !shadow-[0px_1px_3px] !rounded !shadow-primary-200 hover:!bg-deep-orange-500/5"
                            handleUpdateField={handleUpdateField}
                          />
                          <OpportunitiesTableDropdownItems
                            data={oppDetail}
                            dispatch={dispatch}
                            // tableRef={agGridRef}
                            // callApiAgain={callApiAgain}
                            shareLinkModalOpen={shareLinkModalOpen}
                            confirmDialogOpen={confirmDialogOpen}
                            confirmArchiveDialogOpen={confirmArchiveDialogOpen}
                            isNavigate={true}
                          />
                        </li>
                      )}
                    </ul>
                  </div>
                </div>
              </>
            )}
          </div>
        </div>
      </div>
      {isConfirmDialogOpen && (
        <ConfirmModal
          isOpen={isConfirmDialogOpen}
          modalIcon={faClipboardListCheck}
          modaltitle={_t("Add Option To List")}
          description={_t(
            `This will add "${HTMLEntities.decode(
              sanitizeString(customDataAdd?.name?.trim() || "")
            )}" to the list. Do you want to add it?`
          )}
          isLoading={isAddingCustomData}
          onCloseModal={closeConfirmationModal}
          onAccept={() => {
            handleAddCustomData();
          }}
          onDecline={() => {
            previousOppVal.current = "";
            closeConfirmationModal();
          }}
        />
      )}
    </>
  );
};

export default DetailsTopBar;
