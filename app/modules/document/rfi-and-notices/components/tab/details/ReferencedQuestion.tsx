import { useTranslation } from "~/hook";
import delay from "lodash/delay";
// molecules
import { TextAreaField } from "~/shared/components/molecules/textAreaField";
import { CrudCommonCard } from "~/shared/components/molecules/crudCommonCard";
import { useEffect, useRef, useState } from "react";
import { RFIDetailsField } from "../../../utils/constasnts";
import { RFIFieldStatus } from "~/constants/rfi-and-notices";
import { useAppRFIDispatch, useAppRFISelector } from "../../../redux/store";
import { updateRFIDetails } from "../../../redux/slices/rfiDetailSlice";
import {
  listChangeOrders,
  updateRfiDetailsApi,
} from "../../../redux/action/rfiDetailAction";
import { useParams } from "@remix-run/react";
import { getStatusActionForField } from "~/shared/utils/helper/common";
import { InlineField } from "~/shared/components/molecules/inlineField";
import { SelectField } from "~/shared/components/molecules/selectField";
import { InputField } from "~/shared/components/molecules/inputField";
import { ChangeOrderlFieldRedirectionIcon } from "~/shared/components/molecules/fieldRedirect/changeOrderlFieldRedirectionIcon";

export const getStatusForField = (
  loadingStatus: IFieldStatus[],
  fieldName: string
): IStatus => {
  const itemField = loadingStatus.find(
    (item: IFieldStatus) => item && item.field === fieldName
  );
  if (itemField && itemField.status) {
    return itemField.status;
  }
  return "button";
};

const ReferencedQuestion = ({ isReadOnly }: { isReadOnly: boolean }) => {
  const { _t } = useTranslation();
  const dispatch = useAppRFIDispatch();
  const { tab, id: RFI_Id }: RouteParams = useParams();
  const descriptionFieldRef = useRef<HTMLInputElement>(null);
  const [inputValues, setInputValues] =
    useState<Partial<IRFIDetails>>(RFIDetailsField);
  const loadingStatusRef = useRef(RFIFieldStatus);
  const [loadingStatus, setLoadingStatus] =
    useState<Array<IFieldStatus>>(RFIFieldStatus);
  const { rfiDetail }: IRFIInitialState = useAppRFISelector(
    (state) => state.rfiDetail
  );
  const [changeOrderData, setChangeOrderData] = useState<IProjectChangeOrder[]>(
    []
  );
  const handleChangeFieldStatus = ({ field, status, action }: IFieldStatus) => {
    const checkStatus = loadingStatus.find(
      (item: IFieldStatus) => item?.field === field
    );

    if (
      !(
        (checkStatus?.status === "loading" ||
          checkStatus?.status === "success" ||
          checkStatus?.status === "save") &&
        (action === "ME" || action === "ML")
      )
    ) {
      setLoadingStatus((prevState: IFieldStatus[]) => {
        const newState = prevState.map((item) =>
          item.field === field ? { ...item, status: status, action } : item
        );
        loadingStatusRef.current = newState;
        return newState;
      });
    }
  };

  const handleUpdateField = async (data: Partial<IRFIDetails>) => {
    const field = Object.keys(data)[0] as keyof IRFIDetails;

    handleChangeFieldStatus({
      field: field,
      status: "loading",
      action: "API",
    });

    const updateRes = (await updateRfiDetailsApi({
      correspondence_id: RFI_Id,
      correspondence_date: rfiDetail.correspondence_date,
      correspondence_time: rfiDetail.correspondence_time,
      ...data,
    })) as ApiCallResponse;

    if (updateRes?.success) {
      handleChangeFieldStatus({
        field: field,
        status: "success",
        action: "API",
      });
      dispatch(updateRFIDetails(data));
    } else {
      notification.error({
        description: updateRes?.message || "Something went wrong!",
      });
      setInputValues({ ...inputValues, [field]: rfiDetail[field] });
    }
    delay(() => {
      const fieldAction = getStatusActionForField(
        loadingStatusRef.current,
        field
      );
      handleChangeFieldStatus({
        field: field,
        status: "FOCUS" === fieldAction ? "save" : "button",
        action: fieldAction || "API",
      });
    }, 1000);
  };

  useEffect(() => {
    if (rfiDetail?.correspondence_id) {
      setInputValues(rfiDetail);
    }
  }, [rfiDetail]);

  useEffect(() => {
    const fetchChangeOrders = async () => {
      const response = await listChangeOrders({
        filter: {
          status: "0",
          type: "co",
          project: !!rfiDetail?.project_id
            ? rfiDetail?.project_id?.toString() != "0"
              ? rfiDetail?.project_id.toString()
              : undefined
            : undefined,
          billing_status: "140",
        },
        page: 0,
        limit: 0,
        ignore_filter: 1,
      });
      const changeOrderList = response?.data?.changeOrders || [];
      setChangeOrderData(changeOrderList);
    };
    fetchChangeOrders();
  }, [rfiDetail?.project_id]);

  const changeOrderDataList = changeOrderData.map((item) => ({
    value: item.change_order_id,
    label: `#${item.company_order_id} - ${item.project_name} - ${item.subject}`,
  }));
  const selectedChangeOrder = changeOrderData.find(
    (item) => item.change_order_id === inputValues.change_order_id
  );

  const handleInpOnChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setInputValues({ ...inputValues, [name]: value });
  };

  return (
    <>
      <CrudCommonCard
        headerTitle={_t("Location")}
        iconProps={{
          icon: "fa-solid fa-location-dot",
          containerClassName:
            "bg-[linear-gradient(180deg,#63759A1a_0%,#63759A1a_100%)]",
          id: "rfi_question_icon",
          colors: ["#63759A", "#63759A"],
        }}
        children={
          <div className="pt-2">
            <ul className="w-full grid sm:gap-1 gap-2">
              <li>
                <InputField
                  label={_t("Plan Sheet Number")}
                  labelPlacement="left"
                  placeholder={_t("Plan Sheet Number")}
                  name="plan_sheet_no"
                  editInline={true}
                  iconView={true}
                  required={false}
                  readOnly={isReadOnly}
                  value={HTMLEntities.decode(inputValues.plan_sheet_no ?? "")}
                  onChange={handleInpOnChange}
                  onBlur={(e) => {
                    const value = e?.target?.value?.trim();
                    if (value !== rfiDetail.plan_sheet_no) {
                      handleUpdateField({ plan_sheet_no: value });
                    } else {
                      handleChangeFieldStatus({
                        field: "plan_sheet_no",
                        status: "button",
                        action: "BLUR",
                      });
                      setInputValues({
                        ...inputValues,
                        plan_sheet_no: rfiDetail.plan_sheet_no,
                      });
                    }
                  }}
                  fixStatus={getStatusForField(loadingStatus, "plan_sheet_no")}
                  onFocus={() =>
                    handleChangeFieldStatus({
                      field: "plan_sheet_no",
                      status: "save",
                      action: "FOCUS",
                    })
                  }
                  onMouseEnter={() => {
                    handleChangeFieldStatus({
                      field: "plan_sheet_no",
                      status: "edit",
                      action: "ME",
                    });
                  }}
                  onMouseLeaveDiv={() => {
                    handleChangeFieldStatus({
                      field: "plan_sheet_no",
                      status: "button",
                      action: "ML",
                    });
                  }}
                />
              </li>
              <li>
                <TextAreaField
                  label={_t("Area In Question")}
                  placeholder={_t("Area In Question")}
                  labelPlacement="left"
                  name="reference_area_of_question"
                  editInline={true}
                  iconView={true}
                  readOnly={isReadOnly}
                  disabled={isReadOnly}
                  ref={descriptionFieldRef}
                  value={HTMLEntities.decode(
                    inputValues.reference_area_of_question ?? ""
                  )}
                  onClickStsIcon={() => {
                    if (
                      getStatusForField(
                        loadingStatus,
                        "reference_area_of_question"
                      ) === "edit"
                    ) {
                      descriptionFieldRef?.current?.focus();
                    }
                  }}
                  onChange={handleInpOnChange}
                  onBlur={(e) => {
                    const value = e?.target?.value?.trim();
                    if (value !== rfiDetail.reference_area_of_question) {
                      handleUpdateField({
                        reference_area_of_question: value,
                      });
                    } else {
                      handleChangeFieldStatus({
                        field: "reference_area_of_question",
                        status: "button",
                        action: "BLUR",
                      });
                      setInputValues({
                        ...inputValues,
                        reference_area_of_question:
                          rfiDetail.reference_area_of_question,
                      });
                    }
                  }}
                  fixStatus={getStatusForField(
                    loadingStatus,
                    "reference_area_of_question"
                  )}
                  onFocus={() =>
                    handleChangeFieldStatus({
                      field: "reference_area_of_question",
                      status: "save",
                      action: "FOCUS",
                    })
                  }
                  onMouseEnter={() => {
                    handleChangeFieldStatus({
                      field: "reference_area_of_question",
                      status: "edit",
                      action: "ME",
                    });
                  }}
                  onMouseLeaveDiv={() => {
                    handleChangeFieldStatus({
                      field: "reference_area_of_question",
                      status: "button",
                      action: "ML",
                    });
                  }}
                />
              </li>
              <li>
                <InlineField
                  label={_t("Ref. Change Order")}
                  labelPlacement="left"
                  field={
                    <div className="flex items-center w-full sm:max-w-[calc(100%-216px)]">
                      <div
                        className={`hover:w-full focus-within:w-full focus:w-full ${
                          !!changeOrderDataList.find(
                            (opt) =>
                              Number(opt.value) === inputValues?.change_order_id
                          )?.value ||
                          !["loading", "success", "error"].includes(
                            getStatusForField(loadingStatus, "change_order_id")
                          )
                            ? "w-full"
                            : "max-w-[calc(100%-24px)]"
                        }`}
                      >
                        <SelectField
                          placeholder={_t("Select Change Order")}
                          name="change_order_id"
                          labelPlacement="left"
                          editInline={true}
                          iconView={true}
                          allowClear={true}
                          showSearch
                          readOnly={isReadOnly}
                          disabled={isReadOnly}
                          value={
                            changeOrderDataList.find(
                              (opt) =>
                                Number(opt.value) ===
                                inputValues?.change_order_id
                            )?.value
                          }
                          options={
                            changeOrderDataList.map((type) => ({
                              label: type?.label,
                              value: type?.value,
                              key: type?.value,
                            })) ?? []
                          }
                          onChange={async (value: string | string[]) => {
                            let newValue = 0;

                            if (!Array.isArray(value)) {
                              newValue = Number(value);
                            }

                            setInputValues({
                              ...inputValues,
                              change_order_id: newValue,
                            });

                            if (
                              newValue !== Number(rfiDetail.change_order_id)
                            ) {
                              handleUpdateField({
                                change_order_id: newValue,
                              });
                            } else {
                              handleChangeFieldStatus({
                                field: "change_order_id",
                                status: "button",
                                action: "BLUR",
                              });

                              setInputValues({
                                ...inputValues,
                                change_order_id: Number(
                                  rfiDetail.change_order_id
                                ),
                              });
                            }
                          }}
                          fixStatus={getStatusForField(
                            loadingStatus,
                            "change_order_id"
                          )}
                        />
                      </div>
                      {selectedChangeOrder && (
                        <ChangeOrderlFieldRedirectionIcon
                          // iconClassName="!w-3.5 !h-3.5"
                          changeOrderId={selectedChangeOrder.change_order_id}
                        />
                      )}
                    </div>
                  }
                />
              </li>
            </ul>
          </div>
        }
      />
    </>
  );
};

export default ReferencedQuestion;
