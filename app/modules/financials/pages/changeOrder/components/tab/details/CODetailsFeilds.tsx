import React, {
  use<PERSON><PERSON>back,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import {
  IChangeOrderDetail,
  IChangeOrderProjectContactState,
} from "../../../redux/types";
// Molecules
import { InputField } from "~/shared/components/molecules/inputField";
import { ButtonField } from "~/shared/components/molecules/buttonField";
import { SelectField } from "~/shared/components/molecules/selectField";
import { InlineField } from "~/shared/components/molecules/inlineField";
import { DatePickerField } from "~/shared/components/molecules/datePickerField";
import { ContactDetailsButton } from "~/shared/components/molecules/contactDetailsButton";
import { SelectCustomerDrawer } from "~/shared/components/molecules/selectCustomerDrawer";
import { RFIFieldRedirectionIcon } from "~/shared/components/molecules/fieldRedirect/rfiFieldRedirectionIcon";
import FieldRedirectButton from "~/shared/components/molecules/fieldRedirect/fieldRedirectButton/FieldRedirectButton";
import { DirectoryFieldRedirectionIcon } from "~/shared/components/molecules/fieldRedirect/directoryFieldRedirectionIcon";
// Organisms
import ContactDetailsModal from "~/shared/components/organisms/contactDetailModal/ContactDetailModal";
// Other
import {
  filterOptionBySubstring,
  getStatusActionForField,
  getStatusForField,
} from "~/shared/utils/helper/common";
import { displayDateFormat } from "~/shared/utils/helper/defaultDateFormat";
import { getGConfig, getGSettings } from "~/zustand";
import SelectEstimate from "../../shared/SelectEstimates";
import {
  useAppDispatch,
  useAppSelector,
} from "~/modules/financials/pages/changeOrder/redux/store";
import {
  fetchChangeOrderDetails,
  postChangeOrderDetails,
} from "../../../redux/action/changeOrderDetailsActions";
import { updateChangeOrderDetails } from "../../../redux/slices/changeOrderDetailsSlice";
import { defaultConfig } from "~/data";

import SelectRFI from "../../shared/SelectRFI";
import { useCOContext } from "~/context/COContext";
import { resetDash } from "../../../redux/slices/changeOrderDashSlice";
import { resetList } from "../../../redux/slices/changeOrderListSlice";
import { routes } from "~/route-services/routes";

import { fetchChangeOrderProjectAdditonalContact } from "~/redux/action/commonAction";
import { sanitizeString } from "~/helpers/helper";
import { changeOrderFieldStatus } from "~/constants/change-orders";
import delay from "lodash/delay";
import dayjs from "dayjs";
import { useTranslation } from "~/hook";
import isEmpty from "lodash/isEmpty";
import { getDirectaryKeyById } from "~/components/sidebars/multi-select/customer/zustand/action";

export interface ICODetailsFeildsProps {
  details: IChangeOrderDetail;
  type: "cor" | "co";
}

export interface CODetailsSectionActions {
  toggleView: () => void;
}

const ContactRightIcon = ({
  id,
  additionalContactId = 0,
  readOnly,
}: {
  id: string | number;
  additionalContactId?: number;
  readOnly?: boolean | undefined;
}) => {
  const { _t } = useTranslation();
  const [isOpenContactDetails, setIsOpenContactDetails] =
    React.useState<boolean>(false);
  return (
    <div className="flex gap-1 items-center">
      <ContactDetailsButton
        onClick={(e) => {
          e.stopPropagation();
          setIsOpenContactDetails(true);
        }}
      />
      <FieldRedirectButton
        href={`${routes.MANAGE_DIRECTORY.url}/${id}`}
        tooltipTitle={_t("Open the customer detail in new tab")}
      />
      {!!(isOpenContactDetails && id) && (
        <ContactDetailsModal
          isOpenContact={isOpenContactDetails}
          contactId={Number(id)}
          onCloseModal={() => {
            setIsOpenContactDetails(false);
          }}
          readOnly={readOnly}
          additional_contact_id={additionalContactId}
        />
      )}
    </div>
  );
};

//not remove because if need to use in future
// const getInvoiceToName = (invoiceTo: IChangeOrderDetail["invoicedTo"]) => {
//   if (!invoiceTo) return "";
//   if (invoiceTo.first_name && invoiceTo.last_name)
//     return `${invoiceTo.first_name} ${invoiceTo.last_name}`;
//   if (invoiceTo.first_name) return invoiceTo.first_name;
//   return invoiceTo.company_name;
// };

const CODetailsFeilds = ({ details, type }: ICODetailsFeildsProps) => {
  const { _t } = useTranslation();
  const dispatch = useAppDispatch();
  const { isReadOnly } = useCOContext();
  const descriptionFieldRef = useRef<HTMLInputElement>(null);
  const loadingStatusRef = useRef(changeOrderFieldStatus);
  const [showCustomerSelector, setShowCustomerSelector] =
    React.useState<boolean>(false);
  const [formData, setFormData] = React.useState<IChangeOrderDetail>(details);
  const [projectId, setProjectId] = React.useState(details.project_id);
  const [invoiceTo, setInvoiceTo] = React.useState<Partial<CustomerEmail>>(
    details?.invoicedTo?.user_id
      ? {
          module_display_name: details.invoicedTo.company_name,
          display_name: details.billed_to_name,
          user_id: details?.billed_to,
          type_key: details.invoicedTo.type_key ?? "customer",
          type: details?.billed_to_dir_type,
          contact_id: details?.billed_to_contact,
        }
      : {}
  );
  const [rfiLoad, isRFILoad] = useState(true);
  const gConfig: GConfig = getGConfig();

  const [noAccessRfiErrorMessage, setNoAccessRfiErrorMessage] =
    useState<string>("");
  const isRfiModuleNoAccess =
    !isEmpty(noAccessRfiErrorMessage) &&
    noAccessRfiErrorMessage.includes(
      "You are not allowed to view the RFI data."
    ) &&
    details.rfi_id;
  const [loadingStatus, setLoadingStatus] = useState<Array<IFieldStatus>>(
    changeOrderFieldStatus
  );
  const [additionalContacts, setAdditionalContacts] = React.useState<
    IAdditionalContactDirectory[]
  >([]);
  const [additonalContactLoading, setAdditonalContactLoading] =
    React.useState<boolean>(false);
  const [isOpenContactDetails, setIsOpenContactDetails] =
    React.useState<boolean>(false);
  const { date_format, is_custom_change_orders_id }: GSettings = getGSettings();
  const {
    contacts,
    customer_contacts,
    project_contacts,
  }: IChangeOrderProjectContactState = useAppSelector((state) => {
    return state.changeOrderProjectContact;
  });
  const [approvedByValue, setApprovedByValue] = useState<String | Number>();
  const customerName = useMemo(() => {
    if (!details || !details.customer) return "";
    if (!details.customer.first_name && !details.customer.last_name)
      return details.customer.company_name || details.customer.email;

    return details?.customer_display_name;
  }, [details]);

  const contactOptions = useMemo(() => {
    return customer_contacts
      .map((contact) => ({
        label: HTMLEntities.decode(
          sanitizeString(
            contact.first_name +
              " " +
              contact.last_name +
              " (" +
              contact.company_name +
              ")"
          )
        ),
        value: contact.contact_id.toString(),
        type: contact.type,
      }))
      .concat(
        [...contacts, ...project_contacts].map((contact) => ({
          label: HTMLEntities.decode(sanitizeString(contact.contact_name)),
          value:
            contact?.directory_contact_id?.toString() ||
            contact.directory_id.toString(),
          type: contact.type,
        }))
      )
      .filter((contact) => contact.label);
  }, [contacts, customer_contacts]);

  const additionalContactOptions = useMemo(() => {
    return additionalContacts
      .filter((contact) => contact?.user_id && contact?.display_name)
      .map((contact) => ({
        label: HTMLEntities.decode(sanitizeString(contact?.display_name || "")),
        value: contact?.contact_id
          ? contact?.contact_id?.toString()
          : contact?.user_id.toString(),
        ...contact,
      }));
  }, [additionalContacts]);

  const approvalContactOptions = useMemo(() => {
    const options = [...contactOptions, ...additionalContactOptions];

    const uniqueOptions = options.filter(
      (option, index, self) =>
        index === self.findIndex((t) => t.value === option.value)
    );

    return uniqueOptions.filter((contact) => contact.label);
  }, [contacts, customer_contacts, additionalContactOptions]);
  useEffect(() => {
    if (approvalContactOptions.length > 0) {
      const matchingOption = approvalContactOptions.find(
        (option) => option.value === details.approved_by
      );
      setApprovedByValue(matchingOption ? details?.approved_by : undefined);
    }
  }, [details.approved_by, approvalContactOptions]);

  const handleBulkUpdate = useCallback(
    async (
      data: { [key: string]: string | number | null },
      fieldKey: string
    ) => {
      handleChangeFieldStatus({
        field: fieldKey,
        status: "loading",
        action: "API",
      });

      const { success } = await submitUpdate(data);

      if (success) {
        handleChangeFieldStatus({
          field: fieldKey,
          status: "success",
          action: "API",
        });
      }

      delay(() => {
        handleChangeFieldStatus({
          field: fieldKey,
          status: "button",
          action: "API",
        });
      }, 1000);
    },
    []
  );

  const handleUpdate = useCallback(
    async (key: string, value: string | number | null) => {
      if (!details) return;
      let data = {
        [key]: value,
      };
      if (key === "order_date") {
        if (date_format === "DD/MM/YYYY") {
          const parsedDate = dayjs(value, "DD/MM/YYYY", true);
          data = {
            [key]: parsedDate.format("YYYY-MM-DD"),
          };
        } else if (date_format === "MM/DD/YYYY") {
          const parsedDate = dayjs(value, "MM/DD/YYYY", true);
          data = {
            [key]: parsedDate.format("YYYY-MM-DD"),
          };
        } else {
          const parsedDate = dayjs(
            value,
            ["YYYY-MM-DD", "DD.MM.YYYY", "DD/MMM/YYYY"],
            true
          );
          data = {
            [key]: parsedDate.format("YYYY-MM-DD"),
          };
        }
      }
      handleChangeFieldStatus({
        field: key,
        status: "loading",
        action: "API",
      });
      const { success } = await submitUpdate(data);

      if (success) {
        handleChangeFieldStatus({
          field: key,
          status: "success",
          action: "API",
        });
      }

      delay(() => {
        const fieldAction = getStatusActionForField(
          loadingStatusRef.current,
          key
        );
        handleChangeFieldStatus({
          field: key,
          status: "FOCUS" === fieldAction ? "save" : "button",
          action: fieldAction || "API",
        });
      }, 1000);

      if (key === "custom_change_order_id") {
        dispatch(
          fetchChangeOrderDetails({
            id: details.change_order_id.toString(),
            silent: true,
          })
        );
      }
    },
    [details]
  );

  const submitUpdate = useCallback(
    async (data: { [key: string]: string | number | boolean | null }) => {
      const field = Object.keys(data);
      if (field[0] === "order_date") {
        const values = Object.values(data)[0] as string;
        dispatch(
          updateChangeOrderDetails({
            ...data,
            order_date: !!values ? dayjs(values).format(date_format) : "",
          })
        );
      } else {
        dispatch(updateChangeOrderDetails(data));
      }
      const response = await postChangeOrderDetails({
        changeOrderId: details?.change_order_id,
        projectId: details?.project_id,
        type: details.type,
        data: data,
      });
      if (response.success) {
        if (field[0] === "order_date") {
          const values = Object.values(data)[0] as string;
          dispatch(
            updateChangeOrderDetails({
              ...data,
              order_date: !!values ? dayjs(values).format(date_format) : "",
            })
          );
        } else {
          dispatch(updateChangeOrderDetails(data));
        }
        dispatch(resetDash());
        dispatch(resetList());
      } else {
        notification.error({
          message: "Error",
          description: response.message,
        });
      }
      return response;
    },
    []
  );

  const handleChangeFieldStatus = ({ field, status, action }: IFieldStatus) => {
    const checkStatus = loadingStatus.find(
      (item: IFieldStatus) => item?.field === field
    );

    if (
      !(
        (checkStatus?.status === "loading" ||
          checkStatus?.status === "success" ||
          checkStatus?.status === "save") &&
        (action === "ME" || action === "ML")
      )
    ) {
      setLoadingStatus((prevState: IFieldStatus[]) => {
        const newState = prevState.map((item) =>
          item.field === field ? { ...item, status: status, action } : item
        );
        loadingStatusRef.current = newState;
        return newState;
      });
    }
  };

  useEffect(() => {
    if (details.billed_to) {
      setAdditonalContactLoading(true);
      fetchChangeOrderProjectAdditonalContact({
        directory_id: details.billed_to,
      }).then((res) => {
        if (res.success) {
          setAdditionalContacts([
            (res as IAdditionalContactsRes).data.directory,
            ...(res as IAdditionalContactsRes).data.contacts,
          ]);
        }
        setAdditonalContactLoading(false);
      });
    } else {
      setAdditionalContacts([]);
      setAdditonalContactLoading(false);
    }
  }, [details.invoicedTo]);

  useEffect(() => {
    setInvoiceTo(
      details.billed_to
        ? {
            module_display_name: details.invoicedTo.company_name,
            display_name: details.billed_to_name,
            user_id: details?.billed_to,
            type_key: details.invoicedTo.type_key ?? "customer",
            type: details?.billed_to_dir_type,
            contact_id: details?.billed_to_contact,
          }
        : {}
    );
  }, [details.invoicedTo]);

  useEffect(() => {
    setProjectId(details.project_id);
  }, []);

  return (
    <>
      <ul className="w-full flex flex-col gap-1 mt-[3px]">
        {/* Date */}
        <li>
          <DatePickerField
            label={_t("Date")}
            name="order_date"
            labelPlacement="left"
            placeholder={_t("Select Date")}
            editInline={true}
            allowClear={false}
            iconView={true}
            readOnly={isReadOnly}
            disabled={isReadOnly}
            fixStatus={getStatusForField(loadingStatus, "order_date")}
            format={date_format}
            value={displayDateFormat(
              details.order_date?.toString().trim(),
              date_format
            )}
            onChange={(_, dateString) =>
              handleUpdate("order_date", dateString as string)
            }
          />
        </li>

        {/* Customer */}
        <li className="overflow-hidden">
          <ButtonField
            label={_t("Customer")}
            name="customer"
            placeholder={_t("Customer")}
            labelPlacement="left"
            editInline={true}
            readOnly={isReadOnly}
            readOnlyClassName="sm:block flex"
            iconView={true}
            disabled={true}
            value={customerName}
            rightIcon={
              !!details.customer_id && (
                <ContactRightIcon
                  id={details.customer_id}
                  readOnly={isReadOnly}
                  additionalContactId={details?.customer_contact_id}
                />
              )
            }
          />
        </li>

        {/* Invoiced To */}
        {type === "co" && (
          <li className="overflow-hidden">
            <ButtonField
              label={_t("Invoiced To")}
              placeholder={_t("Invoiced To")}
              name="customer"
              labelPlacement="left"
              className="justify-between"
              editInline={true}
              disabled={isReadOnly}
              readOnly={isReadOnly}
              readOnlyClassName="sm:block flex"
              iconView={true}
              value={invoiceTo.display_name ?? ""}
              onClick={() => setShowCustomerSelector(true)}
              avatarProps={
                invoiceTo.display_name
                  ? {
                      user: {
                        name: HTMLEntities.decode(
                          sanitizeString(invoiceTo.display_name)
                        ),
                        image: invoiceTo.image,
                      },
                    }
                  : undefined
              }
              rightIcon={
                <div className="flex gap-1 items-center">
                  {!!invoiceTo.display_name && (
                    <ContactDetailsButton
                      onClick={(e) => {
                        e.stopPropagation();
                        setIsOpenContactDetails(true);
                      }}
                    />
                  )}
                  {!!invoiceTo.user_id && (
                    <DirectoryFieldRedirectionIcon
                      directoryId={invoiceTo.user_id?.toString() || ""}
                      directoryTypeKey={
                        invoiceTo.type
                          ? getDirectaryKeyById(
                              invoiceTo.type === 1 ? 2 : Number(invoiceTo.type),
                              gConfig
                            )
                          : ""
                      }
                    />
                  )}
                </div>
              }
              statusProps={{
                status: getStatusForField(loadingStatus, "billed_to"),
              }}
            />
          </li>
        )}

        {/* Requested By */}
        <li>
          <SelectField
            label={_t("Requested By")}
            placeholder={_t("Requested By")}
            labelPlacement="left"
            value={
              !!Number(details.requested_by) && contactOptions.length > 0
                ? details.requested_by
                : undefined
            }
            editInline={true}
            disabled={isReadOnly}
            readOnly={isReadOnly}
            iconView={true}
            options={contactOptions}
            allowClear
            showSearch={true}
            filterOption={(input, option) =>
              filterOptionBySubstring(input, option?.label as string)
            }
            onChange={(value, selectedOption) => {
              const selectedValue = Array.isArray(value) ? value[0] : value;

              if (details.project_id !== projectId) {
                handleBulkUpdate(
                  {
                    requested_by: selectedValue || "",
                    requested_type: selectedOption?.type || "",
                    project_id: details.project_id,
                  },
                  "requested_by"
                );
              } else {
                handleBulkUpdate(
                  {
                    requested_by: selectedValue || "",
                    requested_type: selectedOption?.type || "",
                  },
                  "requested_by"
                );
              }
            }}
            fixStatus={getStatusForField(loadingStatus, "requested_by")}
            onBlur={() => {
              handleChangeFieldStatus({
                field: "requested_by",
                status: "button",
                action: "BLUR",
              });
            }}
          />
        </li>

        {type === "co" && (
          <li>
            <InlineField
              label={_t("Original Estimate")}
              labelPlacement="left"
              field={
                <SelectEstimate
                  labelPlacement="left"
                  placeholder={_t("Select Approved Estimate (If Any)")}
                  projectId={details.project_id}
                  editInline={true}
                  readOnly={isReadOnly}
                  disabled={isReadOnly}
                  iconView={true}
                  value={
                    Boolean(details.estimate_id)
                      ? details.estimate_id?.toString()
                      : undefined
                  }
                  onChange={(value) => {
                    const selectedValue = Array.isArray(value)
                      ? value[0]
                      : value;
                    if (selectedValue !== details.estimate_id?.toString()) {
                      handleUpdate("estimate_id", selectedValue || null);
                    } else {
                      handleChangeFieldStatus({
                        field: "estimate_id",
                        status: "button",
                        action: "BLUR",
                      });
                    }
                  }}
                  filterOption={(input, option) => {
                    const label =
                      option?.label?.toString()?.toLowerCase() || "";
                    const searchTerm = input.toLowerCase();

                    return label.includes(searchTerm);
                  }}
                  fixStatus={getStatusForField(loadingStatus, "estimate_id")}
                  onBlur={() => {
                    handleChangeFieldStatus({
                      field: "estimate_id",
                      status: "button",
                      action: "BLUR",
                    });
                  }}
                />
              }
            />
          </li>
        )}

        {/* {Customer CO} */}
        <li>
          <InputField
            label={
              type === "co"
                ? _t("Customer CO Number")
                : _t("Customer COR") + " #"
            }
            placeholder={
              type === "co"
                ? _t("Customer CO Number")
                : _t("Customer COR") + " #"
            }
            labelPlacement="left"
            value={formData.owner_co}
            editInline={true}
            readOnly={isReadOnly}
            iconView={true}
            onChange={(e) => {
              const value = e.target.value;
              if (value.length <= 21) {
                setFormData({
                  ...formData,
                  owner_co: e.target.value,
                });
              }
            }}
            fixStatus={getStatusForField(loadingStatus, "owner_co")}
            onMouseEnter={() => {
              handleChangeFieldStatus({
                field: "owner_co",
                status: "edit",
                action: "ME",
              });
            }}
            onMouseLeaveDiv={() => {
              handleChangeFieldStatus({
                field: "owner_co",
                status: "button",
                action: "ML",
              });
            }}
            onFocus={() =>
              handleChangeFieldStatus({
                field: "owner_co",
                status: "save",
                action: "FOCUS",
              })
            }
            onBlur={() => {
              if (formData.owner_co !== details.owner_co) {
                handleUpdate("owner_co", formData.owner_co);
              } else {
                handleChangeFieldStatus({
                  field: "owner_co",
                  status: "button",
                  action: "BLUR",
                });
              }
            }}
          />
        </li>

        {/* <li>
          <SelectField
            label={_t("Approved By")}
            placeholder={_t("Approved By")}
            labelPlacement="left"
            value={approvedByValue}
            editInline={true}
            readOnly={isReadOnly}
            disabled={isReadOnly}
            iconView={true}
            options={approvalContactOptions}
            allowClear
            showSearch={true}
            onChange={(value, selectedOption) => {
              const selectedValue = Array.isArray(value) ? value[0] : value;
              handleBulkUpdate(
                {
                  approved_by: selectedValue || "",
                  approved_type: selectedOption?.type || "",
                  project_id: details.project_id,
                },
                "approved_by"
              );
            }}
            fixStatus={getStatusForField(loadingStatus, "approved_by")}
            onBlur={() => {
              handleChangeFieldStatus({
                field: "approved_by",
                status: "button",
                action: "BLUR",
              });
            }}
          />
        </li> */}

        <li>
          <SelectField
            label={_t("Time Delay")}
            placeholder={_t("Select Time Delay")}
            labelPlacement="left"
            allowClear
            value={
              details?.time_delay?.toString() === "0"
                ? undefined
                : details?.time_delay?.toString()
            }
            editInline={true}
            readOnly={isReadOnly}
            disabled={isReadOnly}
            iconView={true}
            options={[
              {
                label: "Yes",
                value: "97",
              },
              {
                label: "No",
                value: "98",
              },
              {
                label: "Possible",
                value: "99",
              },
            ]}
            fixStatus={getStatusForField(loadingStatus, "time_delay")}
            onBlur={() => {
              handleChangeFieldStatus({
                field: "time_delay",
                status: "button",
                action: "BLUR",
              });
            }}
            onChange={(value) => {
              const firstValue = Array.isArray(value) ? value[0] : value;
              if (firstValue === details.time_delay?.toString()) return;

              if (firstValue === "98") {
                handleBulkUpdate(
                  {
                    time_delay: firstValue,
                    day_delay: null,
                  },
                  "time_delay"
                );
                setFormData({
                  ...formData,
                  day_delay: null,
                });
              } else {
                handleUpdate("time_delay", firstValue || "0");
              }
            }}
          />
        </li>

        {/* Days Delayed */}
        {Number(details.time_delay) !== 98 &&
          Number(details.time_delay) !== 0 && (
            <li>
              <InputField
                label={_t("Days Delayed")}
                placeholder={_t("Days Delayed")}
                labelPlacement="left"
                value={formData.day_delay || ""}
                editInline={true}
                disabled={isReadOnly}
                readOnly={isReadOnly}
                iconView={true}
                maxLength={3}
                pattern="[0-9]*"
                onChange={(e) => {
                  let isInvalid = false;
                  if (e.target.value.length > 3) {
                    isInvalid = true;
                  }
                  if (e.target.value && isNaN(Number(e.target.value))) {
                    isInvalid = true;
                  }
                  if (!isInvalid) {
                    setFormData({
                      ...formData,
                      day_delay: Number(e.target.value),
                    });
                  }
                }}
                onBlur={(e) => {
                  if (formData.day_delay !== details.day_delay) {
                    handleUpdate(
                      "day_delay",
                      (isNaN(Number(formData.day_delay))
                        ? null
                        : Number(formData.day_delay)) || ""
                    );
                  } else {
                    handleChangeFieldStatus({
                      field: "day_delay",
                      status: "button",
                      action: "BLUR",
                    });
                  }
                }}
                fixStatus={getStatusForField(loadingStatus, "day_delay")}
                onFocus={() =>
                  handleChangeFieldStatus({
                    field: "day_delay",
                    status: "save",
                    action: "FOCUS",
                  })
                }
                onMouseEnter={() => {
                  handleChangeFieldStatus({
                    field: "day_delay",
                    status: "edit",
                    action: "ME",
                  });
                }}
                onMouseLeaveDiv={() => {
                  handleChangeFieldStatus({
                    field: "day_delay",
                    status: "button",
                    action: "ML",
                  });
                }}
              />
            </li>
          )}

        {/* Assosiated RFI */}
        {type === "cor" && (
          <li>
            <InlineField
              label={_t("Associated RFI")}
              labelPlacement="left"
              field={
                <div className="flex items-center w-full sm:max-w-[calc(100%-171px)]">
                  <div
                    className={`hover:w-full focus-within:w-full focus:w-full ${
                      !isEmpty(noAccessRfiErrorMessage) &&
                      noAccessRfiErrorMessage.includes(
                        "You are not allowed to view the RFI data."
                      )
                        ? "w-full"
                        : !details.rfi_id ||
                          ["loading", "success", "error"].includes(
                            getStatusForField(loadingStatus, "rfi_id")
                          )
                        ? "w-full"
                        : "max-w-[calc(100%-24px)]"
                    }`}
                  >
                    <SelectRFI
                      placeholder={_t("Select Associated RFI")}
                      labelPlacement="left"
                      value={
                        !rfiLoad
                          ? isRfiModuleNoAccess
                            ? `RFI. #${details?.c_rfi_id} - ${details?.project_name}`
                            : details.rfi_id
                            ? details.rfi_id?.toString()
                            : undefined
                          : undefined
                      }
                      allowClear={isRfiModuleNoAccess ? false : true}
                      editInline={true}
                      readOnly={isReadOnly}
                      isReadOnly={isReadOnly}
                      disabled={isReadOnly}
                      projectId={details.project_id}
                      iconView={true}
                      fixStatus={getStatusForField(loadingStatus, "rfi_id")}
                      onBlur={() => {
                        handleChangeFieldStatus({
                          field: "rfi_id",
                          status: "button",
                          action: "BLUR",
                        });
                      }}
                      details={details}
                      setNoAccessRfiErrorMessage={setNoAccessRfiErrorMessage}
                      noAccessRfiErrorMessage={noAccessRfiErrorMessage}
                      onChange={(value) => {
                        handleUpdate("rfi_id", (value as string) || "");
                      }}
                      isRFILoad={isRFILoad}
                    />
                  </div>
                  {!isEmpty(noAccessRfiErrorMessage) &&
                  noAccessRfiErrorMessage.includes(
                    "You are not allowed to view the RFI data."
                  )
                    ? " "
                    : !!details.rfi_id && (
                        <RFIFieldRedirectionIcon rfiId={`${details.rfi_id}`} />
                      )}
                </div>
              }
            />
          </li>
        )}
      </ul>

      {showCustomerSelector && (
        <SelectCustomerDrawer
          openSelectCustomerSidebar={showCustomerSelector}
          closeDrawer={() => {
            setShowCustomerSelector(false);
          }}
          projectId={details?.project_id}
          singleSelecte={true}
          options={[
            defaultConfig.employee_key,
            "my_crew",
            defaultConfig.customer_key,
            defaultConfig.contractor_key,
            defaultConfig.vendor_key,
            defaultConfig.misc_contact_key,
            "my_project",
            "by_service",
          ]}
          setCustomer={(data) => {
            const invoiceData = {
              ...data[0],
              type: data[0]?.orig_type || details?.billed_to_dir_type,
            };
            setInvoiceTo((invoiceData as Partial<CustomerEmail>) || {});
            // if (data?.[0]?.user_id) {
            handleUpdate("billed_to", data?.[0]?.user_id || "");
            // handleUpdate("billed_to_contact", data?.[0]?.contact_id || "");
            // } else {
            //   handleUpdate("billed_to", "");
            // }
            const isApprovedByValid = contactOptions.some(
              (option) => option.value === details?.approved_by
            );
            if (
              !isApprovedByValid &&
              details?.approved_by != "" &&
              details?.approved_by
            ) {
              handleUpdate("approved_by", null);
            }
          }}
          selectedCustomer={
            (invoiceTo as CustomerEmail).user_id
              ? [invoiceTo as CustomerEmail]
              : []
          }
          groupCheckBox={true}
        />
      )}
      {!!(isOpenContactDetails && invoiceTo.user_id) && (
        <ContactDetailsModal
          isOpenContact={isOpenContactDetails}
          contactId={Number(invoiceTo.user_id)}
          onCloseModal={() => {
            setIsOpenContactDetails(false);
          }}
          readOnly={isReadOnly}
          additional_contact_id={invoiceTo?.contact_id}
        />
      )}
    </>
  );
};

export default CODetailsFeilds;
