import { useMemo } from "react";
import { ColDef, ColGroupDef, ValueGetterParams, ValueSetterParams } from "ag-grid-community";
import { useTranslation } from "~/hook";
import { CheckBox } from "~/shared/components/atoms/checkBox";
import { suppressKeyboardEvent } from "../utils";
import { floatWithNegativeRegex } from "~/modules/financials/pages/changeOrder/utils/helpers";
import { qtyNumberCheck, generateCostCodeLabel } from "~/shared/utils/helper/common";
import { formatAmount, sanitizeString } from "~/helpers/helper";
import {
  Move<PERSON><PERSON><PERSON><PERSON>er,
  CheckboxCellRenderer,
  TypeCellRenderer,
  ItemNameCellRenderer,
  CostCodeCellRenderer,
  QuantityCellRenderer,
  UnitCostCellRenderer,
} from "./CellRenderers";

interface UseColumnDefinitionsProps {
  isReadOnly: boolean;
  ParentIsReadOnly: boolean;
  selectedItems: Record<string, any>;
  singleSection: ESSection;
  filteredCodeCostData: any[];
  singleSectionItems: ESEstimateItem[];
  units: any[];
  flags: any;
  onSelectionChange: (selected: boolean, data: { data: ESEstimateItem }) => void;
  onSelectAll: (sectionId: number) => void;
  onUnselectAll: (sectionId: number) => void;
  updateItemField: (params: any) => void;
  setCostChangeConfirmation: (data: ESEstimateItem) => void;
}

/**
 * Custom hook for generating AgGrid column definitions
 * Extracted from DragComponent to improve code organization
 */
export const useColumnDefinitions = ({
  isReadOnly,
  ParentIsReadOnly,
  selectedItems,
  singleSection,
  filteredCodeCostData,
  singleSectionItems,
  units,
  flags,
  onSelectionChange,
  onSelectAll,
  onUnselectAll,
  updateItemField,
  setCostChangeConfirmation,
}: UseColumnDefinitionsProps) => {
  const { _t } = useTranslation();

  const columnDefs: (ColDef | ColGroupDef)[] = useMemo(() => [
    // Move column
    {
      headerName: "",
      minWidth: isReadOnly ? 0 : 30,
      maxWidth: isReadOnly ? 0 : 30,
      field: "move",
      suppressMenu: true,
      hide: Boolean(isReadOnly),
      rowDrag: !isReadOnly,
      cellClass: () =>
        `ag-cell-center ag-move-cell custom-move-icon-set cell-invinsible ${
          Object.keys(selectedItems)?.length ? "" : " hover-visibility"
        }`,
      cellRenderer: () => <MoveCellRenderer selectedItems={selectedItems} />,
    },
    // Checkbox column
    {
      headerName: "",
      headerComponent: () => {
        const allSelected =
          singleSection?.items?.every(
            (item) => selectedItems?.[item?.item_id]?.selected
          ) && singleSection?.items?.length > 0;
        return (singleSection?.items?.length || 0) > 0 &&
          !flags.showZeroQuantityItemsOnly ? (
          <CheckBox
            checked={allSelected}
            onChange={(e) => {
              if (e.target.checked)
                onSelectAll(singleSection.section_id as number);
              else
                onUnselectAll(singleSection.section_id as number);
            }}
          />
        ) : null;
      },
      field: "checkbox",
      minWidth: isReadOnly ? 0 : 40,
      maxWidth: isReadOnly ? 0 : 40,
      hide: Boolean(isReadOnly || flags.showZeroQuantityItemsOnly),
      checkboxSelection: false,
      headerCheckboxSelection: false,
      suppressMenu: true,
      suppressMovable: true,
      cellClass: () =>
        `ag-cell-center ad-call-pr-0 ad-call-pl-0 hover-visibility ${
          Object.keys(selectedItems)?.length ? "visible" : "cell-invinsible"
        }`,
      cellRenderer: (params: any) => (
        <CheckboxCellRenderer
          data={params.data}
          selectedItems={selectedItems}
          onSelectionChange={onSelectionChange}
          params={params}
        />
      ),
    },
    // Type column
    {
      headerName: _t("Type"),
      field: "item_type_name",
      maxWidth: 50,
      minWidth: 50,
      suppressMovable: false,
      suppressMenu: true,
      headerClass: "ag-header-center",
      cellClass: "ag-cell-center",
      cellRenderer: (params: any) => <TypeCellRenderer data={params.data} />,
    },
    // Item Name column
    {
      headerName: _t("Item Name"),
      field: "subject",
      minWidth: 150,
      flex: 2,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      resizable: true,
      suppressMovable: false,
      suppressMenu: true,
      editable: !isReadOnly,
      cellRenderer: (params: any) => <ItemNameCellRenderer data={params.data} />,
      valueGetter: (params: ValueGetterParams) => {
        return HTMLEntities.decode(sanitizeString(params?.data?.subject));
      },
      valueSetter: (params: ValueSetterParams) => {
        if (params && params.node) {
          if (!params.newValue.trim().length) {
            notification.error({
              description: _t("Item Name is required."),
            });
            return false;
          }
          const updatedData = {
            ...params.data,
            subject: params.newValue.trim(),
          };
          params.node.setData(updatedData);
          updateItemField({
            itemId: updatedData.item_id,
            itemData: params.data,
            updatedItem: {
              subject: params.newValue.trim(),
            },
          });
        }
        return true;
      },
    },
    // Cost Code column
    {
      headerName: _t("Cost Code"),
      field: "cost_code_id",
      minWidth: 180,
      flex: 2,
      editable: !ParentIsReadOnly,
      resizable: true,
      suppressMovable: false,
      suppressMenu: true,
      headerClass: "ag-header-left",
      cellClass: "ag-cell-left",
      cellEditor: "agRichSelectCellEditor",
      cellEditorParams: {
        values: filteredCodeCostData?.map((item) => item?.label),
        filterList: true,
        searchType: "matchAny",
        allowTyping: true,
        valueListMaxHeight:
          singleSectionItems?.length == 1
            ? 60
            : singleSectionItems?.length == 2
            ? 90
            : singleSectionItems?.length == 3
            ? 120
            : singleSectionItems?.length == 4
            ? 150
            : 180,
      },
      valueGetter: (params: ValueGetterParams) => {
        return params.data?.cost_code_name
          ? generateCostCodeLabel({
              name: params.data?.cost_code_name,
              code: params.data?.cost_code,
              isArchived: false,
              isAllowCodeWithoutName: true,
            })
          : "";
      },
      valueSetter: (params: ValueSetterParams) => {
        if (params && params.node) {
          const costCodelabel = params.newValue;
          const costCode = filteredCodeCostData?.find(
            (el) => el?.label?.trim() === costCodelabel?.trim()
          );
          if (costCode) {
            const updatedData = {
              ...params.data,
              cost_code_name: costCode?.cost_code_name,
              cost_code_id: costCode?.code_id,
              cost_code: costCode?.csi_code,
              code_is_deleted: 0,
            };
            params.node.setData(updatedData);
            updateItemField({
              itemId: updatedData.item_id,
              itemData: params.data,
              updatedItem: {
                cost_code_id: Number(costCode?.code_id),
                cost_code_name: costCode?.cost_code_name,
                cost_code: costCode?.csi_code,
                code_is_deleted: 0,
              },
            });
          }
        }
        return true;
      },
      cellRenderer: (params: any) => (
        <CostCodeCellRenderer
          data={params.data}
          filteredCodeCostData={filteredCodeCostData}
        />
      ),
    },
  ], [
    isReadOnly,
    ParentIsReadOnly,
    selectedItems,
    singleSection,
    filteredCodeCostData,
    singleSectionItems,
    flags,
    onSelectionChange,
    onSelectAll,
    onUnselectAll,
    updateItemField,
    setCostChangeConfirmation,
    _t,
  ]);

  return columnDefs;
};
