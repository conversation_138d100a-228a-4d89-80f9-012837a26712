import { ColDef } from "ag-grid-community";

export const suppressKeyboardEvent: ColDef["suppressKeyboardEvent"] = (
  params
) => {
  const { event, api } = params;

  if (event.key === "ArrowUp" || event.key === "ArrowDown") {
    event.preventDefault();
    event.stopPropagation();
    return true;
  }

  if (event.key === "Enter") {
    event.preventDefault();
    event.stopPropagation();

    // Call valueSetter or custom logic for saving the value
    api.stopEditing(); // Stop editing to trigger valueSetter
    return true;
  }

  return true;
};
