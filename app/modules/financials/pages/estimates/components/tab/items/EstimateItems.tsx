// molecules
import { CidbItemDrawer } from "~/shared/components/molecules/cidbItemDrawer";
import { ConfirmModal } from "~/shared/components/molecules/confirmModal";
// store
import { GridApi, GridOptions, GridReadyEvent } from "ag-grid-community";
import { useEffect, useMemo, useState } from "react";
import { DndProvider } from "react-dnd";
import { HTML5Backend } from "react-dnd-html5-backend";
import { useTranslation } from "~/hook";
import { getGSettings, useGModules } from "~/zustand";
import {
  addEstimateItems,
  copySections,
  deleteSections,
  getEstimateItems,
  removeEstimateItems,
} from "../../../redux/action/ESItemAction";
import {
  addBulkItemsFromCostDatabase,
  bulkRemoveItems,
  removeItem,
  removeSection,
  updateItemsBulk,
} from "../../../redux/slices/ESItemSlice";
import { useAppESDispatch, useAppESSelector } from "../../../redux/store";
import { EstimateBulkMarkup } from "../sidebar/estimateBulkMarkup";
import ESSectionItem from "../sidebar/item/ESSectionItem";
import { SectionOpen } from "../sidebar/section";
import DragComponent from "./DragComponent";
import OneBuildImportItems from "../sidebar/itemsdatabase/OneBuildImportItems";
import { useGlobalModule } from "~/zustand/global/modules/slice";
import { Spin } from "~/shared/components/atoms/spin";

function generatePayload(items: ESEstimateItem[], newSectionId?: number) {
  return items?.map((item, index) => ({
    sectionId: newSectionId || item.section_id,
    itemId: item.item_id,
    updatedItem: {
      order_number: index + 1,
    },
  }));
}

const typeMapping: TypeMapping = {
  Material: { idKey: "material_id", taxKey: "is_taxable_material_items" },
  Labor: { idKey: "labor_id", taxKey: "is_taxable_labor_items" },
  Subcontractor: {
    idKey: "contractor_id",
    taxKey: "is_taxable_subcontractor_items",
  },
  Equipment: { idKey: "equipment_id", taxKey: "is_taxable_equipment_items" },
  "Other Items": { idKey: "other_item_id", taxKey: "is_taxable_other_items" },
};

const possibleIds: (keyof ItemType)[] = [
  "equipment_id",
  "material_id",
  "labor_id",
  "contractor_id",
  "other_item_id",
  "item_id",
];
const EstimateItems: React.FC<EstimateItemsProps> = ({
  isReadOnly: ParentIsReadOnly,
}) => {
  const { _t } = useTranslation();
  const { checkModuleAccessByKey } = useGModules();
  const { getGlobalModuleByKey } = useGlobalModule();

  const EstimateModule = useMemo(
    () => getGlobalModuleByKey(CFConfig.estimate_module),
    [getGlobalModuleByKey] // Ensures memoization based on function reference
  );

  const { module_key: estimate_module_key = "" } = EstimateModule || {};
  const [sectionOpen, setSectionOpen] = useState<boolean>(false);
  const [importEstimateFrom1Build, setImportEstimateFrom1Build] =
    useState<null | Partial<ESSection>>(null);
  const [estimateBulkMarkup, setEstimateBulkMarkup] =
    useState<Partial<ESSection> | null>(null);
  const [itemsOpen, setItemsOpen] = useState<boolean>(false);
  const [costItemDatabaseOpen, setCostItemDatabaseOpen] =
    useState<null | Partial<ESSection>>(null);
  const [itemsData, setItemsData] = useState<Partial<ESEstimateItem>>({});
  const [copySectionData, setCopySectionData] = useState<Partial<ESSection>>(
    {}
  );
  const [isSectionAdd, setIsSectionAdd] = useState<boolean>(false);
  const [isItemAdd, setIsItemAdd] = useState<boolean>(false);
  const [sectionData, setSectionData] = useState<Partial<ESSection>>({});
  const [gridApi, setGridApi] = useState<GridApi<any>[]>([]);
  const [selectDeletedItems, setSelectDeletedItems] =
    useState<Partial<ESSection> | null>(null);
  const dispatch = useAppESDispatch();
  const { sections, isLoading: isSectionLoading } = useAppESSelector(
    (state) => state.estimateItems
  );
  const { estimateDetail } = useAppESSelector((state) => state.estimateDetail);
  const gSettings: GSettings = getGSettings();
  const [confirmDialogOpen, setConfirmDialogOpen] = useState<boolean>(false);
  const [confirmCopyDialogOpen, setConfirmCopyDialogOpen] =
    useState<boolean>(false);
  const [selectedItemId, setSelectedItemId] = useState<number>();
  const [selectedSectionId, setSelectedSectionId] = useState<number>();
  const [isDeleteConfirmOpen, setIsDeleteConfirmOpen] =
    useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const handleDeleteSectionItem = async () => {
    if (!isLoading) {
      setIsLoading(true);
      if (selectedItemId) {
        const deleteRes = await dispatch(
          removeEstimateItems({
            estimate_id: estimateDetail?.estimate_id?.toString(),
            item_id: selectedItemId,
          })
        );
        if ((deleteRes?.payload as IEstimatesItemsApiRes)?.success) {
          setIsLoading(false);
          dispatch(
            removeItem({
              sectionId: selectedSectionId,
              itemId: selectedItemId,
            })
          );
        } else {
          notification.error({
            description: "Delete operation failed",
          });
        }
        setSelectedSectionId(undefined);
        setSelectedItemId(undefined);
        setIsDeleteConfirmOpen(false);
      }
    }
  };

  // const { estimateDetail } = useAppESSelector((state) => state.estimateDetail);

  const isReadOnly = useMemo(
    () =>
      Boolean(
        checkModuleAccessByKey(estimate_module_key) === "read_only" ||
          ReadOnlyModuleStatsuKey?.[`${estimateDetail?.approval_type}`] ||
          ParentIsReadOnly ||
          !estimateDetail
      ),
    [estimate_module_key, estimateDetail?.approval_type, ParentIsReadOnly]
  );
  // const taxRate = (parseFloat(estimateDetail?.tax_rate ?? "0") || 0) / 100;
  const gridOptions: GridOptions = {
    onRowDragEnd: function (event) {
      if (isReadOnly) {
        return;
      }

      const { node, overIndex } = event as { node: any; overIndex: number };
      if (!gridOptions.api || !node) return;

      const rowData: ESEstimateItem[] = [];
      gridOptions.api.forEachNode((node) => rowData.push(node.data));

      rowData.splice(overIndex, 0, rowData.splice(node.rowIndex, 1)[0]);

      handleDragAndDrop(rowData);
    },
  };
  const handleDragAndDrop = async (items: ESEstimateItem[]) => {
    const payload = generatePayload(items);
    dispatch(updateItemsBulk(payload));
  };

  const handleCopySection = async () => {
    setIsLoading(true);
    const response: any = await dispatch(copySections(copySectionData!));
    if (response.payload.success) {
      await dispatch(
        getEstimateItems({ estimate_id: estimateDetail?.estimate_id })
      );
      setConfirmCopyDialogOpen(false);
      setIsLoading(false);
    }
  };

  const handleDelete = async () => {
    setIsLoading(true);
    const response: any = await dispatch(deleteSections(selectDeletedItems!));
    if (response.payload.success) {
      dispatch(removeSection(selectDeletedItems?.section_id));
      setConfirmDialogOpen(false);
      setIsLoading(false);
      return;
    }
  };

  const costItemsFromDatabase = useMemo(() => {
    const data = costItemDatabaseOpen?.items
      ?.filter(
        (item) => Number((item as IWorkorderDetailsItem)?.reference_item_id) > 0
      )
      ?.map((item) => {
        let temData: Partial<CIDBItemSideData> = {
          name: item.subject || "",
          reference_item_id: item?.reference_item_id,
          item_id: String(item?.item_id),
        };
        if (item?.item_type_key === "item_material") {
          temData.material_id = item?.reference_item_id;
          temData.item_type = "161";
          temData.type_name = "Material";
        } else if (item?.item_type_key === "item_labour") {
          temData.labor_id = item?.reference_item_id;
          temData.item_type = "163";
          temData.type_name = "Labor";
        } else if (item?.item_type_key === "item_sub_contractor") {
          temData.contractor_id = item?.reference_item_id;
          temData.item_type = "164";
          temData.type_name = "Subcontractor";
        } else if (item?.item_type_key === "item_equipment") {
          temData.equipment_id = item?.reference_item_id;
          temData.item_type = "162";
          temData.type_name = "Equipment";
        } else if (item?.item_type_key === "item_other") {
          temData.other_item_id = item?.reference_item_id;
          temData.item_type = "165";
          temData.type_name = "Other Items";
        }
        return temData as Partial<CIDBItemSideData>;
      });
    return data;
  }, [costItemDatabaseOpen?.items]);
  const handleItemFromCostItemDatabase = async (data: CIDBItemSideData[]) => {
    // contractor_id; use this instead of sub_contract_id

    if (!costItemDatabaseOpen?.section_id) {
      notification.error({
        description: "No valid section ID found",
      });
      return;
    }
    const items = costItemDatabaseOpen?.items;
    const removedItemIds = items
      ?.filter((jobItem) => {
        if (jobItem.reference_item_id && jobItem.reference_item_id > 0) {
          return !data.some((item) => {
            if (item?.type_name === "Material") {
              return item.material_id === jobItem.reference_item_id;
            } else if (item?.type_name === "Labor") {
              return (
                Number(jobItem.reference_item_id) === Number(item.labor_id)
              );
            } else if (item?.type_name === "Subcontractor") {
              return (
                Number(jobItem.reference_item_id) === Number(item.contractor_id)
              );
            } else if (item?.type_name === "Equipment") {
              return (
                Number(jobItem.reference_item_id) === Number(item.equipment_id)
              );
            } else if (item?.type_name === "Other Items") {
              return (
                Number(jobItem.reference_item_id) === Number(item.other_item_id)
              );
            } else if (item?.type_name === "Groups") {
              return Number(jobItem.reference_item_id) === Number(item.item_id);
            }
          });
        }
      })
      .map((item) => Number(item.item_id));

    // const mutationPromises = [];
    // if (removedItemIds.length) {
    //   removedItemIds.forEach((id) => {
    //     mutationPromises.push(handleWorkorderItemDelete(id));
    //   });
    // }

    if (!costItemDatabaseOpen?.section_id) {
      return;
    }

    const itemsToBeAdd = data
      .filter(
        (jobItem) =>
          !items?.some((item) => {
            if (jobItem?.type_name === "Material") {
              return (
                Number(item.reference_item_id) === Number(jobItem.material_id)
              );
            } else if (jobItem?.type_name === "Labor") {
              return (
                Number(item.reference_item_id) === Number(jobItem.labor_id)
              );
            } else if (jobItem?.type_name === "Subcontractor") {
              return (
                Number(item.reference_item_id) === Number(jobItem.contractor_id)
              );
            } else if (jobItem?.type_name === "Equipment") {
              return (
                Number(item.reference_item_id) === Number(jobItem.equipment_id)
              );
            } else if (jobItem?.type_name === "Other Items") {
              return (
                Number(item.reference_item_id) === Number(jobItem.other_item_id)
              );
            } else if (jobItem?.type_name === "Group") {
              return Number(item.reference_item_id) === Number(jobItem.item_id);
            }
          })
      )
      ?.map((item, index) => {
        let temData: IEstimateAddItem = {
          subject: item.name,
          quantity: item.quantity ?? 0,
          unit: item.unit,
          hidden_markup: item.hidden_markup,
          // We have used it, because in the common comp.(AddMultiselectDirectorySide), you have added the unit cost & cost code Id as unitCost & costCodeId instead of unit_cost, please check that
          unit_cost: item.unit_cost
            ? Number(item.unit_cost)
            : Number(item?.unitCost) ?? "",
          cost_code_id: item.cost_code_id ?? item?.costCodeId ?? "",
          markup: item.markup?.toString() ?? "",
          is_markup_percentage: 1,
          total: item.total,
          assigned_to: "",
          assigned_to_contact_id: 0,
          contractor_id: item.contractor_id?.toString() || "",
          contractor_contact_id: 0,
          is_cost_item: 1,
          is_optional_item: costItemDatabaseOpen?.is_optional_section ? 1 : 0,
          // description: item.description,
          description: item.notes || "",
          internal_notes: item?.internal_notes || item?.internalNotes,
          item_type: item?.item_type?.toString() || "",
          section_id: costItemDatabaseOpen?.section_id,
          estimate_item_no:
            index + 1 + (costItemDatabaseOpen?.items?.length ?? 0),
        };
        if (item?.type_name && item.type_name in typeMapping) {
          const { idKey, taxKey } = typeMapping[item.type_name];
          temData.reference_item_id = item[idKey]?.toString();
          temData.apply_global_tax = Number(gSettings[taxKey]);
        } else if (item?.type_name === "Group") {
          // is_taxable_equipment_items and much more here logically
          const foundId = possibleIds.find((key) => item[key] !== undefined);
          temData.reference_item_id = foundId ? item[foundId]!.toString() : "";
          temData.apply_global_tax = foundId
            ? Number(
                gSettings[
                  `is_taxable_${foundId.replace(
                    "_id",
                    ""
                  )}_items` as keyof GSettings
                ]
              )
            : 0;
        }
        return temData;
      });
    if (removedItemIds?.length) {
      const payloadDelete = {
        delete_multiple_item: 1,
        multiple_item_id: removedItemIds,
        estimate_id: estimateDetail?.estimate_id?.toString(),
      };
      const responseDelete: any = await dispatch(
        removeEstimateItems(payloadDelete)
      );
      if (responseDelete?.payload?.success) {
        dispatch(bulkRemoveItems({ itemIds: removedItemIds }));
      }
    }
    if (itemsToBeAdd?.length) {
      const payload = {
        estimate_id: estimateDetail?.estimate_id,
        items: itemsToBeAdd,
      };
      const response: any = await dispatch(addEstimateItems(payload));
      if (response?.payload?.success && response?.payload?.data?.length) {
        dispatch(
          addBulkItemsFromCostDatabase({
            sectionId: costItemDatabaseOpen?.section_id,
            itemsData: response?.payload?.data,
          })
        );
      }
    }

    // if (itemsToBeAdd.length) {
    //   // const workorderItem = {
    //   //   work_order_id: Number(params?.id),
    //   //   from: "panel",
    //   //   items: itemsToBeAdd,
    //   // };
    //   // const formData = getValuableObj(workorderItem);
    //   // mutationPromises.push(addWorkOrderItemCallBack(formData));
    // }

    // await Promise.all(mutationPromises);
  };

  const addGridDropZone = (side: number, api: GridApi) => {
    gridApi.forEach((ele, index) => {
      if (ele && side !== index) {
        const dropApi = ele;
        const dropZone = dropApi?.getRowDropZoneParams();
        if (dropZone) {
          api?.addRowDropZone(dropZone);
        }
      }
    });
  };

  useEffect(() => {
    if (gridApi?.length === sections?.length) {
      gridApi.map((ele, index) => {
        if (ele) {
          addGridDropZone(index, ele);
        }
      });
    }
  }, [gridApi, sections]);

  const onGridReady = (
    side: number,
    params: GridReadyEvent,
    singleSec: ESSection
  ) => {
    setGridApi((old) => {
      const data = [];
      for (let i = 0; i < sections?.length; i++) {
        if (i === side) {
          data?.push(params?.api);
        } else {
          data?.push(old?.[i]);
        }
      }
      return data;
    });
  };
  const actionHandlers = useMemo(
    () => ({
      setSectionOpen,
      setItemsOpen,
      setItemsData,
      setIsItemAdd,
      setSectionData,
      setConfirmDialogOpen,
      setSelectedItemId,
      setSelectedSectionId,
      setIsDeleteConfirmOpen,
      setCostItemDatabaseOpen,
      setImportEstimateFrom1Build,
      setCopySectionData,
      setConfirmCopyDialogOpen,
      setEstimateBulkMarkup,
      setSelectDeletedItems,
    }),
    [
      setSectionOpen,
      setItemsOpen,
      setItemsData,
      setIsItemAdd,
      setSectionData,
      setConfirmDialogOpen,
      setSelectedItemId,
      setSelectedSectionId,
      setIsDeleteConfirmOpen,
      setCostItemDatabaseOpen,
      setImportEstimateFrom1Build,
      setCopySectionData,
      setConfirmCopyDialogOpen,
      setEstimateBulkMarkup,
      setSelectDeletedItems,
    ]
  );
  if (isSectionLoading) {
    return <Spin className="w-full flex justify-center items-center h-20" />;
  }

  return (
    <>
      <div className="grid gap-2.5">
        <DndProvider backend={HTML5Backend}>
          {sections?.map((singleSection, index) => (
            <DragComponent
              actionHandlers={actionHandlers}
              singleSection={singleSection}
              key={singleSection?.section_id}
              index={index}
              isReadOnly={isReadOnly}
              onGridReady={onGridReady}
              ParentIsReadOnly={ParentIsReadOnly}
            />
          ))}
        </DndProvider>
        {isDeleteConfirmOpen && (
          <ConfirmModal
            isOpen={isDeleteConfirmOpen}
            modaltitle={_t("Delete")}
            description={_t("Are you sure you want to delete this Item?")}
            modalIcon="fa-regular fa-trash-can"
            isLoading={isLoading}
            onAccept={handleDeleteSectionItem}
            onDecline={() => {
              setIsDeleteConfirmOpen(false);
            }}
            onCloseModal={() => {
              setIsDeleteConfirmOpen(false);
            }}
          />
        )}
      </div>

      <SectionOpen
        sectionOpen={sectionOpen}
        setSectionOpen={setSectionOpen}
        isViewOnly={isReadOnly}
        isSectionAdd={false}
        formData={sectionData ?? {}}
        onClose={() => {
          setSectionData({});
          setIsSectionAdd(false);
          setSectionOpen(false);
        }}
      />
      {itemsOpen && (
        <ESSectionItem
          formData={itemsData}
          isViewOnly={isReadOnly}
          isReadOnly={isReadOnly}
          itemOpen={itemsOpen}
          isItemAdd={isItemAdd}
          setEstimateItemToView={setItemsData}
          setItemOpen={setItemsOpen}
          onClose={() => {
            setIsItemAdd(false);
            setItemsOpen(false);
            setItemsData({});
          }}
          ParentIsReadOnly={ParentIsReadOnly}
        />
      )}
      <EstimateBulkMarkup
        estimateBulkMarkup={estimateBulkMarkup}
        setEstimateBulkMarkup={setEstimateBulkMarkup}
      />
      <ConfirmModal
        isOpen={confirmDialogOpen}
        modaltitle={_t("Delete")}
        isLoading={isLoading}
        description="Are you sure you want to delete this Section?"
        onAccept={() => {
          handleDelete();
        }}
        onDecline={() => setConfirmDialogOpen(false)}
        onCloseModal={() => setConfirmDialogOpen(false)}
        descriptionclassName="flex text-center justify-center"
        modalIcon="fa-regular fa-trash-can"
      />

      {confirmCopyDialogOpen && (
        <ConfirmModal
          isOpen={confirmCopyDialogOpen}
          modaltitle={_t("Copy Section")}
          description="Are you sure you want to copy this Section?"
          onAccept={() => {
            handleCopySection();
          }}
          isLoading={isLoading}
          onDecline={() => setConfirmCopyDialogOpen(false)}
          onCloseModal={() => setConfirmCopyDialogOpen(false)}
          descriptionclassName="flex text-center justify-center"
          modalIcon="fa-regular fa-clone"
        />
      )}
      {costItemDatabaseOpen !== null && (
        <CidbItemDrawer
          closeDrawer={() => {
            setCostItemDatabaseOpen(null);
          }}
          options={[
            "material",
            "labor",
            "equipment",
            "subcontractor",
            "other_items",
            "groups",
          ]}
          singleSelecte={false}
          addItem={(data) => {
            handleItemFromCostItemDatabase(data as CIDBItemSideData[]);
          }}
          // itemTypes={itemTypes.map((item) => {
          //   return {
          //     ...item,
          //     default_color: item.default_color?.toString(),
          //   };
          // })}
          openSendEmailSidebar={costItemDatabaseOpen !== null}
          // data={[]}
          data={costItemsFromDatabase as Partial<CIDBItemSideData>[]}
          cidbModuleVIseIdAndValue={{
            [CFConfig.material_key]: {
              id: 161,
              value: CFConfig.material_key,
            },
            [CFConfig.labor_key]: {
              id: 163,
              value: CFConfig.labor_key,
            },
            [CFConfig.equipment_key]: {
              id: 162,
              value: CFConfig.equipment_key,
            },

            [CFConfig.subcontractor_key]: {
              id: 164,
              value: CFConfig.subcontractor_key,
            },
            [CFConfig.other_items_key]: {
              id: 165,
              value: CFConfig.other_items_key,
            },
          }}
          isHiddenMarkupApply={true}
        />
      )}
      <OneBuildImportItems
        // isViewOnly
        itemsDatabase={importEstimateFrom1Build}
        setItemsDatabase={setImportEstimateFrom1Build}
      />
    </>
  );
};

export default EstimateItems;

export const ReadOnlyModuleStatsuKey: Record<string, boolean> = {
  estimate_lost: true,
  estimate_completed: true,
  estimate_approved: true,
  estimate_pending_approval: true,
  estimate_rebid: true,
};
