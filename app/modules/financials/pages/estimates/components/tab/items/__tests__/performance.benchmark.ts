/**
 * Performance benchmark tests for DragComponent refactoring
 * Compares original vs refactored component performance
 */

import { performance } from 'perf_hooks';

// Mock data generators
const generateMockSection = (itemCount: number): ESSection => ({
  section_id: 1,
  section_name: `Test Section with ${itemCount} items`,
  code_id: 'PERF001',
  items: Array.from({ length: itemCount }, (_, i) => ({
    item_id: i + 1,
    section_id: 1,
    subject: `Performance Test Item ${i + 1}`,
    quantity: Math.floor(Math.random() * 100) + 1,
    unit_cost: Math.floor(Math.random() * 10000) + 1000,
    unit: 'each',
    markup: Math.floor(Math.random() * 20) + 5,
    is_markup_percentage: true,
    apply_global_tax: Math.random() > 0.5,
    item_type_name: ['Material', 'Labor', 'Equipment'][Math.floor(Math.random() * 3)],
    cost_code_name: `Code ${i + 1}`,
    cost_code_id: i + 1,
  })),
});

const generateMockSections = (sectionCount: number, itemsPerSection: number): ESSection[] =>
  Array.from({ length: sectionCount }, (_, i) => ({
    ...generateMockSection(itemsPerSection),
    section_id: i + 1,
    section_name: `Section ${i + 1}`,
  }));

// Performance test utilities
class PerformanceBenchmark {
  private results: { [key: string]: number[] } = {};

  startTimer(testName: string): () => number {
    const start = performance.now();
    return () => {
      const end = performance.now();
      const duration = end - start;
      
      if (!this.results[testName]) {
        this.results[testName] = [];
      }
      this.results[testName].push(duration);
      
      return duration;
    };
  }

  getAverageTime(testName: string): number {
    const times = this.results[testName] || [];
    return times.reduce((sum, time) => sum + time, 0) / times.length;
  }

  getResults(): { [key: string]: { average: number; min: number; max: number; count: number } } {
    const results: any = {};
    
    Object.keys(this.results).forEach(testName => {
      const times = this.results[testName];
      results[testName] = {
        average: this.getAverageTime(testName),
        min: Math.min(...times),
        max: Math.max(...times),
        count: times.length,
      };
    });
    
    return results;
  }

  reset(): void {
    this.results = {};
  }
}

// Benchmark tests
export const runPerformanceBenchmarks = () => {
  const benchmark = new PerformanceBenchmark();
  
  console.log('🚀 Starting DragComponent Performance Benchmarks...\n');

  // Test 1: Section Metrics Calculation
  console.log('📊 Testing Section Metrics Calculation...');
  const testSectionMetrics = (itemCount: number, iterations: number = 100) => {
    const section = generateMockSection(itemCount);
    
    for (let i = 0; i < iterations; i++) {
      const endTimer = benchmark.startTimer(`sectionMetrics_${itemCount}items`);
      
      // Simulate section metrics calculation
      const filteredItems = section.items.filter(item => 
        !item.is_optional_item && (item.quantity * item.unit_cost) > 0
      );
      
      const subtotal = filteredItems.reduce((sum, item) => {
        const itemTotal = item.quantity * (item.unit_cost / 100);
        const markup = item.is_markup_percentage 
          ? itemTotal * (item.markup / 100)
          : item.markup / 100;
        return sum + itemTotal + markup;
      }, 0);
      
      const tax = filteredItems
        .filter(item => item.apply_global_tax)
        .reduce((sum, item) => sum + (item.quantity * item.unit_cost / 100), 0) * 0.1;
      
      const grandTotal = subtotal + tax;
      
      endTimer();
    }
  };

  // Test different item counts
  [10, 50, 100, 500].forEach(itemCount => {
    testSectionMetrics(itemCount);
  });

  // Test 2: Memoization Effectiveness
  console.log('🧠 Testing Memoization Effectiveness...');
  const testMemoization = (iterations: number = 1000) => {
    const section = generateMockSection(100);
    let memoizedResult: any = null;
    
    for (let i = 0; i < iterations; i++) {
      const endTimer = benchmark.startTimer('memoization_test');
      
      // Simulate memoized calculation
      if (!memoizedResult || i % 10 === 0) { // Recalculate every 10th iteration
        memoizedResult = {
          subtotal: section.items.reduce((sum, item) => sum + (item.quantity * item.unit_cost), 0),
          itemCount: section.items.length,
        };
      }
      
      // Use memoized result
      const result = memoizedResult;
      
      endTimer();
    }
  };

  testMemoization();

  // Test 3: Column Definition Generation
  console.log('📋 Testing Column Definition Generation...');
  const testColumnGeneration = (iterations: number = 100) => {
    for (let i = 0; i < iterations; i++) {
      const endTimer = benchmark.startTimer('columnGeneration');
      
      // Simulate column definition generation
      const columnDefs = [
        { field: 'move', width: 30 },
        { field: 'checkbox', width: 40 },
        { field: 'item_type_name', width: 50 },
        { field: 'subject', flex: 2 },
        { field: 'cost_code_id', flex: 2 },
        { field: 'quantity', width: 80 },
        { field: 'unit_cost', width: 130 },
        { field: 'unit', width: 100 },
        { field: 'markup', width: 100 },
        { field: 'total', width: 130 },
      ];
      
      endTimer();
    }
  };

  testColumnGeneration();

  // Test 4: Memory Usage Simulation
  console.log('💾 Testing Memory Usage Patterns...');
  const testMemoryUsage = () => {
    const sections = generateMockSections(20, 50); // 20 sections with 50 items each
    
    const endTimer = benchmark.startTimer('memoryUsage');
    
    // Simulate component rendering with many sections
    sections.forEach(section => {
      // Simulate filtering and calculations for each section
      const filteredItems = section.items.filter(item => item.quantity > 0);
      const metrics = {
        subtotal: filteredItems.reduce((sum, item) => sum + (item.quantity * item.unit_cost), 0),
        tax: filteredItems.filter(item => item.apply_global_tax).length * 100,
      };
    });
    
    endTimer();
  };

  testMemoryUsage();

  // Test 5: Drag and Drop Performance
  console.log('🖱️ Testing Drag and Drop Performance...');
  const testDragDrop = (iterations: number = 50) => {
    const section = generateMockSection(100);
    
    for (let i = 0; i < iterations; i++) {
      const endTimer = benchmark.startTimer('dragDrop');
      
      // Simulate drag and drop operation
      const items = [...section.items];
      const fromIndex = Math.floor(Math.random() * items.length);
      const toIndex = Math.floor(Math.random() * items.length);
      
      // Move item
      const [movedItem] = items.splice(fromIndex, 1);
      items.splice(toIndex, 0, movedItem);
      
      // Update order numbers
      items.forEach((item, index) => {
        item.order_number = index + 1;
      });
      
      endTimer();
    }
  };

  testDragDrop();

  // Print results
  console.log('\n📈 Performance Benchmark Results:');
  console.log('=====================================');
  
  const results = benchmark.getResults();
  Object.keys(results).forEach(testName => {
    const result = results[testName];
    console.log(`\n${testName}:`);
    console.log(`  Average: ${result.average.toFixed(2)}ms`);
    console.log(`  Min: ${result.min.toFixed(2)}ms`);
    console.log(`  Max: ${result.max.toFixed(2)}ms`);
    console.log(`  Iterations: ${result.count}`);
  });

  // Performance recommendations
  console.log('\n💡 Performance Recommendations:');
  console.log('================================');
  
  const sectionMetrics100 = results['sectionMetrics_100items'];
  const sectionMetrics500 = results['sectionMetrics_500items'];
  
  if (sectionMetrics100 && sectionMetrics500) {
    const scalingFactor = sectionMetrics500.average / sectionMetrics100.average;
    console.log(`- Section metrics calculation scales ${scalingFactor.toFixed(2)}x with 5x more items`);
    
    if (scalingFactor > 6) {
      console.log('  ⚠️  Consider optimizing calculation algorithms');
    } else {
      console.log('  ✅ Scaling performance is acceptable');
    }
  }

  const memoResult = results['memoization_test'];
  if (memoResult && memoResult.average < 0.1) {
    console.log('- ✅ Memoization is highly effective');
  } else {
    console.log('- ⚠️  Consider improving memoization strategies');
  }

  console.log('\n🎯 Benchmark Complete!');
  
  return results;
};

// Export for use in tests
export { PerformanceBenchmark, generateMockSection, generateMockSections };
