import React, { forwardRef } from "react";
import { StaticTable } from "~/shared/components/molecules/staticTable";
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { Spin } from "~/shared/components/atoms/spin";
import { RowDragEvent } from "ag-grid-community";

interface ItemGridProps {
  isInView: boolean;
  isReadOnly: boolean;
  singleSection: ESSection;
  columnDefs: any[];
  onGridReady: (index: number, params: any, section: ESSection) => void;
  onRowDragEnd: (event: RowDragEvent<any, any>, section: Partial<ESSection>) => void;
  index: number;
  gridRef: React.RefObject<any>;
}

/**
 * ItemGrid component - handles the AgGrid table display
 * Extracted from DragComponent to improve code organization
 */
export const ItemGrid = forwardRef<HTMLDivElement, ItemGridProps>(({
  isInView,
  isReadOnly,
  singleSection,
  columnDefs,
  onGridReady,
  onRowDragEnd,
  index,
  gridRef,
}, ref) => {
  if (!isInView) {
    return (
      <div className="w-full common-card flex justify-center items-center h-20">
        <Spin />
      </div>
    );
  }

  return (
    <div ref={ref}>
      <div className="p-2 common-card relative before:h-0.5 before:w-[25px] before:absolute before:!-left-[35px] before:top-5 before:bg-gradient-to-r from-primary-500">
        <div className="ag-theme-alpine">
          <StaticTable
            ref={gridRef}
            suppressContextMenu={true}
            suppressDragLeaveHidesColumns={true}
            suppressMoveWhenRowDragging={true}
            className="static-table"
            suppressRowClickSelection={true}
            rowDragManaged={!isReadOnly}
            animateRows={true}
            onRowDragEnd={(e) => onRowDragEnd(e, singleSection)}
            onGridReady={(params) =>
              onGridReady(index, params, singleSection as ESSection)
            }
            columnDefs={columnDefs}
            rowData={singleSection?.items}
            noRowsOverlayComponent={() => (
              <NoRecords
                image={`${window.ENV.CDN_URL}assets/images/no-records-item-list.svg`}
              />
            )}
          />
        </div>
      </div>
    </div>
  );
});
