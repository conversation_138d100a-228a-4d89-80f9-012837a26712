import { useMemo } from "react";
import { useAppESSelector } from "../../../../redux/store";
import { itemTotalCalculator } from "../../details/EstimatesCalc";

/**
 * Custom hook for calculating section metrics (subtotal, tax, markup, etc.)
 * Extracted from DragComponent to improve performance and reusability
 */
export const useSectionMetrics = (
  singleSection: ESSection | undefined,
  taxRate: number,
  taxIsReversible: boolean
) => {
  const { flags } = useAppESSelector((state) => state.estimateItems);

  const sectionMetrics = useMemo(() => {
    if (!singleSection?.items) {
      return {
        estimatedCost: 0,
        subtotalTaxable: 0,
        subtotal: 0,
        markup: 0,
        profitMargin: "0%",
        tax: 0,
        grandTotal: 0,
      };
    }

    const items = singleSection.items;

    const filteredItems =
      items
        ?.filter((el) => {
          const totalCost = Number(el?.quantity) * Number(el?.unit_cost);
          return (
            !el?.is_optional_item &&
            (flags?.showZeroQuantityItemsOnly ? totalCost == 0 : true)
          );
        })
        ?.map((el) => ({
          ...el,
          unit_cost: `${Number(el.unit_cost) / 100}`,
          markup: el?.is_markup_percentage
            ? Number(el?.markup)
            : Number(el?.markup) / 100,
        })) ?? [];

    const calculateSum = (
      array: ESEstimateItem[],
      callback: (item: ESEstimateItem) => number
    ): number =>
      array?.map(callback)?.reduce((sum, value) => sum + value, 0) || 0;

    const estimatedCost = calculateSum(filteredItems, (ite) =>
      itemTotalCalculator(ite, false)
    );
    
    const subtotal = calculateSum(filteredItems, (ite) =>
      itemTotalCalculator(ite, !flags?.isMarkupHidden)
    );

    const subtotalTaxable = calculateSum(
      filteredItems.filter((ite) =>
        typeof ite?.apply_global_tax === "boolean"
          ? ite?.apply_global_tax
          : ite?.apply_global_tax?.toString() === "1"
      ),
      (ite) => itemTotalCalculator(ite, !flags?.isMarkupHidden)
    );

    const markup = calculateSum(filteredItems, (item) =>
      item?.is_markup_percentage
        ? Number(item?.unit_cost) *
          (Number(item?.markup) / 100) *
          item?.quantity
        : Number(item?.unit_cost) * item?.quantity
        ? Number(item?.markup) - Number(item?.unit_cost) * item?.quantity
        : 0
    );

    const profitMargin = estimatedCost
      ? ((markup / estimatedCost) * 100).toFixed(2) + "%"
      : "0%";

    const tax = taxIsReversible ? 0 : subtotalTaxable * (taxRate / 100);
    const grandTotal = subtotal + tax;

    return {
      estimatedCost,
      subtotalTaxable,
      subtotal,
      markup,
      profitMargin,
      tax,
      grandTotal,
    };
  }, [singleSection?.items, flags, taxRate, taxIsReversible]);

  return sectionMetrics;
};
