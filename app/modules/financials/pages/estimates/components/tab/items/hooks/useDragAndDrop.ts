import { useCallback } from "react";
import { useDrag, useDrop } from "react-dnd";
import { RowDragEvent } from "ag-grid-community";
import { useAppESDispatch, useAppESSelector } from "../../../../redux/store";
import {
  updateEstimateItemsOrder,
  updateEstimateSectionOrder,
} from "../../../../redux/action/ESItemAction";
import {
  moveSectionState,
  setSection,
} from "../../../../redux/slices/ESItemSlice";
import { useTranslation } from "~/hook";

interface UseDragAndDropProps {
  index: number;
  isReadOnly: boolean;
  singleSection: ESSection;
}

/**
 * Custom hook for handling drag and drop functionality
 * Extracted from DragComponent to improve code organization and reusability
 */
export const useDragAndDrop = ({
  index,
  isReadOnly,
  singleSection,
}: UseDragAndDropProps) => {
  const dispatch = useAppESDispatch();
  const { _t } = useTranslation();
  const { sections } = useAppESSelector((state) => state.estimateItems);
  const { estimateDetail } = useAppESSelector((state) => state.estimateDetail);

  // Section drag and drop logic
  const [{ isDragging }, dragRef] = useDrag({
    type: "SECTION",
    item: { index },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
    end: async (draggedItem, monitor) => {
      let payloadSec: Array<Partial<ESEstimateItem>> = [];
      const updatedSections = sections?.map((section, idx) => {
        payloadSec?.push({
          section_id: section?.section_id,
          custom_section_id: idx + 1,
        });

        return {
          ...section,
          custom_section_id: idx + 1,
        };
      });

      try {
        const update_Section = await dispatch(
          updateEstimateSectionOrder({
            estimate_id: estimateDetail?.estimate_id,
            sectionItems: payloadSec,
          })
        );
        const response = update_Section?.payload as IEstimatesItemsApiRes;

        if (response?.success) {
          dispatch(
            moveSectionState({ fromIndex: index, toIndex: draggedItem?.index })
          );
          dispatch(setSection(updatedSections));
        } else {
          notification.error({
            description: response?.message,
          });
          dispatch(setSection(updatedSections));
        }
      } catch (error) {
        notification.error({
          description: error?.toString(),
        });
        dispatch(setSection(sections));
      }
    },
  });

  const [{ isOver }, dropRef] = useDrop<DragItem, void, { isOver: boolean }>({
    accept: "SECTION",
    hover: (item) => {
      if (item.index === index) return;
      dispatch(moveSectionState({ fromIndex: item.index, toIndex: index }));
      item.index = index;
    },
    collect: (monitor) => ({
      isOver: monitor.isOver(),
    }),
  });

  // Item drag and drop logic
  const handleDragEnd = useCallback(
    async (event: RowDragEvent<any, any>, sectionDet: Partial<ESSection>) => {
      if (isReadOnly) return;
      
      const prevSection = event.node.data.section_id;
      const prevRow = event.node.rowIndex;
      const nextSection = sectionDet?.section_id || null;
      let nextRow = event.overIndex === -1 ? 0 : event.overIndex;

      const newItems: ESEstimateItem[] = [];
      let newPayload: {
        item_id: number;
        order_number: number;
        section_id: number;
      }[] = [];

      event?.api?.forEachNode((e, index) => {
        newItems?.push({
          ...e?.data,
          estimate_item_no: index + 1,
          order_number: index + 1,
        });
      });

      const Newsections = JSON.parse(JSON.stringify(sections)) as ESSection[];
      
      if (nextSection !== prevSection) {
        // Moving between sections
        const PreviousSection = Newsections?.find(
          (sec) => sec.section_id === prevSection
        );
        const NextSection = Newsections?.find(
          (sec) => sec.section_id === nextSection
        );

        if (NextSection && PreviousSection) {
          if (prevRow !== null) {
            NextSection?.items?.splice(nextRow, 0, {
              ...PreviousSection?.items?.[prevRow],
              section_id: nextSection ?? 0,
            });
            PreviousSection?.items?.splice(prevRow, 1);
          }
          newPayload = [
            ...(PreviousSection?.items?.map((el, i) => ({
              item_id: Number(el?.item_id),
              order_number: i + 1,
              section_id: Number(el?.section_id),
            })) ?? []),
            ...(NextSection?.items?.map((el, i) => ({
              item_id: Number(el?.item_id),
              order_number: i + 1,
              section_id: Number(el?.section_id),
            })) ?? []),
          ];
        }
        
        dispatch(setSection(Newsections));
        const update_Item = await dispatch(
          updateEstimateItemsOrder({
            estimate_id: estimateDetail?.estimate_id,
            items: newPayload,
          })
        );
        const response = update_Item?.payload as IEstimatesItemsApiRes;
        
        if (!response?.success) {
          notification.error({
            description: _t("Item Order Not updated in between Sections."),
          });
          dispatch(setSection(sections));
        }
      }
      
      if (nextSection === prevSection) {
        // Rearranging within the same section
        const UpdateSameSection = Newsections?.find(
          (sec) => sec.section_id === prevSection
        );
        if (UpdateSameSection) {
          UpdateSameSection.items = newItems;
          newPayload =
            newItems?.map((el, i) => ({
              item_id: Number(el?.item_id),
              order_number: i + 1,
              section_id: Number(el?.section_id),
            })) ?? [];
        }
        
        dispatch(setSection(Newsections));
        const update_Item = await dispatch(
          updateEstimateItemsOrder({
            estimate_id: estimateDetail?.estimate_id,
            items: newPayload,
          })
        );
        const response = update_Item?.payload as IEstimatesItemsApiRes;
        
        if (!response?.success) {
          notification.error({
            description: _t("Item Order Not updated in Section."),
          });
          dispatch(setSection(sections));
        }
      }
    },
    [dispatch, isReadOnly, sections, estimateDetail?.estimate_id, _t]
  );

  const dragAndDropRef = (node: HTMLElement) => {
    dragRef(node);
    dropRef(node);
  };

  return {
    isDragging,
    isOver,
    dragAndDropRef,
    handleDragEnd,
  };
};
