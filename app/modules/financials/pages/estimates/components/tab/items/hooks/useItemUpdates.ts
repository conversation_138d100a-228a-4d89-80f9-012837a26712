import { useCallback, useState } from "react";
import { useAppESDispatch, useAppESSelector } from "../../../../redux/store";
import { updateEstimateItems } from "../../../../redux/action/ESItemAction";
import { updateItem } from "../../../../redux/slices/ESItemSlice";
import { itemTotalCalculator } from "../../details/EstimatesCalc";

interface UseItemUpdatesProps {
  singleSection: ESSection;
}

/**
 * Custom hook for handling item updates and API calls
 * Extracted from DragComponent to improve code organization and reusability
 */
export const useItemUpdates = ({ singleSection }: UseItemUpdatesProps) => {
  const dispatch = useAppESDispatch();
  const { estimateDetail } = useAppESSelector((state) => state.estimateDetail);
  const [isLoadingCheckBox, setIsLoadingCheckBox] = useState<
    Record<number, boolean>
  >({});

  const updateItemField = useCallback(
    async ({
      itemId,
      updatedItem,
      itemData,
    }: {
      itemId: number;
      updatedItem: Partial<ESEstimateItem>;
      itemData?: ESEstimateItem;
    }) => {
      // if unit and quantity is updated then send total value to backend
      const updatedItemKeys = Object.keys(updatedItem);
      
      if (updatedItemKeys.includes("apply_global_tax")) {
        setIsLoadingCheckBox((prev) => ({ ...prev, [itemId]: true }));
      }
      
      if (
        itemData &&
        (updatedItemKeys.includes("unit") ||
          updatedItemKeys.includes("quantity") ||
          updatedItemKeys.includes("markup") ||
          updatedItemKeys.includes("unit_cost"))
      ) {
        updatedItem["total"] = itemTotalCalculator(
          {
            ...itemData,
            ...updatedItem,
          },
          true
        );
      }
      
      dispatch(
        updateItem({
          sectionId: singleSection?.section_id,
          itemId,
          updatedItem,
        })
      );
      
      const apiRes = await dispatch(
        updateEstimateItems({
          estimate_id: estimateDetail?.estimate_id,
          items: [
            {
              ...updatedItem,
              section_id: singleSection?.section_id,
              item_id: itemId,
            },
          ],
        })
      );

      const response = apiRes.payload as IEstimatesItemsApiRes;

      if (response?.success) {
        setIsLoadingCheckBox((prev) => ({ ...prev, [itemId]: false }));
      } else {
        dispatch(
          updateItem({
            sectionId: singleSection?.section_id,
            itemId,
            updatedItem: itemData,
          })
        );
        notification.error({
          description: response?.message,
        });
        setIsLoadingCheckBox((prev) => ({ ...prev, [itemId]: false }));
      }
      
      return response;
    },
    [dispatch, singleSection?.section_id, estimateDetail?.estimate_id]
  );

  return {
    updateItemField,
    isLoadingCheckBox,
    setIsLoadingCheckBox,
  };
};
