import { useMemo, useState } from "react";
import { useInView } from "react-intersection-observer";
import { CollapseSingleTable } from "~/shared/components/molecules/collapseSingleTable";
import { ConfirmModal } from "~/shared/components/molecules/confirmModal";
import { sanitizeString } from "~/helpers/helper";
import { useTranslation } from "~/hook";
import { getGSettings } from "~/zustand";
import { useGlobalModule } from "~/zustand/global/modules/slice";
import { getGlobalUser } from "~/zustand/global/user/slice";
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";
import { useAppESSelector } from "../../../redux/store";

// Import custom hooks
import {
  useSectionMetrics,
  useDragAndDrop,
  useItemUpdates,
  useGridConfiguration,
  useItemSelection,
  usePerformanceOptimization,
  useMemoryManagement,
} from "./hooks";

// Import sub-components
import { ActionMenu, ItemGrid, useColumnDefinitions } from "./components";

/**
 * Refactored DragComponent - Main orchestrator component
 * Uses custom hooks and sub-components for better organization and performance
 */
const DragComponentRefactored = ({
  index,
  isReadOnly,
  onGridReady,
  ParentIsReadOnly,
  singleSection,
  actionHandlers,
}: SectionDraggableProps) => {
  const { _t } = useTranslation();
  const { formatter } = useCurrencyFormatter();
  const gSettings: GSettings = getGSettings();
  const { default_item_view } = gSettings;
  const user: IInitialGlobalData["user"] = getGlobalUser();
  const { getGlobalModuleByKey } = useGlobalModule();

  // State
  const [costchangeConfirmOpen, setCostChangeConfirmation] =
    useState<Partial<ESEstimateItem> | null>(null);

  // Selectors
  const { sections, flags } = useAppESSelector((state) => state.estimateItems);
  const { estimateDetail } = useAppESSelector((state) => state.estimateDetail);

  // Intersection observer for lazy loading
  const { ref, inView: isInView } = useInView({
    threshold: 0.1,
    triggerOnce: false,
  });

  // Tax calculations
  const { taxIsReversible, taxRate } = useMemo(() => {
    return {
      taxIsReversible:
        typeof estimateDetail?.is_reversible_tax === "boolean"
          ? estimateDetail?.is_reversible_tax
          : estimateDetail?.is_reversible_tax?.toString() === "1",
      taxRate: parseFloat(estimateDetail?.tax_rate ?? "0") || 0,
    };
  }, [estimateDetail?.is_reversible_tax, estimateDetail?.tax_rate]);

  // Custom hooks
  const sectionMetrics = useSectionMetrics(
    singleSection,
    taxRate,
    taxIsReversible
  );

  const { isDragging, dragAndDropRef, handleDragEnd } = useDragAndDrop({
    index,
    isReadOnly,
    singleSection,
  });

  const { updateItemField, isLoadingCheckBox } = useItemUpdates({
    singleSection,
  });

  const { gridRef, units } = useGridConfiguration();

  const {
    selectedItems,
    handleSelection,
    handleSelectAll,
    handleUnselectAll,
    isMultipleSelected,
    isAllSelected,
  } = useItemSelection();

  // Performance optimization hook (will be called after columnDefs)
  const performanceData = usePerformanceOptimization({
    singleSection,
    flags,
    isReadOnly,
    selectedItems,
    isLoadingCheckBox,
    sections,
    sectionMetrics,
    columnDefs: [], // Will be updated below
    isInView,
  });

  const {
    filteredCodeCostData,
    filteredSectionItems,
    selectionState,
    sectionKey,
    gridConfig,
    memoizationDeps,
  } = performanceData;

  const { manageGridMemory, registerCleanup } = useMemoryManagement();

  // Column definitions
  const columnDefs = useColumnDefinitions({
    isReadOnly,
    ParentIsReadOnly,
    selectedItems,
    singleSection,
    filteredCodeCostData,
    singleSectionItems: filteredSectionItems || [],
    units,
    flags,
    onSelectionChange: handleSelection,
    onSelectAll: handleSelectAll,
    onUnselectAll: handleUnselectAll,
    updateItemField,
    setCostChangeConfirmation,
  });

  // Action menu items
  const actionMenuItems = ActionMenu({
    singleSection,
    sections,
    estimateDetail,
    actionHandlers,
  });

  // Memoized CollapseSingleTable component
  const CollapseSingleTableMemo = useMemo(() => {
    return (
      <div key={sectionKey} style={{ opacity: isDragging ? 0.5 : 1 }}>
        <CollapseSingleTable
          key={sectionKey}
          style={{
            opacity: isDragging ? 0.5 : 1,
            transform: isDragging ? "scale(1.05)" : "scale(1)",
            transition: "transform 0.2s ease, opacity 0.2s ease",
          }}
          defaultActiveKey={default_item_view ? [] : [1]}
          title={_t(
            `${HTMLEntities.decode(
              sanitizeString(singleSection?.section_name ?? "")
            )}`
          )}
          className={`${
            isReadOnly ? "" : "move-collapse-table"
          } est-item-table`}
          leftsideContant={
            !isReadOnly && (
              <div ref={dragAndDropRef as any}>
                <button
                  className="hover:!bg-primary-8 active:!bg-primary-8 cursor-move absolute top-3.5 left-[5px] w-8 h-8 flex items-center justify-center rounded"
                  title={_t("Move")}
                >
                  <i className="fa-solid fa-grip-dots w-3.5 h-3.5 text-gray-600"></i>
                </button>
              </div>
            )
          }
          centersideContant={
            <div className="flex items-center gap-1.5 bg-primary-8 text-primary-900 py-[3px] px-[9px] rounded-sm dark:bg-dark-800 dark:text-white/90 w-fit whitespace-nowrap hide-collapse">
              <span className="!mb-0 !text-sm !text-primary-900 leading-4 !font-semibold dark:!text-white/90">
                {_t("Total")}:
              </span>
              <span className="!mb-0 !text-sm !text-primary-900 leading-4 !font-semibold dark:!text-white/90 whitespace-nowrap">
                {
                  formatter((sectionMetrics.grandTotal / 100)?.toFixed(2))
                    ?.value_with_symbol
                }
              </span>
            </div>
          }
          rightsideContant={
            !isReadOnly && (
              <div className="flex items-center gap-2">
                {isMultipleSelected && (
                  <div className="flex items-center gap-1.5 bg-blue-100 text-blue-900 py-[3px] px-[9px] rounded-sm dark:bg-blue-800 dark:text-blue-100 w-fit whitespace-nowrap">
                    <span className="!mb-0 !text-sm leading-4 !font-semibold">
                      {_t("Multiple items selected")}
                    </span>
                  </div>
                )}
                <button
                  className="hover:!bg-primary-8 active:!bg-primary-8 w-8 h-8 flex items-center justify-center rounded"
                  title={_t("More")}
                  onClick={() => {
                    // Handle dropdown menu click
                  }}
                >
                  <i className="fa-regular fa-ellipsis-vertical w-3.5 h-3.5 text-gray-600"></i>
                </button>
              </div>
            )
          }
          children={
            <ItemGrid
              ref={ref}
              isInView={isInView}
              isReadOnly={isReadOnly}
              singleSection={singleSection}
              columnDefs={columnDefs}
              onGridReady={onGridReady}
              onRowDragEnd={handleDragEnd}
              index={index}
              gridRef={gridRef}
            />
          }
        />
      </div>
    );
  }, memoizationDeps);

  return (
    <>
      {CollapseSingleTableMemo}

      {/* Cost Change Confirmation Modal */}
      {costchangeConfirmOpen && (
        <ConfirmModal
          isOpen={!!costchangeConfirmOpen}
          modaltitle={_t("Update Cost")}
          description={_t(
            "Do you want to update the cost from the Cost Items Database?"
          )}
          onAccept={() => {
            if (costchangeConfirmOpen) {
              updateItemField({
                itemId: costchangeConfirmOpen.item_id!,
                itemData: costchangeConfirmOpen as ESEstimateItem,
                updatedItem: {
                  unit_cost: costchangeConfirmOpen.updated_unit_cost,
                  modified_unit_cost: costchangeConfirmOpen.updated_unit_cost,
                },
              });
            }
            setCostChangeConfirmation(null);
          }}
          onDecline={() => setCostChangeConfirmation(null)}
          onCloseModal={() => setCostChangeConfirmation(null)}
        />
      )}
    </>
  );
};

export default DragComponentRefactored;
