import React, { useMemo } from "react";
import { useTranslation } from "~/hook";
import { useAppESSelector } from "../../../../redux/store";
import { useGlobalModule } from "~/zustand/global/modules/slice";

interface ActionMenuProps {
  singleSection: ESSection;
  sections: ESSection[];
  estimateDetail: any;
  actionHandlers: {
    setCostItemDatabaseOpen: (section: Partial<ESSection>) => void;
    setItemsOpen: (open: boolean) => void;
    setItemsData: (data: Partial<ESEstimateItem>) => void;
    setIsItemAdd: (add: boolean) => void;
    setImportEstimateFrom1Build: (section: Partial<ESSection>) => void;
    setEstimateBulkMarkup: (section: Partial<ESSection>) => void;
    setConfirmCopyDialogOpen: (open: boolean) => void;
    setCopySectionData: (data: any) => void;
    setConfirmDialogOpen: (open: boolean) => void;
    setSelectDeletedItems: (section: Partial<ESSection>) => void;
  };
}

/**
 * ActionMenu component - handles the section action menu items
 * Extracted from DragComponent to improve code organization
 */
export const ActionMenu: React.FC<ActionMenuProps> = ({
  singleSection,
  sections,
  estimateDetail,
  actionHandlers,
}) => {
  const { _t } = useTranslation();
  const { getGlobalModuleByKey } = useGlobalModule();

  const EstimateModule = useMemo(
    () => getGlobalModuleByKey(CFConfig.estimate_module),
    [getGlobalModuleByKey]
  );

  const { module_name: estimate_module_name = "" } = EstimateModule || {};

  const {
    setCostItemDatabaseOpen,
    setItemsOpen,
    setItemsData,
    setIsItemAdd,
    setImportEstimateFrom1Build,
    setEstimateBulkMarkup,
    setConfirmCopyDialogOpen,
    setCopySectionData,
    setConfirmDialogOpen,
    setSelectDeletedItems,
  } = actionHandlers;

  const menuItems = useMemo(() => [
    {
      label: "Add Items / Modify Section",
      value: "add_item_to_estimate",
      disabled: true,
    },
    {
      label: "Import from Cost Items Database",
      value: "Database",
      onClick: (singleSection: Partial<ESSection>) => {
        setCostItemDatabaseOpen(singleSection);
      },
    },
    {
      label: `Add Manual ${estimate_module_name} Item`,
      value: "add_item_to_estimate",
      onClick: (singleSection: Partial<ESSection>) => {
        setItemsOpen(true);
        setItemsData({
          section_id: singleSection?.section_id,
        });
        setIsItemAdd(true);
      },
    },
    {
      label: "Import from 1build Materials Cost Database",
      value: "1build",
      onClick: (singleSection: Partial<ESSection>) => {
        setImportEstimateFrom1Build(singleSection);
      },
      itemClass: "!bg-deep-orange-500 !text-white",
    },
    {
      label: "Apply Automatic/Bulk Markup",
      value: "apply_automatic_bulk",
      onClick: (singleSection: Partial<ESSection>) => {
        setEstimateBulkMarkup(singleSection);
      },
    },
    {
      label: "Copy Section",
      value: "copy_section",
      onClick: (singleSection: Partial<ESSection>) => {
        setConfirmCopyDialogOpen(true);
        setCopySectionData({
          section_id: singleSection?.section_id,
          estimate_id: +`${estimateDetail?.estimate_id}`,
        });
      },
    },
    {
      label: "Delete this Section",
      value: "delete_section",
      onClick: (singleSection: Partial<ESSection>) => {
        if (sections?.length > 1) {
          setConfirmDialogOpen(true);
          setSelectDeletedItems(singleSection);
        } else {
          notification.error({
            description: _t("At least one section is needed"),
          });
        }
      },
    },
  ], [
    estimate_module_name,
    sections?.length,
    estimateDetail?.estimate_id,
    _t,
    setCostItemDatabaseOpen,
    setItemsOpen,
    setItemsData,
    setIsItemAdd,
    setImportEstimateFrom1Build,
    setEstimateBulkMarkup,
    setConfirmCopyDialogOpen,
    setCopySectionData,
    setConfirmDialogOpen,
    setSelectDeletedItems,
  ]);

  return menuItems;
};
