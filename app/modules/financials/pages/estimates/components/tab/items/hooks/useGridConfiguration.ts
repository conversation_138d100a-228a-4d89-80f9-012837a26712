import { useEffect, useRef, useState } from "react";
import { getUnitList } from "~/redux/action/unitActions";

/**
 * Custom hook for managing AgGrid configuration and units
 * Extracted from DragComponent to improve code organization and reusability
 */
export const useGridConfiguration = () => {
  const gridRef = useRef<ExtendedAgGridReact<ESEstimateItem> | null>(null);
  const [units, setUnits] = useState<
    IUnitListResponseDataAndStatusCode["units"]
  >([]);
  const [isUnitsGetting, setIsUnitsGetting] = useState<boolean>(true);

  useEffect(() => {
    if (window.ENV.ENABLE_UNIT_DROPDOWN) {
      (async function () {
        try {
          const response = await getUnitList();
          if (!response.success) {
            setIsUnitsGetting(false);
            return;
          }
          const units = response.data?.units || [];
          setUnits(units);
          const api = gridRef.current?.api;
          if (!api) return;

          const existingColDefs = api.getColumnDefs();
          if (!existingColDefs) return;

          const updatedColDefs = existingColDefs.map((col) =>
            "field" in col && col.field === "unit"
              ? {
                  ...col,
                  filterParams: {
                    values:
                      units.map((unit) => ({
                        label: unit.name?.toString(),
                        value: unit.name?.toString(),
                      })) ?? [],
                  },
                }
              : col
          );

          api.setColumnDefs(updatedColDefs);
          api.refreshHeader();
        } catch (error) {
          // Handle error silently
        }
        setIsUnitsGetting(false);
      })();
      
      return () => {
        if (gridRef?.current?.api?.destroy) {
          gridRef?.current?.api?.destroy();
        }
      };
    }
  }, []);

  return {
    gridRef,
    units,
    isUnitsGetting,
  };
};
