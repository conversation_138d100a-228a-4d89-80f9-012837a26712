import React from "react";
import { Typography } from "~/shared/components/atoms/typography";
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";
import { DropdownMenu } from "~/shared/components/molecules/dropdownMenu";
import { formatAmount, sanitizeString } from "~/helpers/helper";
import { useTranslation } from "~/hook";
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";

interface SectionHeaderProps {
  singleSection: ESSection;
  sectionMetrics: {
    estimatedCost: number;
    subtotalTaxable: number;
    subtotal: number;
    markup: number;
    profitMargin: string;
    tax: number;
    grandTotal: number;
  };
  isReadOnly: boolean;
  dragAndDropRef: (node: HTMLElement) => void;
  actionMenuItems: any[];
  isMultipleSelected: boolean;
}

/**
 * SectionHeader component - handles the display of section title, metrics, and actions
 * Extracted from DragComponent to improve code organization
 */
export const SectionHeader: React.FC<SectionHeaderProps> = ({
  singleSection,
  sectionMetrics,
  isReadOnly,
  dragAndDropRef,
  actionMenuItems,
  isMultipleSelected,
}) => {
  const { _t } = useTranslation();
  const { formatter } = useCurrencyFormatter();

  const sectionTitle = _t(
    `${HTMLEntities.decode(
      sanitizeString(singleSection?.section_name ?? "")
    )}`
  );

  const sectionTotal = formatter(
    (sectionMetrics.grandTotal / 100)?.toFixed(2)
  )?.value_with_symbol;

  return (
    <>
      {/* Left side content - drag handle */}
      {!isReadOnly && (
        <div ref={dragAndDropRef as any}>
          <ButtonWithTooltip
            icon="fa-solid fa-grip-dots"
            tooltipTitle={_t("Move")}
            tooltipPlacement="top"
            iconClassName="w-3.5 h-3.5"
            className="hover:!bg-primary-8 active:!bg-primary-8 cursor-move absolute top-3.5 left-[5px]"
            onClick={() => {}}
          />
        </div>
      )}

      {/* Center content - section metrics */}
      <div className="flex items-center gap-1.5 bg-primary-8 text-primary-900 py-[3px] px-[9px] rounded-sm dark:bg-dark-800 dark:text-white/90 w-fit whitespace-nowrap hide-collapse">
        <Typography className="!mb-0 !text-sm !text-primary-900 leading-4 !font-semibold dark:!text-white/90">
          {_t("Total")}:
        </Typography>
        <Typography className="!mb-0 !text-sm !text-primary-900 leading-4 !font-semibold dark:!text-white/90 whitespace-nowrap">
          {sectionTotal}
        </Typography>
      </div>

      {/* Right side content - action menu */}
      {!isReadOnly && (
        <DropdownMenu
          trigger={["click"]}
          placement="bottomRight"
          menu={{
            items: actionMenuItems,
            onClick: ({ key }) => {
              const menuItem = actionMenuItems.find(item => item.value === key);
              if (menuItem?.onClick) {
                menuItem.onClick(singleSection);
              }
            },
          }}
        >
          <ButtonWithTooltip
            icon="fa-regular fa-ellipsis-vertical"
            tooltipTitle={_t("More")}
            tooltipPlacement="top"
            iconClassName="w-3.5 h-3.5"
            className="hover:!bg-primary-8 active:!bg-primary-8"
          />
        </DropdownMenu>
      )}

      {/* Multiple selection indicator */}
      {isMultipleSelected && (
        <div className="flex items-center gap-1.5 bg-blue-100 text-blue-900 py-[3px] px-[9px] rounded-sm dark:bg-blue-800 dark:text-blue-100 w-fit whitespace-nowrap">
          <Typography className="!mb-0 !text-sm leading-4 !font-semibold">
            {_t("Multiple items selected")}
          </Typography>
        </div>
      )}
    </>
  );
};
