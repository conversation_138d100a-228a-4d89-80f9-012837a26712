import { useEffect, useRef, useCallback } from "react";

/**
 * Custom hook for managing memory and preventing memory leaks
 * Addresses known memory issues with AgGrid tables when handling many sections
 */
export const useMemoryManagement = () => {
  const cleanupFunctions = useRef<(() => void)[]>([]);
  const timers = useRef<NodeJS.Timeout[]>([]);
  const observers = useRef<IntersectionObserver[]>([]);

  // Register cleanup function
  const registerCleanup = useCallback((cleanupFn: () => void) => {
    cleanupFunctions.current.push(cleanupFn);
  }, []);

  // Register timer for cleanup
  const registerTimer = useCallback((timer: NodeJS.Timeout) => {
    timers.current.push(timer);
  }, []);

  // Register observer for cleanup
  const registerObserver = useCallback((observer: IntersectionObserver) => {
    observers.current.push(observer);
  }, []);

  // Debounced cleanup for performance
  const debouncedCleanup = useCallback((fn: () => void, delay: number = 100) => {
    const timer = setTimeout(fn, delay);
    registerTimer(timer);
    return timer;
  }, [registerTimer]);

  // Memory-efficient data processing
  const processDataInChunks = useCallback(
    <T, R>(
      data: T[],
      processor: (item: T) => R,
      chunkSize: number = 50
    ): R[] => {
      const results: R[] = [];
      for (let i = 0; i < data.length; i += chunkSize) {
        const chunk = data.slice(i, i + chunkSize);
        results.push(...chunk.map(processor));
        
        // Allow other tasks to run between chunks
        if (i + chunkSize < data.length) {
          return new Promise<R[]>((resolve) => {
            const timer = setTimeout(() => {
              resolve(results);
            }, 0);
            registerTimer(timer);
          }) as any;
        }
      }
      return results;
    },
    [registerTimer]
  );

  // Cleanup function to prevent memory leaks
  const cleanup = useCallback(() => {
    // Clear all timers
    timers.current.forEach((timer) => {
      clearTimeout(timer);
    });
    timers.current = [];

    // Disconnect all observers
    observers.current.forEach((observer) => {
      observer.disconnect();
    });
    observers.current = [];

    // Run all registered cleanup functions
    cleanupFunctions.current.forEach((cleanupFn) => {
      try {
        cleanupFn();
      } catch (error) {
        console.warn("Error during cleanup:", error);
      }
    });
    cleanupFunctions.current = [];
  }, []);

  // AgGrid specific memory management
  const manageGridMemory = useCallback((gridApi: any) => {
    if (!gridApi) return;

    // Register grid cleanup
    registerCleanup(() => {
      try {
        if (gridApi.destroy) {
          gridApi.destroy();
        }
      } catch (error) {
        console.warn("Error destroying grid:", error);
      }
    });

    // Optimize grid performance settings
    const optimizeGrid = () => {
      try {
        // Reduce memory usage by limiting rendered rows
        if (gridApi.setRowBuffer) {
          gridApi.setRowBuffer(10);
        }
        
        // Enable row virtualization
        if (gridApi.setDomLayout) {
          gridApi.setDomLayout('normal');
        }
        
        // Clear selection to free memory
        if (gridApi.deselectAll) {
          gridApi.deselectAll();
        }
      } catch (error) {
        console.warn("Error optimizing grid:", error);
      }
    };

    debouncedCleanup(optimizeGrid, 500);
  }, [registerCleanup, debouncedCleanup]);

  // Memory monitoring (development only)
  const monitorMemory = useCallback(() => {
    if (process.env.NODE_ENV === 'development' && 'memory' in performance) {
      const memInfo = (performance as any).memory;
      if (memInfo.usedJSHeapSize > memInfo.jsHeapSizeLimit * 0.9) {
        console.warn('High memory usage detected:', {
          used: Math.round(memInfo.usedJSHeapSize / 1024 / 1024),
          total: Math.round(memInfo.totalJSHeapSize / 1024 / 1024),
          limit: Math.round(memInfo.jsHeapSizeLimit / 1024 / 1024),
        });
      }
    }
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return cleanup;
  }, [cleanup]);

  // Periodic memory monitoring
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      const interval = setInterval(monitorMemory, 10000); // Check every 10 seconds
      registerTimer(interval);
    }
  }, [monitorMemory, registerTimer]);

  return {
    registerCleanup,
    registerTimer,
    registerObserver,
    debouncedCleanup,
    processDataInChunks,
    manageGridMemory,
    cleanup,
  };
};
