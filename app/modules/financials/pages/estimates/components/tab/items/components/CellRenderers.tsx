import React from "react";
import { CheckBox } from "~/shared/components/atoms/checkBox";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Typography } from "~/shared/components/atoms/typography";
import { formatAmount, sanitizeString } from "~/helpers/helper";
import { useTranslation } from "~/hook";
import { ICON_MAP } from "~/modules/financials/pages/changeOrder/components/sidebar/utils";
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";
import { generateCostCodeLabel } from "~/shared/utils/helper/common";
import { isValidId } from "../../../utils/common";

/**
 * CellRenderers - Contains all cell renderer components for the AgGrid
 * Extracted from DragComponent to improve code organization
 */

interface MoveCellRendererProps {
  selectedItems: Record<string, any>;
}

export const MoveCellRenderer: React.FC<MoveCellRendererProps> = ({ selectedItems }) => {
  return (
    <div className="w-6 h-6 flex items-center justify-center absolute top-1 left-1 !z-0 opacity-100">
      <FontAwesomeIcon
        className="w-4 h-4 text-[#4b5a76]"
        icon="fa-solid fa-grip-dots"
      />
    </div>
  );
};

interface CheckboxCellRendererProps {
  data: ESEstimateItem;
  selectedItems: Record<string, any>;
  onSelectionChange: (selected: boolean, data: { data: ESEstimateItem }) => void;
  params: any;
}

export const CheckboxCellRenderer: React.FC<CheckboxCellRendererProps> = ({
  data,
  selectedItems,
  onSelectionChange,
  params,
}) => {
  if (!data) return null;

  const is_checked: boolean = Boolean(
    data?.selected || selectedItems?.[data?.item_id]
  );

  return (
    <CheckBox
      checked={is_checked}
      onChange={(e) => {
        if (params && params.node) {
          const selected = Boolean(e.target.checked);
          const updatedData = {
            ...data,
            selected,
          };
          onSelectionChange(e.target.checked, {
            data: updatedData,
          });
          params.node.setData(updatedData);
          params.api.refreshCells({
            rowNodes: [params.node],
            force: true,
          });
        }
      }}
    />
  );
};

interface TypeCellRendererProps {
  data: ESEstimateItem;
}

export const TypeCellRenderer: React.FC<TypeCellRendererProps> = ({ data }) => {
  const value = data?.item_type_name;
  return value ? (
    <Tooltip title={value}>
      <FontAwesomeIcon
        className="w-4 h-4 text-primary-900 mx-auto"
        icon={
          ICON_MAP[value as keyof typeof ICON_MAP] || ICON_MAP["default"]
        }
      />
    </Tooltip>
  ) : (
    <>-</>
  );
};

interface ItemNameCellRendererProps {
  data: ESEstimateItem;
}

export const ItemNameCellRenderer: React.FC<ItemNameCellRendererProps> = ({ data }) => {
  const itemName =
    HTMLEntities.decode(sanitizeString(data?.subject)) ?? "-";
  return (
    <Tooltip title={itemName}>
      <Typography className="table-tooltip-text">
        {itemName}
        <span className="text-gray-500 ml-1">
          {Boolean(data?.is_optional_item) ? "(Optional)" : ""}
        </span>
      </Typography>
    </Tooltip>
  );
};

interface CostCodeCellRendererProps {
  data: ESEstimateItem;
  filteredCodeCostData: any[];
}

export const CostCodeCellRenderer: React.FC<CostCodeCellRendererProps> = ({
  data,
  filteredCodeCostData,
}) => {
  const costCode = generateCostCodeLabel({
    name: data?.cost_code_name || "",
    code: data?.cost_code,
    isArchived:
      data?.code_is_deleted == 1 ||
      filteredCodeCostData?.findIndex(
        (el) => el?.code_id?.toString() === data?.cost_code_id?.toString()
      ) == -1,
    isAllowCodeWithoutName: true,
  });
  return costCode ? (
    <Tooltip title={costCode}>
      <Typography className="table-tooltip-text">
        {costCode || "-"}
      </Typography>
    </Tooltip>
  ) : (
    "-"
  );
};

interface QuantityCellRendererProps {
  data: ESEstimateItem;
}

export const QuantityCellRenderer: React.FC<QuantityCellRendererProps> = ({ data }) => {
  const { formatter } = useCurrencyFormatter();
  const quantity = data?.quantity ?? "";
  const quantityUnit = formatter(
    formatAmount(Number(data.quantity || 0), { isQuantity: true })
  ).value;

  return (
    <Tooltip title={!!quantity ? quantityUnit : "0"}>
      <Typography className="table-tooltip-text">
        {quantity ? quantityUnit : "0"}
      </Typography>
    </Tooltip>
  );
};

interface UnitCostCellRendererProps {
  data: ESEstimateItem;
  isReadOnly: boolean;
  onCostChangeConfirmation: (data: ESEstimateItem) => void;
}

export const UnitCostCellRenderer: React.FC<UnitCostCellRendererProps> = ({
  data,
  isReadOnly,
  onCostChangeConfirmation,
}) => {
  const { _t } = useTranslation();
  const { formatter } = useCurrencyFormatter();
  
  const totalFloat = Number(data?.unit_cost);
  const unitCost = data?.unit_cost
    ? formatter(formatAmount(totalFloat / 100))?.value_with_symbol
    : formatter("0")?.value_with_symbol;

  const oneBuildId = data?.one_build_id;
  const unitCostRounded = Math.round(Number(data?.unit_cost));
  const updatedUnitCostRounded = Math.round(
    Number(data?.updated_unit_cost)
  );
  const shouldShowUpdateIcon =
    data?.updated_unit_cost &&
    unitCostRounded !== updatedUnitCostRounded &&
    (!oneBuildId || oneBuildId === undefined) &&
    isValidId(data?.reference_item_id);

  return (
    <div className="flex gap-1 items-center justify-end overflow-hidden">
      {shouldShowUpdateIcon && !isReadOnly && (
        <Tooltip
          title={_t(
            "The Cost is different than the Cost defined within the Cost Items Database. Click to update here."
          )}
          placement="top"
        >
          <FontAwesomeIcon
            className="ml-1 w-3.5 h-3.5 text-deep-orange-500 cursor-pointer"
            icon="fa-regular fa-triangle-exclamation"
            onClick={() => {
              onCostChangeConfirmation(data);
            }}
          />
        </Tooltip>
      )}
      <Tooltip title={unitCost}>
        <Typography className="table-tooltip-text">{unitCost}</Typography>
      </Tooltip>
    </div>
  );
};
