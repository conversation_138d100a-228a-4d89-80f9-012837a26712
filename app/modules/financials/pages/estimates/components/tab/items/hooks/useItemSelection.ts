import { useCallback, useMemo } from "react";
import { useAppESDispatch, useAppESSelector } from "../../../../redux/store";
import { selectItem, selectAll, unselectAll } from "../../../../redux/slices/ESItemSlice";

/**
 * Custom hook for handling item selection logic
 * Extracted from DragComponent to improve code organization and reusability
 */
export const useItemSelection = () => {
  const dispatch = useAppESDispatch();
  const { selectedItems } = useAppESSelector((state) => state.estimateItems);

  const handleSelection = useCallback(
    (selected: boolean, params: { data: ESEstimateItem }) => {
      dispatch(selectItem({ selected, data: params?.data }));
    },
    [dispatch]
  );

  const handleSelectAll = useCallback(
    (sectionId: number) => {
      dispatch(selectAll({ sectionId }));
    },
    [dispatch]
  );

  const handleUnselectAll = useCallback(
    (sectionId: number) => {
      dispatch(unselectAll({ sectionId }));
    },
    [dispatch]
  );

  const isMultipleSelected = useMemo(() => {
    return Object?.keys(selectedItems ?? {})?.length >= 1;
  }, [selectedItems]);

  const isAllSelected = useCallback(
    (sectionItems: ESEstimateItem[]) => {
      return (
        sectionItems?.every(
          (item) => selectedItems?.[item?.item_id]?.selected
        ) && sectionItems?.length > 0
      );
    },
    [selectedItems]
  );

  return {
    selectedItems,
    handleSelection,
    handleSelectAll,
    handleUnselectAll,
    isMultipleSelected,
    isAllSelected,
  };
};
