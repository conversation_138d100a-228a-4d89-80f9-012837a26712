import { useMemo, useCallback } from "react";
import { useAppESSelector } from "../../../../redux/store";

interface UsePerformanceOptimizationProps {
  singleSection: ESSection;
  flags: any;
  isReadOnly: boolean;
  selectedItems: Record<string, any>;
  isLoadingCheckBox: Record<number, boolean>;
  sections: ESSection[];
  sectionMetrics: any;
  columnDefs: any[];
  isInView: boolean;
}

/**
 * Custom hook for performance optimizations and memoization patterns
 * Helps prevent unnecessary re-renders and optimize component performance
 */
export const usePerformanceOptimization = ({
  singleSection,
  flags,
  isReadOnly,
  selectedItems,
  isLoadingCheckBox,
  sections,
  sectionMetrics,
  columnDefs,
  isInView,
}: UsePerformanceOptimizationProps) => {
  const { codeCostData } = useAppESSelector((state) => state.costCode);

  // Memoize filtered items to prevent recalculation on every render
  const filteredSectionItems = useMemo(() => {
    return singleSection?.items?.filter((el) => {
      const totalCost = Number(el?.quantity) * Number(el?.unit_cost);
      return flags?.showZeroQuantityItemsOnly ? totalCost <= 0 : true;
    });
  }, [singleSection?.items, flags?.showZeroQuantityItemsOnly]);

  // Memoize filtered cost code data
  const filteredCodeCostData = useMemo(() => {
    return codeCostData
      ?.filter(
        (item) =>
          (item?.cost_code_name?.toString() !== "" ||
            item?.csi_code?.toString() !== "") &&
          Number(item?.parent_id) > 0
      )
      ?.map((el) => ({
        ...el,
        label: `${el?.cost_code_name}${
          el?.csi_code && el?.csi_code !== null ? ` (${el?.csi_code})` : ""
        }${el?.is_deleted === 1 ? ` (Archived)` : ""}`,
      }));
  }, [codeCostData]);

  // Memoize dropdown height calculation
  const dropdownHeight = useMemo(() => {
    const length = filteredSectionItems?.length || 0;
    return length === 1
      ? 60
      : length === 2
      ? 90
      : length === 3
      ? 120
      : length === 4
      ? 150
      : 180;
  }, [filteredSectionItems?.length]);

  // Memoize selection state
  const selectionState = useMemo(() => {
    const selectedCount = Object.keys(selectedItems ?? {}).length;
    const isMultipleSelected = selectedCount >= 1;
    const allSelected =
      singleSection?.items?.every(
        (item) => selectedItems?.[item?.item_id]?.selected
      ) && (singleSection?.items?.length || 0) > 0;

    return {
      selectedCount,
      isMultipleSelected,
      allSelected,
    };
  }, [selectedItems, singleSection?.items]);

  // Memoize section key for React reconciliation
  const sectionKey = useMemo(() => {
    return `${singleSection?.section_id}-${singleSection?.code_id}`;
  }, [singleSection?.section_id, singleSection?.code_id]);

  // Memoize loading states
  const loadingStates = useMemo(() => {
    const hasLoadingItems = Object.values(isLoadingCheckBox).some(Boolean);
    return {
      hasLoadingItems,
      loadingItemIds: Object.keys(isLoadingCheckBox).filter(
        (id) => isLoadingCheckBox[Number(id)]
      ),
    };
  }, [isLoadingCheckBox]);

  // Memoize grid configuration
  const gridConfig = useMemo(() => {
    return {
      suppressContextMenu: true,
      suppressDragLeaveHidesColumns: true,
      suppressMoveWhenRowDragging: true,
      suppressRowClickSelection: true,
      rowDragManaged: !isReadOnly,
      animateRows: true,
      className: "static-table",
    };
  }, [isReadOnly]);

  // Memoize style configurations
  const styleConfig = useMemo(() => {
    return {
      sectionStyle: {
        opacity: 1,
        transform: "scale(1)",
        transition: "transform 0.2s ease, opacity 0.2s ease",
      },
      dragStyle: {
        opacity: 0.5,
        transform: "scale(1.05)",
        transition: "transform 0.2s ease, opacity 0.2s ease",
      },
    };
  }, []);

  // Stable callback for preventing unnecessary re-renders
  const stableCallbacks = useMemo(() => {
    return {
      // Add stable callback references here if needed
    };
  }, []);

  // Memoize component dependencies for CollapseSingleTableMemo
  const memoizationDeps = useMemo(() => {
    return [
      singleSection?.section_id,
      singleSection?.items?.length,
      flags?.showZeroQuantityItemsOnly,
      flags?.isMarkupHidden,
      isReadOnly,
      selectionState.isMultipleSelected,
      loadingStates.hasLoadingItems,
      sections?.length,
      sectionMetrics.grandTotal,
      columnDefs?.length,
      isInView,
    ];
  }, [
    singleSection?.section_id,
    singleSection?.items?.length,
    flags?.showZeroQuantityItemsOnly,
    flags?.isMarkupHidden,
    isReadOnly,
    selectionState.isMultipleSelected,
    loadingStates.hasLoadingItems,
    sections?.length,
    sectionMetrics.grandTotal,
    columnDefs?.length,
    isInView,
  ]);

  return {
    filteredSectionItems,
    filteredCodeCostData,
    dropdownHeight,
    selectionState,
    sectionKey,
    loadingStates,
    gridConfig,
    styleConfig,
    stableCallbacks,
    memoizationDeps,
  };
};
