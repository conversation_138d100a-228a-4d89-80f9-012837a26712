import { useEffect, useMemo, useRef, useState } from "react";
import isEmpty from "lodash/isEmpty";
// Atoms
import { Typography } from "~/shared/components/atoms/typography";
// Molecules
import { GoogleMap } from "~/shared/components/molecules/googleMap";
import { FieldStatus } from "~/shared/components/molecules/fieldStatus";
import { SelectField } from "~/shared/components/molecules/selectField";
import { InlineField } from "~/shared/components/molecules/inlineField";
import { CrudCommonCard } from "~/shared/components/molecules/crudCommonCard";
// Other
import { useGModules } from "~/zustand";

import { getStatusForField } from "~/shared/utils/helper/common";
import { getAddressComponent } from "~/shared/utils/helper/locationAddress";
import delay from "lodash/delay";
import { useTranslation } from "~/hook";
import { useAppDispatch } from "~/redux/store";
import {
  address<PERSON>ey<PERSON>ist,
  ESDetailsField,
  estAdd<PERSON><PERSON><PERSON><PERSON><PERSON>,
  ESTIMATES_LIST_OPTIONS,
  isNotString,
} from "../../../utils/constants";
import { useAppESSelector } from "../../../redux/store";
import { fieldStatus, HtmlDecode } from "../../../utils/common";
import { updateEstimateDetail } from "../../../redux/slices/ESDetailSlice";
import { updateEstimateDetailApi } from "../../../redux/action/ESDetailAction";
import useFieldStatus from "../../../utils/useFieldStatus";
import { prevAddress } from "../../../utils/function";

const EstimatesAddressInformation = ({ isReadOnly }: IEReadOnlyComponent) => {
  const { _t } = useTranslation();

  const { estimateDetail, isEstimateDetailLoading }: IEstimatesDetailState =
    useAppESSelector((state) => state.estimateDetail);
  const { checkModuleAccessByKey } = useGModules();
  const dispatch = useAppDispatch();
  const { loadingStatus, handleChangeFieldStatus, setLoadingStatus } =
    useFieldStatus(fieldStatus);
  const [estimateValues, setEstimateValues] =
    useState<IEstimateDetailData>(ESDetailsField);

  // STATES

  const [seletedAddressKey, setSeletedAddressKey] =
    useState<String>("directory");
  // const [loadingStatus, setLoadingStatus] =
  //   useState<Array<IEFieldStatus>>(fieldStatus);
  const [locationLatLong, setLocationLatLong] =
    useState<IEAddrLatLong>(estAddrLatLong);

  const [viewUnit, setViewUnit] = useState<boolean>(false);
  const addressInfoRef = useRef<HTMLDivElement | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [isAddresInput, setIsAddresInput] = useState<boolean>(false);

  const toggleAddressInfo = () => {
    if (!loading) setViewUnit((current) => !current);
  };

  useEffect(() => {
    if (estimateDetail) setEstimateValues(estimateDetail);
  }, [estimateDetail]);

  useEffect(() => {
    if (estimateDetail?.address_from === seletedAddressKey) return;
    setSeletedAddressKey(estimateDetail?.address_from ?? "");
  }, [estimateDetail]);

  useEffect(() => {
    if (addressInfoRef?.current) {
      const mouseEventHandler = (e: MouseEvent) => {
        if (!addressInfoRef?.current?.contains(e.target as Node)) {
          setViewUnit(false);
        }
      };
      window.addEventListener("mousedown", mouseEventHandler);
      return () => {
        window.removeEventListener("mousedown", mouseEventHandler);
      };
    }
  }, [addressInfoRef]);

  const initialValues: IDirAddrInfo = useMemo(
    () => ({
      est_street1: HtmlDecode(estimateDetail?.est_street1 || ""),
      est_street2: HtmlDecode(estimateDetail?.est_street2 || ""),
      est_city: HtmlDecode(estimateDetail?.est_city || ""),
      est_state: HtmlDecode(estimateDetail?.est_state || ""),
      est_zip: HtmlDecode(estimateDetail?.est_zip || ""),
      latitude: Number(estimateDetail?.latitude) || 0,
      longitude: Number(estimateDetail?.longitude) || 0,
      user_id: estimateDetail?.user_id || "",
    }),
    [estimateDetail]
  );
  // const [defaultAddressValues, setDefaultAddressValues] =
  //   useState<IEstimateAddrInfo>(initialValues);
  const [inputValues, setInputValues] =
    useState<IEstimateAddrInfo>(initialValues);

  const hasEstStreet1 = Boolean(inputValues?.est_street1);
  const hasEstStreet2 = Boolean(inputValues?.est_street2);
  const hasEstCity = Boolean(inputValues?.est_city);
  const hasEstState = Boolean(inputValues?.est_state);
  const hasEstZip = Boolean(inputValues?.est_zip);

  const getFindAddress = async (valueKey: String = "") => {
    const isEstValid =
      isNotString(estimateValues?.est_street1) ||
      isNotString(estimateValues?.est_street2) ||
      isNotString(estimateValues?.est_zip) ||
      isNotString(estimateValues?.est_city) ||
      isNotString(estimateValues?.est_state);
    const estAddressData = {
      // address_from: seletedAddressKey,
      est_street1: estimateValues.est_street1,
      est_street2: estimateValues.est_street2,
      est_city: estimateValues.est_city,
      est_state: estimateValues.est_state,
      est_zip: estimateValues.est_zip,
      latitude: Number(estimateValues?.latitude) || 0,
      longitude: Number(estimateValues?.longitude) || 0,
    };
    const selectedAddressData = (keys: (keyof IEstimateDetailData)[]) => {
      // address_from: seletedAddressKey,
      if (valueKey === "directory") {
        return {
          // est_street1: !isEmpty(estimateValues?.[keys?.[0]])
          //   ? estimateValues?.[keys?.[0]] || ""
          //   : estimateValues?.customer_address1 || "",
          // est_street2: !isEmpty(estimateValues?.[keys?.[1]])
          //   ? estimateValues?.[keys?.[1]] || ""
          //   : estimateValues?.customer_address2 || "",
          est_street1: estimateValues?.[keys?.[0]] || "",
          est_street2: estimateValues?.[keys?.[1]] || "",
          est_city: estimateValues?.[keys?.[2]] || "",
          est_state: estimateValues?.[keys?.[3]] || "",
          est_zip: estimateValues?.[keys?.[4]] || "",
          latitude: Number(estimateValues?.latitude) || 0,
          longitude: Number(estimateValues?.longitude) || 0,
        };
      } else {
        return {
          est_street1: estimateValues?.[keys?.[0]] || "",
          est_street2: estimateValues?.[keys?.[1]] || "",
          est_city: estimateValues?.[keys?.[2]] || "",
          est_state: estimateValues?.[keys?.[3]] || "",
          est_zip: estimateValues?.[keys?.[4]] || "",
          latitude: Number(estimateValues?.latitude) || 0,
          longitude: Number(estimateValues?.longitude) || 0,
          // user_id: estimateValues?.[keys[7]] || "",}
        };
      }
    };
    const emptyAddressData = {
      // address_from: seletedAddressKey,
      est_street1: "",
      est_street2: "",
      est_city: "",
      est_state: "",
      est_zip: "",
      latitude: 0,
      longitude: 0,
    };
    switch (valueKey) {
      case "project":
        const projectData =
          (isEstValid || isAddresInput) &&
          estimateValues?.address_from === "project"
            ? estAddressData
            : selectedAddressData(
                addressKeyList?.project as (keyof IEstimateDetailData)[]
              );
        // setDefaultAddressValues(projectData);
        setInputValues(projectData);
        return projectData;
      case "directory":
        const custData =
          (isEstValid || isAddresInput) &&
          estimateValues?.address_from === "directory"
            ? estAddressData
            : selectedAddressData(
                addressKeyList?.directory as (keyof IEstimateDetailData)[]
              );
        // setDefaultAddressValues(custData);
        setInputValues(custData);
        return custData;
      case "other":
        const otherData =
          valueKey === "project"
            ? selectedAddressData(
                addressKeyList?.project as (keyof IEstimateDetailData)[]
              )
            : valueKey === "directory"
            ? selectedAddressData(
                addressKeyList?.directory as (keyof IEstimateDetailData)[]
              )
            : (isEstValid || isAddresInput) &&
              estimateValues?.address_from === "other"
            ? estAddressData
            : emptyAddressData;
        // setDefaultAddressValues(otherData);
        setInputValues(otherData);
        return otherData;
      default:
        // setDefaultAddressValues(emptyAddressData);
        setInputValues(emptyAddressData);
        return emptyAddressData;
    }
  };

  useEffect(() => {
    if (
      estimateDetail?.est_street1 !== inputValues.est_street1 ||
      estimateDetail?.est_street2 !== inputValues.est_street2 ||
      estimateDetail?.est_zip !== inputValues.est_zip ||
      estimateDetail?.est_city !== inputValues.est_city ||
      estimateDetail?.est_state !== inputValues.est_state
    ) {
      setInputValues({
        est_street1: HtmlDecode(estimateDetail?.est_street1?.trim() || ""),
        est_street2: HtmlDecode(estimateDetail?.est_street2?.trim() || ""),
        est_city: HtmlDecode(estimateDetail?.est_city?.trim() || ""),
        est_state: HtmlDecode(estimateDetail?.est_state?.trim() || ""),
        est_zip: HtmlDecode(estimateDetail?.est_zip?.trim() || ""),
        latitude: Number(estimateDetail?.latitude) || 0,
        longitude: Number(estimateDetail?.longitude) || 0,
        user_id: estimateDetail?.user_id || "",
      });
    }
  }, [estimateDetail]);

  // const handleChangeFieldStatus = ({
  //   field,
  //   status,
  //   action,
  // }: IEFieldStatus) => {
  //   const checkStatus = loadingStatus.find(
  //     (item: IEFieldStatus) => item?.field === field
  //   );
  //   if (
  //     !(
  //       (checkStatus?.status === "loading" ||
  //         checkStatus?.status === "success" ||
  //         checkStatus?.status === "save") &&
  //       (action === "ME" || action === "ML")
  //     )
  //   ) {
  //     setLoadingStatus((prevState: IEFieldStatus[]) =>
  //       prevState.map((item) =>
  //         item.field === field ? { ...item, status: status } : item
  //       )
  //     );
  //   }
  // };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    const add1 = {
      ...inputValues,
      [name]: value,
    };
    setInputValues((prev) => ({ ...prev, [name]: value }));
    const isEstValid =
      isNotString(add1?.est_street1) ||
      isNotString(add1?.est_street2) ||
      isNotString(add1?.est_zip) ||
      isNotString(add1?.est_city) ||
      isNotString(add1?.est_state);
    // if (isEstValid) {
    setIsAddresInput(isEstValid);
    // } else {
    //   setIsAddresInput(false);
    // }
    // setDefaultAddressValues((prev) => ({ ...prev, [name]: value }));
  };
  const handleSelectedLocation = async (place: IdirAddrPlaceDetails) => {
    setLocationLatLong({
      ...locationLatLong,
      latitude: place?.geometry?.location?.lat(),
      longitude: place?.geometry?.location?.lng(),
    });
    const streetNumber = getAddressComponent(place, "street_number")
      ? `${getAddressComponent(place, "street_number")} `
      : "";
    const updateValues: IEstimateAddrInfo = {
      est_street1: `${streetNumber}${getAddressComponent(place, "route")}`,
      est_street2: "",
      est_city: getAddressComponent(place, "locality"),
      est_state: getAddressComponent(place, "administrative_area_level_1"),
      est_zip: getAddressComponent(place, "postal_code"),
      latitude: place?.geometry?.location?.lat(),
      longitude: place?.geometry?.location?.lng(),
    };
    setInputValues(updateValues);
    await updateAddressInfo(updateValues, "address_info");
  };

  const handleInputBlur = async () => {
    if (viewUnit) {
      return false;
    }
    const updateValues: IEstimateAddrInfo = {
      ...inputValues,
    };
    // delete updateValues.user_id;
    await updateAddressInfo(updateValues, "address_info");
  };
  // async function updateAddressInfo(
  //   updateInfo: IEstimateAddrInfo,
  //   fieldKey?: string
  // ) {
  //   if (!updateInfo) return;
  //   const selectedKeys =
  //     addressKeyList[seletedAddressKey as keyof IEstimateAddressKey] ?? [];
  //   setLoading(true);
  //   setViewUnit(false);
  //   handleChangeFieldStatus({
  //     field: fieldKey,
  //     status: "loading",
  //     action: "API",
  //   });
  //   const updateRes = (await updateEstimateDetailApi({
  //     estimate_id: id || "",
  //     address_from: updateInfo?.address_from || seletedAddressKey,
  //     est_street1: updateInfo?.est_street1,
  //     est_street2: updateInfo?.est_street2,
  //     est_city: updateInfo?.est_city,
  //     est_state: updateInfo?.est_state,
  //     est_zip: updateInfo?.est_zip,
  //     // customer_id: estimateValues?.customer_id,
  //   })) as IEDetailsApiRes;
  //   if (updateRes?.success) {
  //     handleChangeFieldStatus({
  //       field: fieldKey,
  //       status: "success",
  //       action: "API",
  //     });
  //     dispatch(
  //       updateEstimateDetail(
  //         selectedKeys && selectedKeys?.length
  //           ? {
  //               address_from: updateInfo?.address_from || seletedAddressKey,
  //               est_street1: updateInfo?.est_street1,
  //               est_street2: updateInfo?.est_street2,
  //               est_city: updateInfo?.est_city,
  //               est_state: updateInfo?.est_state,
  //               est_zip: updateInfo?.est_zip,
  //             }
  //           : {}
  //       )
  //     );
  //   } else {
  //     handleChangeFieldStatus({
  //       field: fieldKey,
  //       status: "error",
  //       action: "API",
  //     });
  //     const existingKeys =
  //       addressKeyList[
  //         estimateDetail?.address_from as keyof IEstimateAddressKey
  //       ] ?? [];
  //     const prevAdd = {
  //       est_street1:
  //         estimateValues[existingKeys?.[0] as keyof IEstimateDetailData] ?? "",
  //       est_street2:
  //         estimateValues[existingKeys?.[1] as keyof IEstimateDetailData] ?? "",
  //       est_city:
  //         estimateValues[existingKeys?.[2] as keyof IEstimateDetailData] ?? "",
  //       est_state:
  //         estimateValues[existingKeys?.[3] as keyof IEstimateDetailData] ?? "",
  //       est_zip:
  //         estimateValues[existingKeys?.[4] as keyof IEstimateDetailData] ?? "",
  //       latitude:
  //         estimateValues[existingKeys?.[5] as keyof IEstimateDetailData] || 0,
  //       longitude:
  //         estimateValues[existingKeys?.[6] as keyof IEstimateDetailData] || 0,
  //     };
  //     dispatch(
  //       updateEstimateDetail({
  //         address_from: estimateDetail?.address_from,
  //         ...prevAdd,
  //       })
  //     );
  //     setInputValues((prev: IDirAddrInfo) => ({
  //       ...prevAdd,
  //     }));
  //     notification.error({
  //       description: (updateRes as { message?: string })?.message,
  //     });
  //   }
  //   setLoading(false);
  //   delay(() => {
  //     handleChangeFieldStatus({
  //       field: fieldKey,
  //       status: "button",
  //       action: "API",
  //     });
  //   }, 3000);
  // }

  const handleUpdateError = (message?: string, fieldKey?: string) => {
    const existingKeys =
      addressKeyList[
        estimateDetail?.address_from as keyof IEstimateAddressKey
      ] ?? [];
    const prevAdd = prevAddress(estimateValues, existingKeys);

    handleChangeFieldStatus({
      field: fieldKey,
      status: "error",
      action: "API",
    });
    dispatch(
      updateEstimateDetail({
        address_from: estimateDetail?.address_from,
        ...prevAdd,
      })
    );

    setInputValues((prev: IDirAddrInfo) => ({
      ...prevAdd,
    }));

    notification.error({ description: message });
  };

  async function updateAddressInfo(
    updateInfo: IEstimateAddrInfo,
    fieldKey?: string
  ) {
    // if (!updateInfo) return;
    if (
      updateInfo &&
      updateInfo?.est_street1?.trim() === estimateDetail?.est_street1?.trim() &&
      updateInfo?.est_street2?.trim() === estimateDetail?.est_street2?.trim() &&
      updateInfo?.est_zip?.trim() === estimateDetail?.est_zip?.trim() &&
      updateInfo?.est_city?.trim() === estimateDetail?.est_city?.trim() &&
      updateInfo?.est_state?.trim() === estimateDetail?.est_state?.trim()
    ) {
      return;
    }
    const selectedKeys =
      addressKeyList[seletedAddressKey as keyof IEstimateAddressKey] ?? [];
    const addressFrom = updateInfo?.address_from || seletedAddressKey;

    setLoading(true);
    setViewUnit(false);

    handleChangeFieldStatus({
      field: fieldKey,
      status: "loading",
      action: "API",
    });

    try {
      const updateRes = (await updateEstimateDetailApi({
        estimate_id: estimateDetail?.estimate_id || 0,
        address_from: addressFrom,
        est_street1: HtmlDecode(updateInfo?.est_street1?.trim() || ""),
        est_street2: HtmlDecode(updateInfo?.est_street2?.trim() || ""),
        est_city: HtmlDecode(updateInfo?.est_city?.trim() || ""),
        est_state: HtmlDecode(updateInfo?.est_state?.trim() || ""),
        est_zip: HtmlDecode(updateInfo?.est_zip?.trim() || ""),
        customer_id: estimateValues?.customer_id,
      })) as IEDetailsApiRes;
      if (updateRes?.success) {
        handleChangeFieldStatus({
          field: fieldKey,
          status: "success",
          action: "API",
        });

        dispatch(
          updateEstimateDetail(
            selectedKeys.length
              ? {
                  address_from: addressFrom,
                  est_street1: HtmlDecode(
                    updateInfo?.est_street1?.trim() || ""
                  ),
                  est_street2: HtmlDecode(
                    updateInfo?.est_street2?.trim() || ""
                  ),
                  est_city: HtmlDecode(updateInfo?.est_city?.trim() || ""),
                  est_state: HtmlDecode(updateInfo?.est_state?.trim() || ""),
                  est_zip: HtmlDecode(updateInfo?.est_zip?.trim() || ""),
                }
              : {}
          )
        );
      } else {
        handleUpdateError(updateRes?.message, fieldKey);
      }
    } catch (error) {
      handleUpdateError((error as { message?: string })?.message, fieldKey);
    } finally {
      setLoading(false);
      delay(() => {
        handleChangeFieldStatus({
          field: fieldKey,
          status: "button",
          action: "API",
        });
      }, 3000);
    }
  }

  const addHeader = useMemo(() => {
    const addressKey =
      ESTIMATES_LIST_OPTIONS.find((item) => item.value === seletedAddressKey)
        ?.label || "";

    return addressKey;
  }, [seletedAddressKey]);

  return (
    <>
      <CrudCommonCard
        headerTitle={_t(`${addHeader} Address`)}
        iconProps={{
          icon: "fa-solid fa-address-card",
          containerClassName:
            "bg-[linear-gradient(180deg,#B08CAB1a_0%,#734B6D1a_100%)]",
          id: "address_information_icon",
          colors: ["#B08CAB", "#734B6D"],
        }}
        headerRightButton={
          <div className="flex items-center justify-center gap-2">
            {!isReadOnly && (
              <FieldStatus
                status={getStatusForField(loadingStatus, "address_Select")}
              />
            )}
            <SelectField
              label=""
              popupClassName="!w-[150px]"
              className={`bg-[#EBF1F9] 
              ${
                !isEmpty(estimateDetail?.est_street1) ||
                !isEmpty(estimateDetail?.est_street2)
                  ? "hidden"
                  : ""
              } disabled:opacity-50 header-dropdown-select pl-3 !h-7 min-w-[90px] rounded`}
              fieldClassName="before:hidden"
              placement="bottomRight"
              value={seletedAddressKey}
              disabled={
                getStatusForField(loadingStatus, "address_Select") ===
                  "loading" || isReadOnly
              }
              readOnly={isReadOnly}
              labelPlacement="left"
              options={ESTIMATES_LIST_OPTIONS}
              onChange={async (value) => {
                const val = value as string;
                await setSeletedAddressKey(val);
                if (estimateDetail?.address_from === value) return;
                await getFindAddress(val).then((selectedValue) => {
                  updateAddressInfo(
                    {
                      ...selectedValue,
                      address_from: val,
                    },
                    "address_Select"
                  );
                });
              }}
            />
          </div>
        }
        children={
          <ul className="pt-2">
            <li>
              <InlineField
                label={_t("Address")}
                labelPlacement="left"
                labelClass="sm:w-[100px] sm:max-w-[100px]"
                field={
                  <ul
                    className={`grid items-start w-full ${
                      viewUnit ? "" : "xl:grid-cols-2 gap-2"
                    }`}
                  >
                    <li
                      className={`w-full gap-1 flex justify-between p-1.5 pt-2 ${
                        isReadOnly
                          ? "cursor-default sm:bg-transparent bg-[#f4f5f6]"
                          : "hover:bg-[#f4f5f6] min-[768px]:bg-transparent bg-[#f4f5f6] cursor-pointer"
                      } ${viewUnit ? "hidden" : ""}`}
                      onClick={() => {
                        if (isReadOnly) {
                          return false;
                        }
                        toggleAddressInfo();
                      }}
                      onMouseEnter={() => {
                        handleChangeFieldStatus({
                          field: "address_info",
                          status: "edit",
                          action: "ME",
                        });
                      }}
                      onMouseLeave={() => {
                        handleChangeFieldStatus({
                          field: "address_info",
                          status: "button",
                          action: "ML",
                        });
                      }}
                    >
                      <ul className="w-full">
                        {isEmpty(inputValues?.est_street1) &&
                        isEmpty(inputValues?.est_street2) &&
                        isEmpty(inputValues?.est_city) &&
                        isEmpty(inputValues?.est_state) &&
                        isEmpty(inputValues?.est_zip) ? (
                          "-"
                        ) : (
                          <>
                            <li className="text-primary-900 text-sm">
                              {HtmlDecode(inputValues?.est_street1 ?? "")}
                              {(hasEstStreet1 && hasEstStreet2) ||
                              (hasEstStreet1 &&
                                (hasEstCity || hasEstState || hasEstZip))
                                ? ", "
                                : ""}
                            </li>
                            <li className="text-primary-900 text-sm">
                              {HtmlDecode(inputValues.est_street2 ?? "")}
                              {hasEstStreet2 &&
                              (hasEstCity || hasEstState || hasEstZip)
                                ? ", "
                                : ""}
                            </li>
                            <li>
                              <Typography className="text-primary-900 text-sm">
                                {HtmlDecode(inputValues?.est_city ?? "")}
                              </Typography>
                              <Typography className="text-primary-900 text-sm">
                                {hasEstCity && hasEstState
                                  ? `, ${HtmlDecode(inputValues?.est_state)}`
                                  : HtmlDecode(inputValues?.est_state || "")}
                              </Typography>
                              <Typography className="text-primary-900 text-sm">
                                {hasEstZip && (hasEstState || hasEstCity)
                                  ? `, ${HtmlDecode(inputValues?.est_zip)}`
                                  : HtmlDecode(inputValues?.est_zip)}
                              </Typography>
                            </li>
                          </>
                        )}
                      </ul>
                      {!isReadOnly && (
                        <FieldStatus
                          status={getStatusForField(
                            loadingStatus,
                            "address_info"
                          )}
                        />
                      )}
                    </li>
                    <li className="w-full grid 2xl:grid-cols-2 gap-2">
                      <GoogleMap
                        ref={addressInfoRef}
                        cssStyle={{ height: "150px" }}
                        addressInfo={inputValues}
                        mapAddress={{
                          address1:
                            HtmlDecode(estimateDetail?.est_street1) || "",
                          address2:
                            HtmlDecode(estimateDetail?.est_street2) || "",
                          city: HtmlDecode(estimateDetail?.est_city) || "",
                          state: HtmlDecode(estimateDetail?.est_state) || "",
                          zip: HtmlDecode(estimateDetail?.est_zip) || "",
                        }}
                        temperature_scale={
                          Number(estimateValues?.temperature_scale) ?? 0
                        }
                        handleInputChange={handleInputChange}
                        handleSelectedLocation={handleSelectedLocation}
                        isEditable={viewUnit}
                        handleInputBlur={handleInputBlur}
                        title={[
                          HtmlDecode(estimateDetail?.est_street1),
                          HtmlDecode(estimateDetail?.est_street2),
                          HtmlDecode(estimateDetail?.est_city),
                          HtmlDecode(estimateDetail?.est_state),
                          HtmlDecode(estimateDetail?.est_zip),
                        ]
                          .filter((value) => !!value)
                          .join(", ")}
                      />
                    </li>
                  </ul>
                }
              />
            </li>
          </ul>
        }
      />
    </>
  );
};

export default EstimatesAddressInformation;
