import { use<PERSON>allback, useEffect, useMemo, useRef, useState } from "react";
import delay from "lodash/delay";
import debounce from "lodash/debounce";
// Atoms
import { Spin } from "~/shared/components/atoms/spin";
import { Empty } from "~/shared/components/atoms/empty";
import { But<PERSON> } from "~/shared/components/atoms/button";
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// Molecules
import { InputField } from "~/shared/components/molecules/inputField";
import { SelectField } from "~/shared/components/molecules/selectField";
import { InlineField } from "~/shared/components/molecules/inlineField";
import { ConfirmModal } from "~/shared/components/molecules/confirmModal";
import { CrudCommonCard } from "~/shared/components/molecules/crudCommonCard";
import { DatePickerField } from "~/shared/components/molecules/datePickerField";
import { InputNumberField } from "~/shared/components/molecules/inputNumberField";
import { InvoiceFieldRedirectionIcon } from "~/shared/components/molecules/fieldRedirect/invoiceFieldRedirectionIcon";
// organisms
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";
// Hook
import { useTranslation } from "~/hook";
import {
  useAppPaymentDispatch,
  useAppPaymentSelector,
} from "../../../redux/store";
import { getGModuleByKey, getGProject, getGSettings } from "~/zustand";
import dayjs from "dayjs";
import { formatAmount, sanitizeString } from "~/helpers/helper";
import {
  fetchPaymentHistory,
  fetchPaymentHistoryApi,
  fetchPaymentInvoiceListApi,
  fetchPaymentStatusListApi,
  updatePaymentDetailApi,
} from "../../../redux/action";
import { getCustomData } from "~/redux/action/customDataAction";
import { defaultConfig } from "~/data";
import {
  filterOptionBySubstring,
  getStatusActionForField,
  getStatusForField,
  onKeyDownCurrency,
} from "~/shared/utils/helper/common";
import {
  paymentDetailsField,
  upadatePaymentFieldStatus,
} from "../../../utils/constants";
import { updatePaymentDetail } from "../../../redux/slices/paymentDetailsSlice";
import { backendDateFormat } from "~/shared/utils/helper/defaultDateFormat";
import { PostPayment } from "../../sidebar/postPayment";
import { getGlobalUser } from "~/zustand/global/user/slice";
import {
  createPaymentIntentApi,
  updatePaymentIntentApi,
} from "~/redux/action/stripeAction";
import { updatePaymentHistory } from "../../../redux/slices/paymentHistorySlice";
import { resetDash } from "~/modules/financials/pages/payment/redux/slices/dashboardSlice";

const DetailsCard = ({ isReadOnly }: { isReadOnly: boolean }) => {
  const { _t } = useTranslation();
  const {
    date_format,
    quickbook_desktop_sync,
    quickbook_sync,
    stripe_activated,
    allow_online_payment,
    allow_creditcard_payment,
  }: GSettings = getGSettings();
  const { project_id }: GProject = getGProject();
  const paymentModule: GModule | undefined = getGModuleByKey(
    defaultConfig.payment_module
  );
  const { inputFormatter, unformatted, formatter } = useCurrencyFormatter();
  const dispatch = useAppPaymentDispatch();
  const { details }: IPaymentDetailsInitialState = useAppPaymentSelector(
    (state) => state?.paymentDetail
  );

  const {
    invoice_data,
    isInvoiceListDataLoading,
  }: IPaymentInvoiceListInitialState = useAppPaymentSelector(
    (state) => state.invoiceList
  );

  useEffect(() => {
    if (!!details?.payment_id) {
      const fetchWithDebounce = debounce(() => {
        dispatch(fetchPaymentInvoiceListApi({ project_id }));
      }, 300);
      fetchWithDebounce();
      return () => fetchWithDebounce.cancel();
    }
  }, [details?.payment_id, dispatch]);

  const {
    payment_type: paymentType,
    isStatusListDataLoading: isApprovalStatusAndPaymentTypeLoading,
    isStatusListDataFetched,
  } = useAppPaymentSelector((state) => state.statusList);

  useEffect(() => {
    if (!isStatusListDataFetched) {
      dispatch(
        fetchPaymentStatusListApi({
          quickbook_sync: quickbook_sync?.toString() || "0",
          quickbook_desktop_sync: quickbook_desktop_sync?.toString() || "0",
        })
      );
    }
  }, [isStatusListDataFetched]);

  const invoiceData = useMemo(() => {
    const addInvoice = (invoice_data ?? [])?.map((invoice) => {
      const nValue: number = invoice?.total ? Number(invoice.total) / 100 : 0.0;
      const total = formatter(
        formatAmount(nValue.toFixed(2))
      ).value_with_symbol;

      return {
        label: `Inv. #${
          !!invoice.company_invoice_id
            ? `${HTMLEntities.decode(
                sanitizeString(invoice.company_invoice_id)
              )}`
            : ""
        } (${total})${
          !!invoice.project_name
            ? ` - ${HTMLEntities.decode(sanitizeString(invoice.project_name))}`
            : ""
        }${
          !!invoice.customer_name
            ? ` - ${HTMLEntities.decode(sanitizeString(invoice.customer_name))}`
            : ""
        }`,
        value: invoice?.invoice_id?.toString(),
        show_payment_link: invoice?.show_payment_link?.toString() ?? "0",
        project_allow_online_payment:
          invoice?.project_allow_online_payment?.toString() ?? "0",
      };
    });

    if (
      !invoice_data.find(
        (data) =>
          data?.invoice_id?.toString() == details?.invoice_id?.toString()
      )
    ) {
      const nValue: number = !!details?.invoice_total
        ? Number(details.invoice_total) / 100
        : 0.0;
      const total = formatter(
        formatAmount(nValue.toFixed(2))
      ).value_with_symbol;
      if (
        !!details?.invoice_id &&
        details?.invoice_id?.toString() != "0" &&
        !!details.company_invoice_id &&
        details.company_invoice_id != "0"
      ) {
        addInvoice.push({
          label: `Inv. #${
            !!details.company_invoice_id
              ? `${HTMLEntities.decode(
                  sanitizeString(details.company_invoice_id)
                )}`
              : ""
          } (${total})${
            !!details.project_name
              ? `-${HTMLEntities.decode(sanitizeString(details.project_name))}-`
              : "-"
          }${
            !!details.customer_name
              ? `${HTMLEntities.decode(sanitizeString(details.customer_name))}`
              : ""
          }`,
          value: details?.invoice_id?.toString() ?? "",
          show_payment_link: details?.show_payment_link?.toString() ?? "0",
          project_allow_online_payment:
            details?.project_allow_online_payment?.toString() ?? "0",
        });
      }
    }

    return addInvoice;
  }, [invoice_data, details]);

  const { customDataList, isCLDataFetched }: ICustomDataInitialState =
    useAppPaymentSelector((state) => state.customData);

  useEffect(() => {
    if (paymentModule?.module_id && !isCLDataFetched) {
      dispatch(
        getCustomData({
          types: [242],
          moduleId: paymentModule?.module_id,
        })
      );
    }
  }, [paymentModule?.module_id, isCLDataFetched]);

  const [inputValues, setInputValues] =
    useState<Partial<IPaymentDetailData>>(paymentDetailsField);

  const depositeList = useMemo(() => {
    // Create a Set to track unique values and prevent duplicates
    const uniqueItems = new Set();

    const accumulatedData = customDataList.reduce<ICategCusDataItems>(
      (acc, item) => {
        if (item.is_custom_item === "1") {
          // Create a unique identifier using both id and name
          // Remove Condition by Aditya, Akash and Utsav
          const value = item?.item_id;

          // Only add if we haven't seen this value before
          if (!uniqueItems.has(value) && !!value) {
            uniqueItems.add(value);

            acc.empRlsTypeList?.push({
              label: HTMLEntities.decode(sanitizeString(item.name)),
              value: value.toString(),
            });
          }
        }
        return acc;
      },
      { empRlsTypeList: [] }
    );

    if (
      !!details?.deposit_to &&
      !!details?.deposit_name &&
      details?.is_deleted_deposit?.toString() == "1"
    ) {
      accumulatedData.empRlsTypeList?.push({
        label: `${HTMLEntities.decode(
          sanitizeString(details?.deposit_name?.toString())
        )} (Archived)`,
        value: details?.deposit_to?.toString(),
      });
    }

    // 86cy0b8vt 17-02-2025
    // accumulatedData.empRlsTypeList?.sort((a, b) =>
    //   (a.label || "").localeCompare(b.label || "")
    // );

    return accumulatedData;
  }, [customDataList, details?.deposit_to, inputValues?.deposit_to]);

  const [loadingStatus, setLoadingStatus] = useState<Array<IFieldStatus>>(
    upadatePaymentFieldStatus
  );

  useEffect(() => {
    setInputValues(details);
  }, [JSON.stringify(details)]);

  const loadingStatusRef = useRef(upadatePaymentFieldStatus);
  const [isAmountValue, setIsAmountValue] = useState<boolean>(false);

  const handleChangeFieldStatus = ({ field, status, action }: IFieldStatus) => {
    const checkStatus = loadingStatus.find(
      (item: IFieldStatus) => item?.field === field
    );
    if (
      !(
        (checkStatus?.status === "loading" ||
          checkStatus?.status === "success" ||
          checkStatus?.status === "save") &&
        (action === "ME" || action === "ML")
      )
    ) {
      setLoadingStatus((prevState: IFieldStatus[]) => {
        const newState = prevState.map((item) =>
          item.field === field ? { ...item, status: status, action } : item
        );
        loadingStatusRef.current = newState;
        return newState;
      });
    }
  };

  const debouncedHandleChangeDate = useCallback(
    debounce((dateString) => {
      if (!!details?.invoice_id) {
        const newValue = dateString
          ? backendDateFormat(dateString.toString(), date_format)
          : "";
        setInputValues((prevValues) => ({
          ...prevValues,
          payment_date: dateString.toString(),
        }));

        handleUpdateField({
          payment_date: newValue,
        });
      } else {
        notification.error({
          description: "Invoice not found",
        });
      }
    }, 0),
    []
  );

  useEffect(() => {
    return () => {
      debouncedHandleChangeDate.cancel();
    };
  }, [debouncedHandleChangeDate]);

  const [isConfirmDialogOpen, setIsConfirmDialogOpen] =
    useState<boolean>(false);

  const [isInvoiceUpdateLoader, setIsInvoiceUpdateLoader] =
    useState<boolean>(false);

  const handleSelectChange = ({ value, name }: ISingleSelectOption) => {
    const newValue = typeof value === "string" ? value : value[0];
    setInputValues({
      ...inputValues,
      [name]: newValue,
    });

    if (name === "payment_type") {
      const getKey =
        paymentType?.find((data) => data.value === newValue)?.key ?? "";
      if (getKey === "payment_credit_card" && stripe_activated === "1") {
        if (
          !details?.amount ||
          (Number(details?.amount) < 0.5 && stripe_activated === "1")
        ) {
          setInputValues({
            ...inputValues,
            payment_type: details?.payment_type,
          });
          return notification.error({
            description: "Amount must be grater than 0.5",
          });
        } else {
          if (finalTotal < 0) {
            setIsButtonDisablePayNow(false);
            setInputValues({
              ...inputValues,
              payment_type: details?.payment_type,
            });
            notification.error({
              description: "Amount should not be greater than Due Amount",
            });
          } else {
            // Call Insert amd Update Intent API
            handleUpdateField({
              [name]: newValue,
            });
          }
        }
      } else {
        handleUpdateField({
          [name]: newValue,
        });
      }
    } else {
      const paymentTypeKey =
        paymentType?.find(
          ({ value }) => value === details?.payment_type?.toString()
        )?.key || "";

      // condition for invoice_id and open confrmation dialog
      const requiresConfirmation =
        name === "invoice_id" &&
        ((paymentTypeKey === "payment_credit_card" &&
          stripe_activated === "1" &&
          Boolean(details.is_from_stripe) == true) ||
          (paymentTypeKey === "payment_bank_transfer" &&
            details?.approval_status_key === "pay_verified"));

      requiresConfirmation
        ? setIsConfirmDialogOpen(true)
        : handleUpdateField({ [name]: newValue });
    }
  };

  const handleUpdateField = async (data: IPaymentDetailFields) => {
    try {
      const field = Object.keys(data)[0] as keyof IPaymentDetailData;
      const value = Object.values(data)?.[0] as string;
      if (field !== "invoice_id") {
        handleChangeFieldStatus({
          field: field,
          status: "loading",
          action: "API",
        });
      } else {
        setIsInvoiceUpdateLoader(true);
      }
      let newData = data;
      if (field === "amount") {
        const newVal = Number(value) * 100;
        newData = {
          ...data,
          [field]: newVal.toString(),
        };
      }

      const updateRes = (await updatePaymentDetailApi({
        payment_id: details?.payment_id || "",
        invoice_id: field !== "invoice_id" ? details?.invoice_id || "" : value,
        ...newData,
      })) as IPaymentDetailsUpdateApiRes;

      if (updateRes?.success) {
        handleChangeFieldStatus({
          field: field,
          status: "success",
          action: "API",
        });

        if (field === "amount") {
          setInputValues({
            ...inputValues,
            [field]: (Number(value) * 100).toFixed(0).toString(),
          });
          dispatch(
            updatePaymentHistory({
              id: details?.payment_id?.toString() || "",
              [field]: (Number(value) * 100).toFixed(0).toString(),
            })
          );
        }

        if (field === "payment_type") {
          const getPaymentTypes = paymentType.find(
            (data) => data.value == value
          );
          dispatch(
            updatePaymentHistory({
              id: details?.payment_id?.toString() || "",
              [field]: value,
              payment_type_key: getPaymentTypes?.key,
              payment_type_name: getPaymentTypes?.label,
            })
          );
        }

        if (field === "invoice_id") {
          newData = {
            customer_name_only: updateRes?.data?.customer_name_only || "",
            customer_company: updateRes?.data?.customer_company || "",
            customer_name: updateRes?.data?.customer_name || "-",
            ...newData,
          } as IPaymentDetailFields;
          dispatch(
            fetchPaymentHistory({
              id: value?.toString() || "",
              get_payment_link: 0,
            })
          );
        }

        if (field === "payment_date") {
          newData.payment_date = newData.payment_date
            ? dayjs(data[field], "YYYY-MM-DD").format(date_format)
            : "";

          dispatch(
            updatePaymentHistory({
              id: details?.payment_id?.toString() || "",
              ...data,
            })
          );
        }

        dispatch(updatePaymentDetail(newData));
        dispatch(resetDash());
        setIsConfirmDialogOpen(false);
      } else {
        handleChangeFieldStatus({
          field: field,
          status: "error",
          action: "API",
        });
        setInputValues({ ...inputValues, [field]: details[field] });
        notification.error({
          description: updateRes?.message || "Something went wrong!",
        });
      }
      delay(() => {
        const fieldAction = getStatusActionForField(
          loadingStatusRef.current,
          field
        );
        handleChangeFieldStatus({
          field: field,
          status: "FOCUS" === fieldAction ? "save" : "button",
          action: fieldAction || "API",
        });
      }, 3000);
    } catch (error) {
    } finally {
      setIsInvoiceUpdateLoader(false);
      setIsButtonDisablePayNow(false);
    }
  };

  const {
    invoice_total: invoice_payment_history_invoice_total,
    payments: invoice_payment_history_list,
  } = useAppPaymentSelector((state) => state?.paymentHistory);

  const finalTotal = useMemo(() => {
    const totalPayments = invoice_payment_history_list
      .filter(
        (payment) =>
          payment.approval_status_key !== "pay_failed" ||
          [details?.payment_id?.toString()].includes(
            payment.payment_id.toString()
          )
      )
      .reduce((sum, payment) => sum + Number(payment.amount) / 100, 0);
    return (
      Number(invoice_payment_history_invoice_total) / 100 -
      Number(totalPayments.toFixed(2))
    );
  }, [invoice_payment_history_list, invoice_payment_history_invoice_total]);

  const [isOpenPostPaymentSidebar, openPostPaymentSidebar] =
    useState<boolean>(false);

  const user: IInitialGlobalData["user"] = getGlobalUser();
  const { company_id = 0, user_id = 0 } = user || {};

  const [isButtonShow, setIsButtonShow] = useState<boolean>(false);
  const [isButtonDisablePayNow, setIsButtonDisablePayNow] =
    useState<boolean>(false);
  const [isLoadingForPayNow, setIsLoadingForPayNow] = useState<boolean>(false);

  const [clientSecretKey, setClientSecretKey] = useState<string>("");
  const [stripeAccount, setStripeAccount] = useState<string>("");
  const [paymentIntentId, setPaymentIntentId] = useState<string>("");

  const paymentIntent = async (invoiceAmount: string) => {
    try {
      setIsLoadingForPayNow(true);
      setIsButtonDisablePayNow(true);
      let isShowButton = true;
      const getAmount = Number(
        invoiceAmount.toString().replace(/[^\d.-]/g, "")
      );
      let setAmount: number = Number(getAmount);
      const invoicefind = invoiceData?.find(
        (data) => data?.value?.toString() == details?.invoice_id?.toString()
      );

      if (finalTotal < 0) {
        setIsButtonDisablePayNow(false);
        notification.error({
          description: "Amount should not be greater than Due Amount",
        });
      } else {
        if (invoicefind?.show_payment_link?.toString() == "0") {
          notification.error({
            description: "The Online Payment is not enabled for this Invoice",
          });
        } else {
          const requestCreatePayment: Partial<IStripeCreatePaymentIntentRequest> =
            {
              company_id,
              cf_invoice_id: Number(details.invoice_id),
              cf_user_id: user_id,
              amount: Number(setAmount.toFixed(0)),
              template_id: 21,
              is_post_payment: 1,
              payment_type: "card",
            };
          const isResponseCreatePayment = (await createPaymentIntentApi(
            requestCreatePayment as Partial<IStripeCreatePaymentIntentRequest>
          )) as IStripeCreatePaymentIntentResponse;

          if (isResponseCreatePayment?.success) {
            const requestUpdatePayment: Partial<IStripeUpdatePaymentIntentRequest> =
              {
                company_id,
                payment_intent_id: isResponseCreatePayment.data.data.id,
                amount: Number(setAmount.toFixed(0)),
                template_id: 21,
                is_post_payment: 1,
                payment_type: "card",
                payment_id: details?.payment_id?.toString(),
              };
            const isResponseUpdatePayment = (await updatePaymentIntentApi(
              requestUpdatePayment as IStripeUpdatePaymentIntentRequest
            )) as IStripeUpdatePaymentIntentResponse;
            if (isResponseUpdatePayment?.success) {
              if (
                allow_online_payment.toString() == "2" &&
                allow_creditcard_payment.toString() == "1" &&
                invoicefind?.show_payment_link?.toString() == "1" &&
                Number(getAmount) > 0
              ) {
                isShowButton = true;
                setIsButtonShow(true);
              } else if (
                allow_online_payment.toString() == "1" &&
                allow_creditcard_payment.toString() == "1" &&
                Number(getAmount) > 0
              ) {
                isShowButton = true;
                setIsButtonShow(true);
              } else if (
                allow_online_payment.toString() == "2" &&
                allow_creditcard_payment.toString() == "1" &&
                invoicefind?.show_payment_link?.toString() == "0" &&
                Number(getAmount) > 0
              ) {
                isShowButton = false;
                setIsButtonShow(false);
              } else {
                isShowButton = false;
                setIsButtonShow(false);
              }
              if (isShowButton) {
                setClientSecretKey(
                  isResponseUpdatePayment?.data?.data?.data?.client_secret || ""
                );
                setStripeAccount(
                  isResponseUpdatePayment?.data?.data?.data?.stripe_acc_id || ""
                );
                setPaymentIntentId(
                  isResponseUpdatePayment?.data?.data?.data
                    ?.payment_intent_id || ""
                );
                setIsButtonDisablePayNow(false);
                openPostPaymentSidebar(true);
              } else {
                setIsButtonDisablePayNow(true);
              }
            } else {
              setIsButtonDisablePayNow(true);
              notification.error({
                description: isResponseUpdatePayment?.message,
              });
            }
          } else {
            setIsButtonDisablePayNow(true);
            notification.error({
              description: isResponseCreatePayment?.message,
            });
          }
        }
      }
    } catch (error) {
      setIsButtonDisablePayNow(true);
      notification.error({
        description: (error as Error)?.message ?? "Something went wrong!",
      });
    } finally {
      setIsLoadingForPayNow(false);
    }
  };

  const onCloseSidebarPostPayment = useCallback(() => {
    setClientSecretKey("");
    setStripeAccount("");
    setPaymentIntentId("");
    setIsButtonDisablePayNow(false);
    setIsButtonShow(false);
    openPostPaymentSidebar(false);
  }, [
    isButtonShow,
    clientSecretKey,
    stripeAccount,
    paymentIntentId,
    isButtonDisablePayNow,
    isOpenPostPaymentSidebar,
  ]);

  return (
    <>
      <CrudCommonCard
        headerTitle={_t("Details")}
        iconProps={{
          icon: "fa-solid fa-file-lines",
          containerClassName:
            "bg-[linear-gradient(180deg,#FF868A1a_0%,#FD33331a_100%)]",
          id: "contractor_details_icon",
          colors: ["#FF868A", "#FD3333"],
        }}
        headerRightButton={
          <>
            {!isReadOnly &&
              finalTotal >= 0 &&
              // By Change Utasv, Aditya
              // Boolean(details.is_from_stripe) == false &&
              Boolean(details.stripe_payment_status) == false &&
              paymentType?.find(
                (data) =>
                  data?.value?.toString() ==
                  inputValues?.payment_type?.toString()
              )?.key == "payment_credit_card" &&
              invoiceData
                ?.find(
                  (data) =>
                    data?.value?.toString() == details?.invoice_id?.toString()
                )
                ?.show_payment_link?.toString() == "1" &&
              stripe_activated === "1" && (
                <Button
                  icon={
                    isLoadingForPayNow ? (
                      <FontAwesomeIcon
                        className="w-3.5 h-3.5 fa-spin"
                        icon="fa-duotone fa-solid fa-spinner-third"
                      />
                    ) : (
                      <FontAwesomeIcon
                        icon="fa-regular fa-credit-card"
                        className={`w-3.5 h-3.5 ${
                          isButtonShow ||
                          isButtonDisablePayNow ||
                          getStatusForField(loadingStatus, "payment_type") ===
                            "loading"
                            ? ""
                            : "text-primary-900"
                        }`}
                      />
                    )
                  }
                  className={`w-full justify-center !border-0 !font-medium !bg-[#EBF1F9] !px-2.5 text-13 h-[26px] gap-[5px] ${
                    isButtonShow ||
                    isButtonDisablePayNow ||
                    getStatusForField(loadingStatus, "payment_type") ===
                      "loading"
                      ? "!text-primary-900/90"
                      : "!text-primary-900 hover:!text-primary-900"
                  } `}
                  disabled={
                    isButtonShow ||
                    isButtonDisablePayNow ||
                    getStatusForField(loadingStatus, "payment_type") ===
                      "loading"
                  }
                  onClick={() => {
                    if (!!details.invoice_id) {
                      paymentIntent(inputValues?.amount?.toString() || "0");
                    } else {
                      notification.error({
                        description: "Invoice not found",
                      });
                    }
                  }}
                >
                  {_t("Pay Now")}
                </Button>
              )}
          </>
        }
        children={
          <div className="pt-2">
            <ul className="w-full grid sm:gap-1 gap-2">
              <li className="overflow-hidden">
                <DatePickerField
                  label={_t("Payment Date")}
                  labelPlacement="left"
                  placeholder={_t("Select Payment Date")}
                  name="payment_date"
                  editInline={true}
                  allowClear={false}
                  iconView={true}
                  format={date_format}
                  value={
                    inputValues.payment_date
                      ? dayjs(inputValues.payment_date, date_format)
                      : undefined
                  }
                  fixStatus={getStatusForField(loadingStatus, "payment_date")}
                  onChange={(_, dateString) => {
                    if (dateString) {
                      debouncedHandleChangeDate(dateString as string);
                    } else {
                      notification.error({
                        description: "Payment date is required.",
                      });
                    }
                  }}
                  readOnly={isReadOnly}
                />
              </li>
              <li className="overflow-hidden">
                <InputNumberField
                  label={_t("Amount")}
                  placeholder={inputFormatter("0.00").value}
                  name="amount"
                  labelPlacement="left"
                  labelClass="dark:text-white/90"
                  editInline={true}
                  iconView={true}
                  required={true}
                  value={
                    inputValues.amount
                      ? formatAmount(
                          (Number(inputValues.amount) / 100).toFixed(2)
                        )
                      : ""
                  }
                  prefix={inputFormatter().currency_symbol}
                  parser={(value) => {
                    if (value === undefined) return "";
                    return unformatted(value.toString());
                  }}
                  formatter={(value, info) => {
                    const inputValue = info.input.trim();
                    const valueToFormat =
                      inputValue !== "0" && inputValue.length > 0
                        ? unformatted(inputValue)
                        : String(value);
                    return isAmountValue
                      ? inputFormatter(valueToFormat).value
                      : inputFormatter(Number(value)?.toFixed(2)).value;
                  }}
                  onKeyDown={(event) => {
                    if (event.ctrlKey && event.key === "z") {
                      event.preventDefault();
                      setInputValues({
                        ...inputValues,
                        amount: details?.amount,
                      });
                    }
                    if (event.metaKey || event.ctrlKey) {
                      return;
                    }
                    if (event.key === "Enter") {
                      setInputValues({
                        ...inputValues,
                        amount: event?.currentTarget?.value,
                      });
                    }
                    onKeyDownCurrency(event, {
                      integerDigits: 10,
                      decimalDigits: 2,
                      unformatted,
                      allowNegative: true,
                      decimalSeparator: inputFormatter().decimal_separator,
                    });
                  }}
                  onMouseEnter={() => {
                    handleChangeFieldStatus({
                      field: "amount",
                      status: "edit",
                      action: "ME",
                    });
                  }}
                  onMouseLeaveDiv={() => {
                    handleChangeFieldStatus({
                      field: "amount",
                      status: "button",
                      action: "ML",
                    });
                  }}
                  onFocus={() => {
                    handleChangeFieldStatus({
                      field: "amount",
                      status: "save",
                      action: "FOCUS",
                    });
                    setIsAmountValue(true);
                  }}
                  fixStatus={getStatusForField(loadingStatus, "amount")}
                  onChange={(value) => {
                    if (value) {
                      setInputValues({
                        ...inputValues,
                        amount: (Number(value) * 100).toString(),
                      });
                    }
                  }}
                  onBlur={(e) => {
                    const value = unformatted(e?.target?.value.trim());
                    const formattedValue = parseFloat(value).toFixed(2);
                    const oldVal =
                      inputValues.amount !== "0.00" &&
                      inputValues.amount !== "0"
                        ? details?.amount?.toString()
                        : "";
                    const normalizedOldVal = parseFloat(oldVal || "").toFixed(
                      2
                    );
                    if (formattedValue != normalizedOldVal) {
                      if (
                        formattedValue === "0.00" ||
                        formattedValue === "0" ||
                        formattedValue === "" ||
                        formattedValue === "0.0" ||
                        isNaN(parseFloat(formattedValue))
                      ) {
                        setInputValues({
                          ...inputValues,
                          amount: details?.amount?.toString(),
                        });
                        // 86cyd5qar
                        if (!!value) {
                          notification.error({
                            description: "Amount Can't be zero.",
                          });
                        } else {
                          notification.error({
                            description: "Amount is a required field.",
                          });
                        }
                      } else if (
                        Number(value) * 100 !==
                        Number(details.amount)
                      ) {
                        const paymentCard =
                          paymentType?.find(
                            (data) =>
                              data?.value?.toString() ==
                              inputValues?.payment_type?.toString()
                          )?.key == "payment_credit_card";
                        if (
                          paymentCard &&
                          Number(value) < 0.5 &&
                          stripe_activated === "1"
                        ) {
                          setInputValues({
                            ...inputValues,
                            amount: details?.amount?.toString(),
                          });
                          notification.error({
                            description: "Amount must be greater than 0.5",
                          });
                        } else {
                          setIsButtonDisablePayNow(true);
                          handleUpdateField({ amount: value.toString() });
                        }
                      } else if (
                        Number(value) * 100 ===
                        Number(details.amount)
                      ) {
                        handleChangeFieldStatus({
                          field: "amount",
                          status: "button",
                          action: "BLUR",
                        });
                        setInputValues({
                          ...inputValues,
                          amount: details?.amount?.toString(),
                        });
                      }
                    } else {
                      handleChangeFieldStatus({
                        field: "amount",
                        status: "button",
                        action: "BLUR",
                      });
                      setInputValues({
                        ...inputValues,
                        amount: details?.amount?.toString(),
                      });
                    }
                    setIsAmountValue(false);
                  }}
                  readOnly={
                    isReadOnly ||
                    details?.is_from_stripe ||
                    details?.is_from_wepay ||
                    (paymentType?.find(
                      (data) =>
                        data?.value?.toString() ==
                        inputValues?.payment_type?.toString()
                    )?.key == "payment_bank_transfer" &&
                      "pay_verified" == details?.approval_status_key &&
                      stripe_activated === "1")
                  }
                  disabled={
                    getStatusForField(loadingStatus, "amount") === "loading"
                  }
                />
              </li>
              <li className="overflow-hidden">
                <InlineField
                  label={_t("Payment Type")}
                  labelPlacement="left"
                  field={
                    <div className="flex items-center w-full">
                      <div
                        className={`${
                          !isReadOnly &&
                          paymentType?.find(
                            (data) =>
                              data?.value?.toString() ==
                              inputValues?.payment_type?.toString()
                          )?.key == "payment_credit_card" &&
                          stripe_activated === "1"
                            ? "hover:w-full focus-within:w-full focus:w-full sm:w-fit w-full"
                            : "w-full"
                        }`}
                      >
                        <SelectField
                          placeholder={_t("Select Payment Type")}
                          name="payment_type"
                          labelPlacement="left"
                          editInline={true}
                          iconView={true}
                          allowClear={false}
                          // Shail Patel :: 19-02-2025
                          // showSearch
                          // filterOption={(input, option) =>
                          //   filterOptionBySubstring(
                          //     input,
                          //     option?.label as string
                          //   )
                          // }
                          options={paymentType}
                          value={paymentType?.find(
                            (data) =>
                              data?.value?.toString() ==
                              inputValues?.payment_type?.toString()
                          )}
                          notFoundContent={
                            isApprovalStatusAndPaymentTypeLoading ? (
                              <Spin className="w-full h-[150px] flex items-center justify-center" />
                            ) : (
                              <Empty />
                            )
                          }
                          fixStatus={getStatusForField(
                            loadingStatus,
                            "payment_type"
                          )}
                          disabled={
                            getStatusForField(loadingStatus, "payment_type") ===
                            "loading"
                          }
                          onChange={(value: string | string[]) => {
                            if (value) {
                              setInputValues({
                                ...inputValues,
                                payment_type: Number(value),
                              });
                              handleSelectChange({
                                value,
                                name: "payment_type",
                              });
                            }
                          }}
                          readOnly={
                            isReadOnly ||
                            details?.is_from_stripe ||
                            details?.is_from_wepay ||
                            (paymentType?.find(
                              (data) =>
                                data?.value?.toString() ==
                                inputValues?.payment_type?.toString()
                            )?.key == "payment_bank_transfer" &&
                              "pay_verified" == details?.approval_status_key &&
                              stripe_activated === "1")
                          }
                        />
                      </div>
                      {!isReadOnly &&
                        finalTotal >= 0 &&
                        // By Change Utasv, Aditya
                        // Boolean(details.is_from_stripe) == false &&
                        Boolean(details.stripe_payment_status) == false &&
                        paymentType?.find(
                          (data) =>
                            data?.value?.toString() ==
                            inputValues?.payment_type?.toString()
                        )?.key == "payment_credit_card" &&
                        invoiceData
                          ?.find(
                            (data) =>
                              data?.value?.toString() ==
                              details?.invoice_id?.toString()
                          )
                          ?.show_payment_link?.toString() == "1" &&
                        stripe_activated === "1" && (
                          <Tooltip
                            title={_t(
                              "Payment will be processed in next screen after clicking Process Payment button."
                            )}
                            placement="top"
                          >
                            <div className="!w-6 !h-6 min-w-6 flex items-center justify-center rounded hover:!bg-[#f0f0f0] group/buttonHover">
                              <FontAwesomeIcon
                                icon="fa-regular fa-circle-info"
                                className="w-3.5 h-3.5 text-primary-900/80 group-hover/buttonHover:text-primary-900"
                              />
                            </div>
                          </Tooltip>
                        )}
                    </div>
                  }
                />
              </li>
              {(quickbook_sync?.toString() === "1" ||
                quickbook_desktop_sync?.toString() === "1") && (
                <li className="overflow-hidden">
                  <SelectField
                    label={_t("Deposit To")}
                    placeholder={_t("Select Deposit To")}
                    name="deposit_to"
                    labelPlacement="left"
                    editInline={true}
                    iconView={true}
                    showSearch
                    allowClear={false}
                    options={depositeList?.empRlsTypeList}
                    filterOption={(input, option) =>
                      filterOptionBySubstring(input, option?.label as string)
                    }
                    value={depositeList?.empRlsTypeList?.find(
                      (data) =>
                        data?.value?.toString() ==
                        inputValues?.deposit_to?.toString()
                    )}
                    isRequired={false}
                    fixStatus={getStatusForField(loadingStatus, "deposit_to")}
                    disabled={
                      getStatusForField(loadingStatus, "deposit_to") ===
                      "loading"
                    }
                    onChange={(value) => {
                      setInputValues({
                        ...inputValues,
                        deposit_to: Number(value),
                      });
                      handleSelectChange({
                        value,
                        name: "deposit_to",
                      });
                    }}
                    onClear={() => {
                      handleUpdateField({
                        deposit_to: "",
                      });
                    }}
                    readOnly={isReadOnly}
                  />
                </li>
              )}
              <li className="overflow-hidden">
                <InlineField
                  label={_t("Invoice") + " #"}
                  labelPlacement="left"
                  field={
                    <div className="flex items-center w-full sm:max-w-[calc(100%-171px)]">
                      <div
                        className={`hover:w-full focus-within:w-full focus:w-full ${
                          !!inputValues?.invoice_id?.toString() ||
                          !["loading", "success", "error"].includes(
                            getStatusForField(loadingStatus, "invoice_id")
                          )
                            ? "max-w-[calc(100%-24px)]"
                            : "w-full"
                        }`}
                      >
                        <SelectField
                          placeholder={_t("Select Invoice")}
                          name="invoice_id"
                          labelPlacement="left"
                          editInline={true}
                          iconView={true}
                          allowClear={false}
                          showSearch
                          filterOption={(input, option) =>
                            filterOptionBySubstring(
                              input,
                              option?.label as string
                            )
                          }
                          options={invoiceData}
                          value={invoiceData?.find(
                            (data) =>
                              data?.value?.toString() ==
                              inputValues?.invoice_id?.toString()
                          )}
                          notFoundContent={
                            isInvoiceListDataLoading ? (
                              <Spin className="w-full h-[150px] flex items-center justify-center" />
                            ) : (
                              <Empty />
                            )
                          }
                          fixStatus={getStatusForField(
                            loadingStatus,
                            "invoice_id"
                          )}
                          disabled={
                            getStatusForField(loadingStatus, "invoice_id") ===
                            "loading"
                          }
                          onChange={(value) => {
                            if (value) {
                              setInputValues({
                                ...inputValues,
                                invoice_id: Number(value),
                              });
                              handleSelectChange({
                                value,
                                name: "invoice_id",
                              });
                            }
                          }}
                          readOnly={isReadOnly}
                        />
                      </div>
                      {!!inputValues?.invoice_id && (
                        <InvoiceFieldRedirectionIcon
                          invoiceId={inputValues.invoice_id.toString()}
                        />
                      )}
                    </div>
                  }
                />
              </li>
              <li className="overflow-hidden">
                <InputField
                  label={_t("Reference") + " #"}
                  placeholder={_t("Reference")}
                  labelPlacement="left"
                  name="reference_id"
                  editInline={true}
                  iconView={true}
                  value={HTMLEntities.decode(
                    sanitizeString(inputValues?.reference_id)
                  )}
                  onMouseEnter={() => {
                    handleChangeFieldStatus({
                      field: "reference_id",
                      status: "edit",
                      action: "ME",
                    });
                  }}
                  onMouseLeaveDiv={() => {
                    handleChangeFieldStatus({
                      field: "reference_id",
                      status: "button",
                      action: "ML",
                    });
                  }}
                  onFocus={() =>
                    handleChangeFieldStatus({
                      field: "reference_id",
                      status: "save",
                      action: "FOCUS",
                    })
                  }
                  fixStatus={getStatusForField(loadingStatus, "reference_id")}
                  onChange={(event) => {
                    setInputValues({
                      ...inputValues,
                      reference_id: event.target.value,
                    });
                  }}
                  onBlur={(e) => {
                    const value = e?.target?.value.trim();
                    if (value !== details?.reference_id) {
                      handleUpdateField({ reference_id: value });
                    } else {
                      handleChangeFieldStatus({
                        field: "reference_id",
                        status: "button",
                        action: "BLUR",
                      });
                      setInputValues({
                        ...inputValues,
                        reference_id: details.reference_id,
                      });
                    }
                  }}
                  readOnly={isReadOnly}
                  disabled={
                    getStatusForField(loadingStatus, "reference_id") ===
                    "loading"
                  }
                />
              </li>
            </ul>
          </div>
        }
      />
      {isOpenPostPaymentSidebar && !!clientSecretKey && !!stripeAccount && (
        <PostPayment
          postPayment={isOpenPostPaymentSidebar}
          setPostPayment={openPostPaymentSidebar}
          clientSecretKey={clientSecretKey}
          stripeAccount={stripeAccount}
          paymentIntentId={paymentIntentId}
          onCloseSidebarPostPayment={onCloseSidebarPostPayment}
        />
      )}

      {isConfirmDialogOpen && (
        <ConfirmModal
          isOpen={isConfirmDialogOpen}
          modalIcon="fa-regular fa-triangle-exclamation"
          modaltitle={_t("Update Invoice")}
          description={_t(
            `This will cause a mismatch between the existing Stripe account details and the payment record. Are you sure you want to update the invoice?`
          )}
          isLoading={isInvoiceUpdateLoader}
          onCloseModal={() => {
            setIsConfirmDialogOpen(false);
            setInputValues({
              ...inputValues,
              invoice_id: Number(details?.invoice_id),
            });
          }}
          onAccept={async () => {
            try {
              setIsInvoiceUpdateLoader(true);
              const fetchPaymentHistory = (await fetchPaymentHistoryApi({
                id: inputValues?.invoice_id?.toString() || "",
                get_payment_link: 0,
              })) as IInvoicePaymentHistoryApiRes;

              if (fetchPaymentHistory.success) {
                const invoiceUpdateData =
                  fetchPaymentHistory?.data?.payments ?? [];
                const totalPayments = invoiceUpdateData
                  .filter(
                    (payment) => payment.approval_status_key !== "pay_failed"
                  )
                  .reduce(
                    (sum, payment) => sum + parseFloat(payment.amount) / 100,
                    0
                  );

                // Calculate the remaining balance
                let remaining_balance =
                  Number(fetchPaymentHistory?.data?.invoice_total ?? 0) / 100 -
                  totalPayments;
                if (remaining_balance < 0) {
                  setIsConfirmDialogOpen(false);
                  setInputValues({
                    ...inputValues,
                    invoice_id: Number(details?.invoice_id),
                  });
                  notification.error({
                    description: "Amount should not be greater than Due Amount",
                  });
                } else {
                  handleUpdateField({
                    invoice_id: inputValues?.invoice_id?.toString() || "",
                  });
                }
              } else {
                setIsConfirmDialogOpen(false);
                setInputValues({
                  ...inputValues,
                  invoice_id: Number(details?.invoice_id),
                });
                notification.error({
                  description:
                    fetchPaymentHistory.message || "Something went wrong!",
                });
              }
            } catch (error) {
              notification.error({
                description:
                  (error as Error).message || "Something went wrong!",
              });
            } finally {
              setIsInvoiceUpdateLoader(false);
            }
          }}
          onDecline={() => {
            setIsConfirmDialogOpen(false);
            setInputValues({
              ...inputValues,
              invoice_id: Number(details?.invoice_id),
            });
          }}
        />
      )}
    </>
  );
};

export default DetailsCard;
