import { useEffect, useMemo, useRef, useState } from "react";
import isEmpty from "lodash/isEmpty";
// Atoms
import { Typography } from "~/shared/components/atoms/typography";
// Molecules
import { GoogleMap } from "~/shared/components/molecules/googleMap";
import { FieldLabel } from "~/shared/components/molecules/fieldLabel";
import { CrudCommonCard } from "~/shared/components/molecules/crudCommonCard";
import { FieldStatus } from "~/shared/components/molecules/fieldStatus";
// Other
import { dirAddrLatLong } from "~/modules/people/directory/utils/constasnts";

import {
  getStatusForField,
  getStatusActionForField,
} from "~/shared/utils/helper/common";
import { getAddressComponent } from "~/shared/utils/helper/locationAddress";
import delay from "lodash/delay";
import { useTranslation } from "~/hook";
import { useAppPODispatch, useAppPOSelector } from "../../../redux/store";
import { updatePODetail } from "../../../redux/slices/poDetailSlice";
import { updatePODetailApi } from "../../../redux/action/PODetailAction";
import { useParams } from "@remix-run/react";
import { PODetailsFields } from "../../../utils/constants";
import useFieldStatus from "../../../utils/useFieldStatus ";
import { POfieldStatus } from "../../../utils/common";
import { HtmlDecode } from "../../../utils/function";

const AddressInformationField = ({ isReadOnly }: POReadOnlyComponent) => {
  const { _t } = useTranslation();
  const { id }: RouteParams = useParams();
  const dispatch = useAppPODispatch();
  const [viewUnit, setViewUnit] = useState<boolean>(false);
  const addressInfoRef = useRef<HTMLDivElement | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const toggleAddressInfo = () => {
    if (!loading) setViewUnit((current) => !current);
  };

  const { purchaseOrderDetail, isPODetailLoading } = useAppPOSelector(
    (state) => state?.purchaseOrderDetail
  );
  const {
    customer_id = 0,
    po_address1,
    po_address2,
    po_city,
    po_state,
    po_zip,
    latitude,
    longitude,
    user_id,
  } = purchaseOrderDetail || {};

  const initialValues: IPOAddrInfo = useMemo(
    () => ({
      po_address1: HtmlDecode(po_address1 || ""),
      po_address2: HtmlDecode(po_address2 || ""),
      po_city: HtmlDecode(po_city || ""),
      po_state: HtmlDecode(po_state || ""),
      latitude: Number(latitude) || 0,
      longitude: Number(longitude) || 0,
      po_zip: HtmlDecode(po_zip || ""),
      user_id: user_id || "",
    }),
    [purchaseOrderDetail]
  );

  useEffect(() => {
    setInputValues((prev: IPOAddrInfo) => ({
      ...prev,
      po_address1: HtmlDecode(po_address1?.trim() || ""),
      po_address2: HtmlDecode(po_address2?.trim() || ""),
      po_city: HtmlDecode(po_city?.trim() || ""),
      po_state: HtmlDecode(po_state?.trim() || ""),
      latitude: Number(latitude) || 0,
      longitude: Number(longitude) || 0,
      po_zip: HtmlDecode(po_zip?.trim() || ""),
      user_id: user_id || "",
    }));
  }, [purchaseOrderDetail, viewUnit]);

  const [inputValues, setInputValues] = useState<IPOAddrInfo>(initialValues);
  const { loadingStatus, handleChangeFieldStatus } =
    useFieldStatus(POfieldStatus);
  const [locationLatLong, setLocationLatLong] =
    useState<IDirAddrLatLong>(dirAddrLatLong);

  const hasPoAddress1 = Boolean(inputValues?.po_address1);
  const hasPoAddress2 = Boolean(inputValues?.po_address2);
  const hasPoCity = Boolean(inputValues?.po_city);
  const hasPoState = Boolean(inputValues?.po_state);
  const hasPoZip = Boolean(inputValues?.po_zip);

  useEffect(() => {
    if (addressInfoRef?.current) {
      const mouseEventHandler = (e: MouseEvent) => {
        if (!addressInfoRef?.current?.contains(e?.target as Node)) {
          setViewUnit(false);
        }
      };
      window.addEventListener("mousedown", mouseEventHandler);
      return () => {
        window.removeEventListener("mousedown", mouseEventHandler);
      };
    }
  }, [addressInfoRef]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e?.target;
    setInputValues({ ...inputValues, [name]: value });
  };

  const handleSelectedLocation = async (place: IdirAddrPlaceDetails) => {
    setLocationLatLong({
      ...locationLatLong,
      latitude: place?.geometry?.location?.lat(),
      longitude: place?.geometry?.location?.lng(),
    });
    const streetNumber = getAddressComponent(place, "street_number")
      ? `${getAddressComponent(place, "street_number")} `
      : "";
    const updateValues: IPOAddrInfo = {
      po_address1: `${streetNumber}${getAddressComponent(place, "route")}`,
      po_address2: "",
      po_city: getAddressComponent(place, "locality"),
      po_state: getAddressComponent(place, "administrative_area_level_1"),
      po_zip: getAddressComponent(place, "postal_code"),
      latitude: place?.geometry?.location?.lat(),
      longitude: place?.geometry?.location?.lng(),
    };
    setInputValues(updateValues);
    await updateAddressInfo(updateValues);
  };

  const handleInputBlur = async (data?: boolean) => {
    if (viewUnit && data != true) {
      return false;
    }
    const updateValues: IPOAddrInfo = {
      ...inputValues,
    };
    delete updateValues?.user_id;
    await updateAddressInfo(updateValues);
  };

  async function updateAddressInfo(updateInfo: IPOAddrInfo) {
    if (
      updateInfo &&
      updateInfo.po_address1?.trim() === po_address1?.trim() &&
      updateInfo.po_address2?.trim() === po_address2?.trim() &&
      updateInfo.po_city?.trim() === po_city?.trim() &&
      updateInfo.po_state?.trim() === po_state?.trim() &&
      updateInfo.po_zip?.trim() === po_zip?.trim()
    ) {
      return;
    }
    setLoading(true);
    setViewUnit(false);
    handleChangeFieldStatus({
      field: "address_info",
      status: "loading",
      action: "API",
    });

    try {
      const updateRes = (await updatePODetailApi({
        purchase_order_id: id || 0,

        // po_address1: HtmlDecode(updateInfo?.address1?.trim() || ""),
        // po_address2: HtmlDecode(updateInfo?.address2?.trim() || ""),
        // po_city: HtmlDecode(updateInfo?.city?.trim() || ""),
        // po_state: HtmlDecode(updateInfo?.state?.trim() || ""),
        // po_zip: HtmlDecode(updateInfo?.zip?.trim() || ""),
        customer_id: customer_id || "0",
        ...updateInfo,
      })) as IEDetailsApiRes;
      if (updateRes?.success) {
        handleChangeFieldStatus({
          field: "address_info",
          status: "success",
          action: "API",
        });
        dispatch(
          updatePODetail(
            updateInfo
            //   {
            //   po_address1: HtmlDecode(updateInfo?.address1?.trim() || ""),
            //   po_address2: HtmlDecode(updateInfo?.address2?.trim() || ""),
            //   po_city: HtmlDecode(updateInfo?.city?.trim() || ""),
            //   po_state: HtmlDecode(updateInfo?.state?.trim() || ""),
            //   po_zip: HtmlDecode(updateInfo?.zip?.trim() || ""),
            // }
          )
        );
        // const detail = updateRes?.data?.detail;
        // if (Object.keys(detail || {})?.length > 0) {
        //   dispatch(updatePODetail(detail));
        // }
      }
    } catch (error) {
      handleChangeFieldStatus({
        field: "address_info",
        status: "error",
        action: "API",
      });
      setInputValues((prev: IPOAddrInfo) => ({
        ...prev,
        po_address1: HtmlDecode(purchaseOrderDetail?.po_address1 ?? ""),
        po_address2: HtmlDecode(purchaseOrderDetail?.po_address2 ?? ""),
        po_city: HtmlDecode(purchaseOrderDetail?.po_city ?? ""),
        po_state: HtmlDecode(purchaseOrderDetail?.po_state ?? ""),
        po_zip: HtmlDecode(purchaseOrderDetail?.po_zip ?? ""),
        latitude: Number(purchaseOrderDetail?.latitude || 0),
        longitude: Number(purchaseOrderDetail?.longitude || 0),
      }));
      // notification.error({
      //   description: updateRes?.message,
      // });
    } finally {
      setLoading(false);
      delay(() => {
        const fieldAction = getStatusActionForField(
          loadingStatus,
          "address_info"
        );
        handleChangeFieldStatus({
          field: "address_info",
          status: "FOCUS" === fieldAction ? "save" : "button",
          action: fieldAction || "API",
        });
      }, 3000);
    }
  }

  return (
    <div
      className={`w-full flex sm:gap-1.5 gap-0.5 ${
        viewUnit ? "sm:flex-row flex-col" : "flex-col"
      }`}
    >
      <ul
        className={`flex flex-col gap-1 ${
          viewUnit ? "2xl:w-[110px] w-[165px]" : "w-full"
        }`}
      >
        <li className="flex sm:flex-row flex-col sm:gap-1.5 gap-0.5">
          <FieldLabel labelClass="2xl:w-[110px] 2xl:max-w-[110px] sm:w-[165px] sm:max-w-[165px] py-[7px]">
            {_t("Pickup Address")}
          </FieldLabel>
          <div
            className={`w-full gap-1 flex justify-between p-1.5 pt-2 ${
              isReadOnly
                ? "cursor-default sm:bg-transparent bg-[#f4f5f6]"
                : "hover:bg-[#f4f5f6] min-[768px]:bg-transparent bg-[#f4f5f6] cursor-pointer"
            } ${viewUnit ? "hidden" : ""}`}
            onClick={() => {
              if (isReadOnly) {
                return false;
              }
              toggleAddressInfo();
            }}
            onMouseEnter={() => {
              handleChangeFieldStatus({
                field: "address_info",
                status: "edit",
                action: "ME",
              });
            }}
            onMouseLeave={() => {
              handleChangeFieldStatus({
                field: "address_info",
                status: "button",
                action: "ML",
              });
            }}
          >
            <ul className="w-full">
              {isEmpty(po_address1) &&
              isEmpty(po_address2) &&
              isEmpty(po_city) &&
              isEmpty(po_state) &&
              isEmpty(po_zip) ? (
                "-"
              ) : (
                <>
                  <li className="text-primary-900 text-sm">
                    {/* {HtmlDecode(inputValues?.address1 ?? "")} */}
                    {po_address1}
                    {(hasPoAddress1 && hasPoAddress2) ||
                    (hasPoAddress1 && (hasPoCity || hasPoState || hasPoZip))
                      ? ", "
                      : ""}
                  </li>
                  <li className="text-primary-900 text-sm">
                    {/* {HtmlDecode(inputValues?.address2 ?? "")} */}
                    {po_address2}
                    {hasPoAddress2 && (hasPoCity || hasPoState || hasPoZip)
                      ? ", "
                      : ""}
                  </li>
                  <li>
                    <Typography className="text-primary-900 text-sm">
                      {HtmlDecode(po_city ?? "")}
                    </Typography>
                    <Typography className="text-primary-900 text-sm">
                      {hasPoCity && hasPoState
                        ? `, ${HtmlDecode(po_state)}`
                        : HtmlDecode(po_state || "")}
                      {""}
                    </Typography>
                    <Typography className="text-primary-900 text-sm">
                      {/* {inputValues?.zip
                        ? ` ${HtmlDecode(inputValues?.zip)}`
                        : ""} */}
                      {hasPoZip && (hasPoState || hasPoCity)
                        ? `, ${po_zip}`
                        : po_zip}
                    </Typography>
                  </li>
                </>
              )}
            </ul>
            {!isReadOnly && (
              <FieldStatus
                status={getStatusForField(loadingStatus, "address_info")}
              />
            )}
          </div>
        </li>
      </ul>
      <ul
        className={`w-full flex flex-col gap-1 ${
          viewUnit
            ? "2xl:max-w-[calc(100%-116px)] sm:max-w-[calc(100%-171px)]"
            : "2xl:pl-[120px] sm:pl-[175px]"
        }`}
      >
        <li className="w-full grid lg:gap-4 gap-2">
          <GoogleMap
            key={isPODetailLoading ? "map_loading" : "map_loaded"}
            ref={addressInfoRef}
            cssStyle={{ height: "150px" }}
            addressInfo={inputValues}
            mapAddress={{
              address1: purchaseOrderDetail?.po_address1 || "",
              address2: purchaseOrderDetail?.po_address2 || "",
              city: purchaseOrderDetail?.po_city || "",
              state: purchaseOrderDetail?.po_state || "",
              zip: purchaseOrderDetail?.po_zip || "",
            }}
            temperature_scale={0}
            handleInputChange={handleInputChange}
            handleSelectedLocation={handleSelectedLocation}
            isEditable={viewUnit}
            handleInputBlur={handleInputBlur}
            title={[po_address1, po_address2, po_city, po_state, po_zip]
              .filter((value) => !!value)
              .join(", ")}
          />
        </li>
      </ul>
    </div>
  );
};

export default AddressInformationField;
