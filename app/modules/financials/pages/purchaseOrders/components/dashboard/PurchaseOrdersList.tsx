// React + ag-grid
import {
  CellClickedEvent,
  GridReadyEvent,
  SortChangedEvent,
} from "ag-grid-community";
import isEmpty from "lodash/isEmpty";
import isEqual from "lodash/isEqual";
import { useEffect, useMemo, useRef } from "react";
// Hook
import { useTranslation } from "~/hook";
import useTableGridData from "~/shared/hooks/useTableGridData";
// Atoms
import { Tag } from "~/shared/components/atoms/tag";
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Popover } from "~/shared/components/atoms/popover";
import { Typography } from "~/shared/components/atoms/typography";
// Molecules
import { DateTimeCard } from "~/shared/components/molecules/dateTimeCard";
import { DynamicTable } from "~/shared/components/molecules/dynamicTable";
import { NoRecords } from "~/shared/components/molecules/noRecords";
// organisms
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";
// Constants, Shared & Common
import {
  escapeHtmlEntities,
  formatAmount,
  getDefaultStatuscolor,
} from "~/helpers/helper";
// Redux
import { getGConfig, getGModuleFilters, useGModules } from "~/zustand";
import { getPoListApi } from "../../redux/action/dashboardAction";
import { HtmlDecode } from "../../utils/function";
import { useNavigate } from "@remix-run/react";
import { routes } from "~/route-services/routes";
import POListAction from "./POListAction";

let timeout: NodeJS.Timeout;

const PurchaseOrdersList = ({
  search,
  setAddPOOpen,
}: IPurchaseOrdersListProps) => {
  const { formatter } = useCurrencyFormatter();
  const { _t } = useTranslation();
  const navigate = useNavigate();
  // const [open, setOpen] = useState<boolean>();
  const {
    module_key,
    module_name,
    module_access,
    module_singular_name,
  }: GConfig = getGConfig();

  const filterSrv = getGModuleFilters() as
    | Partial<PurchaseOrdersFilter>
    | undefined;

  const { checkModuleAccessByKey } = useGModules();
  const isReadOnly = useMemo(
    () => checkModuleAccessByKey(module_key) === "read_only",
    [module_key]
  );

  const { datasource, gridRowParams } = useTableGridData();

  const filter = useMemo(() => {
    return {
      project: filterSrv?.project,
      tab: filterSrv?.tab,
      billing_status: filterSrv?.billing_status,
      status: filterSrv?.status,
      directory_names: filterSrv?.directory_names,
      directory: filterSrv?.directory,
      is_multiple_suppliers: filterSrv?.is_multiple_suppliers,
    };
  }, [JSON.stringify(filterSrv)]);

  // use ref
  const previousValues = useRef({
    filter: JSON.stringify(filter),
    search,
  });

  // use effects
  useEffect(() => {
    if (gridRowParams?.changeGridParams) {
      if (timeout) {
        clearTimeout(timeout);
      }
      // onIsDataLoading(true);
      timeout = setTimeout(() => {
        if (!isEmpty(filterSrv)) {
          fetchPOList();
        }
      }, 500);
    }
  }, [gridRowParams?.changeGridParams]);

  useEffect(() => {
    const currentValues = {
      filter: JSON.stringify(filter),
      search,
    };

    if (
      !isEqual(previousValues.current, currentValues) &&
      !isEmpty(filterSrv)
    ) {
      previousValues.current = currentValues;
      refreshAgGrid();
    }
  }, [search, JSON.stringify(filter)]);

  // fetch to do list
  const fetchPOList = async () => {
    let gridData: { rowCount?: number; rowData: IPurchaseOrderData[] } = {
      rowData: [],
    };
    const { changeGridParams, gridParams } = gridRowParams ?? {};
    const { start, order_by_name, order_by_dir } = changeGridParams || {};
    const limit: number = changeGridParams?.length ?? 0;
    const {
      project,
      billing_status,
      status,
      directory,
      directory_names,
      tab,
      is_multiple_suppliers,
    } = filter;

    const tempFil: Partial<PurchaseOrdersFilter> = {};

    if (project) tempFil.project = project;
    if (billing_status) tempFil.billing_status = billing_status;
    if (status) tempFil.status = status;
    if (directory) tempFil.directory = directory;
    if (directory_names) tempFil.directory_names = directory_names;
    if (tab) tempFil.tab = tab;
    if (!!is_multiple_suppliers && is_multiple_suppliers !== "2")
      tempFil.is_multiple_suppliers = is_multiple_suppliers;
    const startPagination = !!start ? Math.floor(start) : 0;

    let dataParams: IPoListParams = {
      limit,
      search: escapeHtmlEntities(search),
      filter: tempFil,
      order_by_name: order_by_name,
      order_by_dir: order_by_dir,
      start: startPagination,
      ignore_filter: 1, // without data-base
    };
    if (search === "") {
      delete dataParams.search;
    }
    if (isEmpty(tempFil)) {
      delete dataParams.filter;
    }
    if (!order_by_name || !order_by_dir) {
      delete dataParams.order_by_name;
      delete dataParams.order_by_dir;
    }

    try {
      if (startPagination > 0) {
        // onIsDataLoading(false);
      }
      gridParams?.api.hideOverlay();
      const resData = (await getPoListApi(
        dataParams as IPoListParmas
      )) as IPurchaseOrderApiRes;
      // onIsDataLoading(false);

      const rowCount = gridParams?.api?.getDisplayedRowCount() ?? 0;
      if (resData?.data?.length < limit) {
        gridData = {
          ...gridData,
          rowCount: rowCount + (resData?.data?.length ?? 0) - 1,
        };
      }
      gridData = { ...gridData, rowData: resData?.data ?? [] };
      gridParams?.success(gridData);
      if (
        (!resData?.success || gridData.rowData.length <= 0) &&
        dataParams?.start === 0
      ) {
        gridParams?.api.showNoRowsOverlay();
      } else if (resData?.success && gridData.rowData.length > 0) {
        gridParams?.api.hideOverlay();
      }
    } catch (err) {
      // onIsDataLoading(false);
      gridParams?.success({ rowCount: 0, rowData: [] });
      gridParams?.api.showNoRowsOverlay();
      gridParams?.fail();
    }
  };

  const onGridReady = (gridParams: GridReadyEvent) => {
    gridParams?.api?.setServerSideDatasource(datasource);
  };

  const onSortChanged = async (params: SortChangedEvent) => {
    params.api.setServerSideDatasource({ getRows: () => {} });
    params.api.setServerSideDatasource(datasource);
  };

  const refreshAgGrid = () => {
    const gridParams = gridRowParams?.gridParams;
    if (gridParams) {
      gridParams.api.setServerSideDatasource({ getRows: () => {} });
      gridParams.api.setServerSideDatasource(datasource);
    }
  };

  const columnDefs = [
    {
      headerName: "PO #",
      field: "prefix_company_purchase_order_id",
      minWidth: 130,
      maxWidth: 190,
      flex: 1,
      resizable: true,
      sortable: true,
      suppressMovable: false,
      suppressMenu: true,
      cellRenderer: ({ data }: IPoTableCellRenderer) => {
        const id = HtmlDecode(data?.prefix_company_purchase_order_id);
        return !!id ? (
          <Tooltip title={id}>
            <Typography className="table-tooltip-text">{id}</Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("Date"),
      maxWidth: 135,
      minWidth: 135,
      field: "order_date",
      suppressMovable: false,
      suppressMenu: true,
      sortable: true,
      cellRenderer: ({ data }: IPoTableCellRenderer) => {
        const orderDate = HtmlDecode(data?.order_date);
        // to prevent  00-00-0000
        return !!orderDate && !orderDate?.includes("0000") ? (
          <DateTimeCard format="date" date={orderDate} />
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("Subject"),
      field: "subject",
      minWidth: 150,
      flex: 1,
      resizable: true,
      sortable: true,
      suppressMovable: false,
      suppressMenu: true,
      cellRenderer: ({ data }: IPoTableCellRenderer) => {
        const subject = HtmlDecode(data?.subject);
        return !!subject ? (
          <Tooltip title={subject}>
            <Typography className="table-tooltip-text">{subject}</Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("Project"),
      field: "project_name",
      minWidth: 150,
      flex: 1,
      resizable: true,
      sortable: true,
      suppressMovable: false,
      suppressMenu: true,
      cellRenderer: ({ data }: IPoTableCellRenderer) => {
        const projectName = HtmlDecode(data?.project_name);
        return !!projectName ? (
          <Tooltip title={projectName}>
            <Typography className="table-tooltip-text">
              {projectName}
            </Typography>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("Supplier"),
      field: "supplier_company_name",
      minWidth: 150,
      flex: 1,
      resizable: true,
      sortable: true,
      suppressMovable: false,
      suppressMenu: true,
      cellRenderer: ({ data }: IPoTableCellRenderer) => {
        // const supplier = HtmlDecode(data?.supplier_company_name);
        // const supplier = HtmlDecode(data?.supplier_name);
        // const supplierLists = data?.supplier_details?.length
        //   ? data?.supplier_details?.map((data) =>
        //       HtmlDecode(!!data?.display_name ? data?.display_name : "-")
        //     )
        //   : [];
        // const supplierLists = !!data?.multiple_supplier_names
        //   ? data?.multiple_supplier_names
        //       ?.split(",")
        //       ?.map((data) => HtmlDecode(!!data ? data : "-"))
        //   : [];
        //Now logic change ==========
        const supplierLists =
          data?.is_multiple_suppliers == 0
            ? [
                HtmlDecode(
                  !!data?.supplier_company_name
                    ? data?.supplier_company_name
                    : "-"
                ),
              ]
            : data?.supplier_details?.length
            ? data?.supplier_details?.map((data) =>
                HtmlDecode(!!data?.display_name ? data?.display_name : "-")
              )
            : [];

        return supplierLists?.length > 0 ? (
          <div className="flex items-center gap-1 w-full overflow-hidden">
            <Tooltip title={supplierLists?.[0] || ""}>
              <Typography className="table-tooltip-text">
                {supplierLists?.[0]}
              </Typography>
            </Tooltip>
            {supplierLists?.length > 1 && (
              <Popover
                trigger={"hover"}
                id={`po-supplier-popover-${data?.purchase_order_id}`}
                placement="right"
                content={
                  <div className="dark:bg-dark-900 min-w-[155px]">
                    <div className="px-2 py-1 text-sm font-semibold text-primary-900 bg-[#F3F4F6] rounded-t-lg">
                      {_t("More Suppliers")}
                    </div>
                    <ul className="py-2 px-2 grid gap-1.5 max-h-[150px] max-w-[200px] overflow-auto">
                      {supplierLists?.slice(1)?.map((supplier, index) => (
                        <li
                          className="overflow-hidden"
                          key={`supplier_name_${index}`}
                        >
                          <Tooltip title={supplier}>
                            <Typography className="text-primary-900 truncate block w-fit max-w-full">
                              {supplier}
                            </Typography>
                          </Tooltip>
                        </li>
                      ))}
                    </ul>
                  </div>
                }
                // open={open}
                // onOpenChange={(newOpen: boolean) => setOpen(newOpen)}
              >
                <div className="flex relative border border-primary-900/20 items-center justify-center px-[3px] rounded font-bold text-primary-900">
                  +{supplierLists?.length - 1}
                </div>
              </Popover>
            )}
          </div>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: _t("Total"),
      field: "total",
      minWidth: 130,
      maxWidth: 130,
      cellClass: "ag-cell-right",
      headerClass: "ag-header-right",
      suppressMovable: false,
      suppressMenu: true,
      sortable: true,
      cellRenderer: ({ data }: IPoTableCellRenderer) => {
        const nValue = Number(data?.total) / 100;
        const total = formatter(
          formatAmount(Number(nValue), { isDashboard: true })
        )?.value_with_symbol;
        return (
          <div className="text-right">
            <Tooltip title={total}>
              <Typography className="table-tooltip-text">{total}</Typography>
            </Tooltip>
          </div>
        );
      },
    },
    {
      headerName: _t("Status"),
      field: "billing_status_name",
      minWidth: 140,
      maxWidth: 140,
      cellClass: "ag-cell-center",
      headerClass: "ag-header-center",
      suppressMovable: false,
      suppressMenu: true,
      sortable: true,
      cellRenderer: ({ data }: IPoTableCellRenderer) => {
        const { color, textColor } = getDefaultStatuscolor(
          data?.default_status_color || ""
        );
        return !!data?.billing_status_name ? (
          <Tooltip title={data?.billing_status_name}>
            <div className="text-center overflow-hidden">
              {/* Notes: This text color and backgound color set by developer */}
              <Tag
                color={color}
                style={{
                  color: `${textColor || ""}`,
                }}
                className={`${
                  textColor === "" && "!text-primary-900"
                } mx-auto text-13 type-badge common-tag`}
              >
                {data?.billing_status_name}
              </Tag>
            </div>
          </Tooltip>
        ) : (
          "-"
        );
      },
    },
    {
      headerName: "",
      field: "",
      maxWidth: 50,
      minWidth: 50,
      suppressMenu: true,
      cellStyle: { textAlign: "center" },
      headerClass: "ag-header-center",
      cellClass: "!cursor-auto",
      hide: module_access === "read_only",
      cellRenderer: ({ data }: { data: Partial<IPODetailData> }) => {
        return (
          <div className="flex items-center gap-2 justify-center">
            <POListAction
              paramsData={data}
              refreshAgGrid={refreshAgGrid}
              isDashboard={true}
              pId={data?.purchase_order_id}
              isBilled={data?.is_billed}
              POID={
                !data?.custom_purchase_order_id?.toString() ||
                data?.custom_purchase_order_id?.length < 1
                  ? data?.company_purchase_order_id?.toString()
                  : data?.custom_purchase_order_id?.toString() || ""
              }
            />
          </div>
        );
      },
    },
  ];

  return (
    <div className="md:h-[calc(100dvh-270px)] h-[calc(100dvh-294px)] list-view-table ag-grid-cell-pointer ag-theme-alpine">
      <DynamicTable
        columnDefs={columnDefs}
        onGridReady={onGridReady}
        onSortChanged={onSortChanged}
        // onCellClicked={(params: CellClickedEvent) => {
        //   const column = params.column;
        //   if (column && column.getColDef().field !== "") {
        //     navigate(
        //       `${routes.MANAGE_PURCHASE_ORDERS.url}/${params.data.purchase_order_id}`
        //     );
        //   }
        // }}
        noRowsOverlayComponent={() => (
          <NoRecords
            rootClassName="w-full max-w-[280px]"
            image={`${window.ENV.CDN_URL}assets/images/create-record-list-view.svg`}
            text={
              module_access === "full_access" ||
              module_access === "own_data_access" ? (
                <div>
                  <Typography
                    className="sm:text-base text-xs underline underline-offset-1 text-black font-bold cursor-pointer"
                    onClick={() => {
                      // navigate(
                      //   `${routes?.MANAGE_PURCHASE_ORDERS?.url}?action=new`
                      // );
                      setAddPOOpen(true);
                    }}
                  >
                    {_t("Click here ")}
                  </Typography>
                  <Typography className="sm:text-base text-xs text-black font-semibold">
                    {_t("to Create a New Record")}
                  </Typography>
                </div>
              ) : (
                <Typography className="sm:text-base text-xs text-black font-semibold">
                  {_t("No Record Found")}
                </Typography>
              )
            }
          />
        )}
        enableOpenInNewTab={window.ENV.ENABLE_ALL_CLICK}
        generateOpenInNewTabUrl={(data: { purchase_order_id?: number }) =>
          `${routes.MANAGE_PURCHASE_ORDERS.url}/${data?.purchase_order_id}`
        }
        restrictOpenInNewTabFields={["email"]}
      />
    </div>
  );
};
export default PurchaseOrdersList;
