import { useTranslation } from "~/hook";
import { sanitizeString } from "~/helpers/helper";
// Atoms
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { Dropdown } from "~/shared/components/atoms/dropDown";
import { Typography } from "~/shared/components/atoms/typography";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// Molecules
import { InputField } from "~/shared/components/molecules/inputField";
import { IconButton } from "~/shared/components/molecules/iconButton";
import { ButtonField } from "~/shared/components/molecules/buttonField";
import { FieldStatus } from "~/shared/components/molecules/fieldStatus";
import { TopBarSkeleton } from "~/shared/components/molecules/topBarSkeleton";
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";
import { ProjectFieldRedirectionIcon } from "~/shared/components/molecules/fieldRedirect/projectFieldRedirectionIcon";
// Other
import { getCurrentMenuModule } from "~/zustand/global/config/slice";
import { parseParamsFromURL } from "~/components/page/$url/helper";
import { useNavigate, useParams, useRevalidator } from "@remix-run/react";
import { getGSettings, setCommonSidebarCollapse } from "~/zustand";
import { useEffect, useMemo, useState } from "react";
import BillTableDropDownItems from "~/modules/financials/pages/bills/components/dashboard/BillTableDropDownItems";

import { useAppBillDispatch, useAppBillSelector } from "../../redux/store";
import { setBillDetailLoading } from "../../redux/slices/billDetailSlice";
import { getStatusForField } from "~/shared/utils/helper/common";
import { BillDetailActionsTab } from "./BillDetailActionsTab";
import PostPayment from "./details/PostPayment";
import {
  copyBillApi,
  generateBillIntoInvoice,
} from "../../redux/action/billDetailAction";
import { statusIconMap } from "~/modules/financials/pages/bills/utils/constants";
import { getStatusList } from "~/redux/action/getStatusListAction";
import { MenuProps } from "antd";

const BillDetailsTopBar: React.FC<IBillDetailsTopbarProps> = ({
  setIsSelectProjectOpen,
  selectedProject,
  loadingStatus,
  sidebarCollapse,
  inputValues,
  setInputValues,
  handleUpdateField,
  handleChangeFieldStatus,
}) => {
  const navigate = useNavigate();
  const { _t } = useTranslation();
  const currentModule: IInitialGlobalData["config"]["current_module"] =
    getCurrentMenuModule();
  const params: Partial<RouteParams> = useParams();
  const { module_access = "no_access" } = currentModule || {};
  const [confirmArchiveDialogOpen, setConfirmArchiveDialogOpen] =
    useState<boolean>(false);
  const [isViewEmailModalOpen, setIsViewEmailModalOpen] =
    useState<boolean>(false);
  const [isShareOpen, setIsShareOpen] = useState<boolean>(false);
  const [confirmDialogOpen, setConfirmDialogOpen] = useState<boolean>(false);
  const handleInpOnChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setInputValues({ ...inputValues, [name]: value });
  };
  const [isCopyBillConfirmOpen, setIsCopyBillConfirmOpen] =
    useState<boolean>(false);

  const revalidator = useRevalidator();
  const { tab, id }: RouteParams = useParams();
  const [defaultColor, setDefaultColor] = useState<string>("");

  const [shareLink, setShareLink] = useState<string>("");
  const [isSendEmailSidebarOpen, setIsSendEmailSidebarOpen] =
    useState<boolean>(false);
  const { billDetail, isBillDetailLoading }: IBillDetailsInitialState =
    useAppBillSelector((state) => state.billDetails);
  const [activeStep, setActiveStep] = useState<string | number>("");
  const [billStatus, setBillStatus] = useState<IStatusList>();
  const dispatch = useAppBillDispatch();
  const [itemBox, setItemBox] = useState<boolean>(false);
  const gSettings: GSettings = getGSettings();
  const [selectedStatusKey, setSelectedStatusKey] = useState<string>("");

  const customId = inputValues?.custom_bill_id;
  const companyId = inputValues?.company_bill_id;

  const billIdToShow = sanitizeString(
    typeof customId === "string" && customId.trim() ? customId : companyId || ""
  );

  useEffect(() => {
    if (revalidator.state === "loading") {
      dispatch(setBillDetailLoading(true));
    } else {
      dispatch(setBillDetailLoading(false));
    }
  }, [revalidator.state]);

  const GenerateInvoice = async () => {
    if (
      gSettings?.quickbook_sync !== "0" &&
      billDetail?.data &&
      billDetail?.data?.items?.length &&
      billDetail?.data?.items
        .filter(
          (obj: { item_category: string }) => obj?.item_category !== "category"
        )
        ?.some(
          (item: Partial<ItemSideData>) =>
            Number(item?.cost_code_id) === 0 && item?.is_retainage_item !== "1"
        )
    ) {
      notification.error({
        description: "One or more items are missing Cost code.",
      });
    } else if (billDetail?.data?.project_id) {
      try {
        // Call the API and await its result
        const response = await generateBillIntoInvoice({
          bill_id: billDetail?.data?.bill_id,
        });
        if (response?.data?.invoice_id) {
          const url = `/manage-invoice/${response?.data.invoice_id}`;
          if (window && window.ENV && window.ENV.PAGE_IS_IFRAME) {
            if (window.parent && window.parent.location) {
              window.parent.location.href = url;
            }
          } else {
            navigate(url);
          }
        }
      } catch (error) {
        notification.error({
          description: "An error occurred while generating the invoice.",
        });
        console.error("Generate Invoice Error:", error);
      }
    } else {
      notification.error({
        description: "Please select project.",
      });
    }
  };

  const billStatList: IStatusList[] = useMemo(() => {
    if (!Array.isArray(billStatus)) return [];

    return billStatus.map((item: ITodoStatus) => ({
      label: HTMLEntities.decode(sanitizeString(item?.display_name || "")),
      value: item?.type_id?.toString() || "",
      default_color: item?.default_color || "",
      icon: statusIconMap[item?.name] || "",
    })) as IStatusList[];
  }, [billStatus]);

  const status = useMemo(() => {
    const statusList = billStatList?.map((item: IStatusList) => ({
      label: HTMLEntities.decode(sanitizeString(item.label)),
      key: item?.label ?? "",
      icon: (
        <FontAwesomeIcon
          icon="fa-solid fa-square"
          className="h-3.5 w-3.5"
          style={{
            color: item?.default_color,
          }}
        />
      ),
    }));

    const getSelectStatus = billStatList?.find(
      (item: IStatusList) => item?.label === activeStep
    ) || { label: activeStep, default_color: defaultColor };

    const selectStatus = (
      <Tooltip title={getSelectStatus?.label}>
        <div
          className={`py-0.5 rounded flex items-center justify-center w-full group/status-dropdown px-2.5`}
          style={{
            backgroundColor: getSelectStatus?.default_color + "1d",
          }}
        >
          <Typography
            style={{
              color: getSelectStatus?.default_color,
            }}
            className="text-xs whitespace-nowrap truncate"
          >
            {getSelectStatus?.label}
          </Typography>
        </div>
      </Tooltip>
    );
    return { statusList, selectStatus };
  }, [billStatList, activeStep]);

  const handleStatusBill: MenuProps["onClick"] = (e) => {
    const sort_Value = billStatList?.find((item) => item?.label === e?.key);
    if (
      !billDetail?.data?.items?.length &&
      activeStep === "Open" &&
      activeStep !== e.key
    ) {
      notification.error({
        description: "Please create a payment",
      });
      return;
    }

    if (billDetail?.data?.items?.length) {
      if (
        activeStep !== e.key &&
        (!billDetail?.data?.payments?.length ||
          Number(billDetail?.data?.due_balance) > 0)
      ) {
        notification.error({
          description: "The bill must be paid in full to be marked as 'Paid'",
        });
        return;
      }

      if (Number(billDetail?.data?.due_balance) <= 0 && activeStep !== e.key) {
        notification.error({
          description: "Bill is Paid",
        });
      }
    }
  };

  const BillStatusVal: IStatusList | undefined = useMemo(
    () => billStatList?.find((item: IStatusList) => item.label == activeStep),
    [activeStep, billStatList]
  );

  useEffect(() => {
    if (!billDetail?.data?.bill_status_name) {
      if (
        billDetail?.data?.due_balance &&
        Number(billDetail?.data?.due_balance) <= 0 &&
        billDetail?.data?.payments?.length
      ) {
        setActiveStep("Paid");
        setDefaultColor("#009900");
      } else {
        setActiveStep("Open");
        setDefaultColor("#FF5400");
      }
    } else {
      setActiveStep(billDetail?.data?.bill_status_name);
    }
    setSelectedStatusKey(billDetail?.data?.bill_status_name || "");
  }, [billDetail?.data?.bill_status_name, billStatList]);

  useEffect(() => {
    if (id) {
      dispatch(
        getStatusList({
          types: ["bill_status"],
        })
      )
        .then((resData) => {
          if (resData?.payload) {
            setBillStatus(resData?.payload?.data?.bill_status);
          } else {
            console.error(
              "Failed to fetch bill status:",
              resData?.payload?.message
            );
          }
        })
        .catch((error) => {
          console.error("Error fetching bill status:", error);
        });
    }
  }, [id, dispatch]);

  return (
    <>
      <div className="sticky top-0 z-[99] bg-[#F8F8F9] p-[15px] pb-0 mb-[15px]">
        <div className="flex items-center bg-white dark:bg-dark-800 py-[5px] px-3.5 shadow-[0_4px_24px_0] shadow-[#22292f1a] rounded-md">
          <div className="w-full flex flex-col-reverse md:flex-row items-center justify-between gap-2">
            {isBillDetailLoading ? (
              <TopBarSkeleton num={3} />
            ) : (
              <>
                <div className="flex items-center md:w-[calc(100%-150px)] w-full">
                  <div
                    className={`w-11 h-11 flex items-center justify-center rounded-full relative before:absolute before:w-[39px] before:h-[39px] before:top-1/2 before:left-1/2 before:-translate-x-1/2 before:-translate-y-1/2 before:rounded-full before:border-2 before:border-white`}
                    style={{
                      backgroundColor: BillStatusVal?.default_color,
                    }}
                  >
                    {BillStatusVal?.icon && (
                      <FontAwesomeIcon
                        className="w-[18px] h-[18px] text-white"
                        icon={BillStatusVal?.icon}
                      />
                    )}
                  </div>
                  <div className="pl-2.5 flex flex-col gap-0.5 max-w-[calc(100%-44px)]">
                    <ButtonField
                      label=""
                      labelProps={{
                        labelClass: "!hidden",
                      }}
                      className="h-6 py-0 w-full gap-0"
                      inputClassName="w-fit"
                      mainReadOnlyClassName="sm:w-fit max-w-full"
                      fieldClassName="w-auto"
                      spanWidthClass="w-fit"
                      buttonClassName="!text-base font-medium"
                      placeholder={_t("Select Project")}
                      readOnlyClassName="text-base !font-medium whitespace-nowrap truncate sm:block flex"
                      labelPlacement="left"
                      name="project_id"
                      editInline={true}
                      iconView={true}
                      onClick={() => {
                        setIsSelectProjectOpen(true);
                      }}
                      value={
                        (selectedProject &&
                          HTMLEntities.decode(
                            sanitizeString(selectedProject.project_name)
                          )) ||
                        ""
                      }
                      headerTooltip={`Project Name: ${HTMLEntities.decode(
                        sanitizeString(selectedProject?.project_name)
                      )}`}
                      statusProps={{
                        status: getStatusForField(loadingStatus, "project_id"),
                        className: "right-6 flex",
                        iconProps: {
                          className: "!w-[15px] !h-[15px]",
                        },
                      }}
                      disabled={
                        getStatusForField(loadingStatus, "project_id") ===
                        "loading"
                      }
                      readOnly={module_access === "read_only"}
                      required={true}
                      rightIcon={
                        selectedProject &&
                        selectedProject.id &&
                        selectedProject?.project_name &&
                        !isNaN(Number(selectedProject.id)) ? (
                          <ProjectFieldRedirectionIcon
                            projectId={`${selectedProject?.id}`}
                          />
                        ) : null
                      }
                    />
                    <div className="flex items-center gap-2">
                      <div className="flex items-center gap-2">
                        <Dropdown
                          menu={{
                            items: status?.statusList,
                            selectable: true,
                            selectedKeys: [activeStep as string],
                            onClick: handleStatusBill,
                          }}
                          disabled={true} //this will set fixed as per the https://app.clickup.com/t/86cydj85b
                          trigger={["click"]}
                          overlayClassName="dropdown-color-option-block !min-w-40"
                        >
                          {status.selectStatus}
                        </Dropdown>
                        {["loading", "success", "error"].includes(
                          getStatusForField(loadingStatus, "status")
                        ) && (
                          <FieldStatus
                            className="flex items-center"
                            iconProps={{
                              className: "!w-[15px] !h-[15px]",
                            }}
                            status={getStatusForField(loadingStatus, "status")}
                          />
                        )}
                      </div>

                      <Tooltip
                        title={`${_t("Bill")} #${HTMLEntities.decode(
                          sanitizeString(billIdToShow)
                        )}`}
                        placement="topLeft"
                      >
                        <InputField
                          value={HTMLEntities.decode(
                            sanitizeString(billIdToShow || "")
                          )}
                          name="custom_bill_id"
                          labelPlacement="left"
                          placeholder={_t("Bill") + " #"}
                          editInline={true}
                          iconView={true}
                          readOnly={false}
                          inputStatusClassName="!w-3.5 !h-3.5"
                          className="h-5 py-0"
                          labelClass="hidden"
                          fixStatus={getStatusForField(
                            loadingStatus,
                            "custom_bill_id"
                          )}
                          disabled={
                            module_access === "read_only" ||
                            gSettings?.is_custom_bill_id == 0 ||
                            getStatusForField(
                              loadingStatus,
                              "custom_bill_id"
                            ) === "loading"
                          }
                          onChange={(e) => {
                            handleInpOnChange(e);
                          }}
                          onMouseEnter={() => {
                            handleChangeFieldStatus({
                              field: "custom_bill_id",
                              status: "edit",
                              action: "ME",
                            });
                          }}
                          onMouseLeaveDiv={() => {
                            handleChangeFieldStatus({
                              field: "custom_bill_id",
                              status: "button",
                              action: "ML",
                            });
                          }}
                          onFocus={() => {
                            handleChangeFieldStatus({
                              field: "custom_bill_id",
                              status: "save",
                              action: "FOCUS",
                            });
                          }}
                          onBlur={(e) => {
                            const value = e?.target?.value
                              ?.replace(/\s+/g, " ")
                              ?.trim();
                            if (value === "") {
                              notification.error({
                                description: _t("Bill# field is required."),
                              });
                              setInputValues({
                                ...inputValues,
                                custom_bill_id:
                                  billDetail?.data?.custom_bill_id,
                              });
                              return false;
                            }
                            if (value !== billDetail?.data?.custom_bill_id) {
                              handleUpdateField({
                                custom_bill_id: value.trim(),
                              });
                            } else {
                              handleChangeFieldStatus({
                                field: "custom_bill_id",
                                status: "button",
                                action: "BLUR",
                              });
                            }
                          }}
                        />{" "}
                      </Tooltip>
                      {gSettings?.is_custom_bill_id == 0 && (
                        <Tooltip title='Due to the "Start at Number" setting selected in the Bills configuration, you are unable to modify the Bill number.'>
                          <FontAwesomeIcon
                            className="text-base w-3.5 h-3.5 text-primary-900/80 dark:text-white/90"
                            icon="fa-regular fa-circle-info"
                          />
                        </Tooltip>
                      )}
                    </div>
                  </div>
                </div>

                <div className="flex justify-between items-center md:w-fit w-full">
                  <div className="flex gap-2.5">
                    {!window.ENV.PAGE_IS_IFRAME && (
                      <div
                        className="flex items-center cursor-pointer md:!hidden"
                        onClick={() => {
                          const params: Partial<IframeRouteParams> =
                            parseParamsFromURL(window?.location?.pathname);
                          if (params?.page && params?.id) {
                            navigate("/" + params?.page);
                          }
                        }}
                      >
                        <IconButton
                          htmlType="button"
                          variant="default"
                          className="md:!hidden group/module-menu relative w-[34px] h-[34px] !border-0 !shadow-[0px_1px_3px] !shadow-primary-200 hover:!bg-deep-orange-500/5"
                          iconClass="text-primary-900 group-hover/module-menu:text-deep-orange-500"
                          icon="fa-regular fa-chevron-left"
                        />
                      </div>
                    )}
                    <div>
                      <IconButton
                        htmlType="button"
                        variant="default"
                        className="md:!hidden group/module-menu relative w-[34px] h-[34px] !border-0 !shadow-[0px_1px_3px] !shadow-primary-200 hover:!bg-deep-orange-500/5"
                        iconClass="text-primary-900 group-hover/module-menu:text-deep-orange-500"
                        icon="fa-regular fa-bars"
                        onClick={() =>
                          setCommonSidebarCollapse(!sidebarCollapse)
                        }
                      />
                    </div>
                  </div>
                  <ul className="flex items-center justify-end gap-2.5 sm:w-fit w-full">
                    <li>
                      <ButtonWithTooltip
                        tooltipTitle={_t("Refresh")}
                        tooltipPlacement="top"
                        icon="fa-regular fa-arrow-rotate-right"
                        iconClassName="!text-primary-900 group-hover/buttonHover:!text-deep-orange-500"
                        className="!w-[34px] !h-[34px] !shadow-[0px_1px_3px] !shadow-primary-200 hover:!bg-deep-orange-500/5"
                        disabled={isBillDetailLoading}
                        onClick={() => {
                          revalidator.revalidate();
                        }}
                      />
                    </li>

                    {module_access !== "read_only" && (
                      <li>
                        <BillDetailActionsTab
                          buttonClass="!w-[34px] !h-[34px] !shadow-[0px_1px_3px] !rounded !shadow-primary-200 hover:!bg-deep-orange-500/5"
                          iconClassName="text-primary-900 group-hover/buttonHover:text-deep-orange-500"
                          tooltipcontent={_t("More")}
                          onClick={(value) => {
                            if (value === "change_status") {
                              setConfirmArchiveDialogOpen(true);
                            } else if (value === "delete") {
                              setConfirmDialogOpen(true);
                            } else if (value === "share_internal_link") {
                              setIsShareOpen(true);
                            } else if (value === "view_pdf") {
                              setIsViewEmailModalOpen(true);
                            } else if (value === "Post Payment") {
                              setItemBox(true);
                            } else if (value === "Generate an Invoice") {
                              GenerateInvoice();
                            } else if (value === "Copy bill") {
                              setIsCopyBillConfirmOpen(true);
                            }
                          }}
                          isArchive={
                            billDetail?.data?.is_deleted?.toString() === "0"
                          }
                          dueBalance={billDetail?.data?.due_balance}
                        />
                        <BillTableDropDownItems
                          confirmArchiveDialogOpen={confirmArchiveDialogOpen}
                          isCopyBillConfirmOpen={isCopyBillConfirmOpen}
                          confirmDialogOpen={confirmDialogOpen}
                          isDeleted={billDetail?.data?.is_deleted?.toString()}
                          billId={billDetail?.data?.bill_id?.toString()}
                          onClose={(value: string) => {
                            if (value === "change_status") {
                              setConfirmArchiveDialogOpen(false);
                            } else if (value === "delete") {
                              setConfirmDialogOpen(false);
                            } else if (value === "Copy_bill") {
                              setIsCopyBillConfirmOpen(false);
                            }
                          }}
                          isViewEmailModalOpen={isViewEmailModalOpen}
                          setIsViewEmailModalOpen={setIsViewEmailModalOpen}
                          isShareOpen={isShareOpen}
                          data={billDetail?.data}
                          setIsSendEmailSidebarOpen={setIsSendEmailSidebarOpen}
                          shareLink={shareLink}
                          setShareLink={setShareLink}
                          setIsShareOpen={setIsShareOpen}
                          callApiAgain={() => {
                            setConfirmArchiveDialogOpen(false);
                            setConfirmDialogOpen(false);
                            setIsCopyBillConfirmOpen(false);
                          }}
                        />
                      </li>
                    )}
                  </ul>
                </div>
              </>
            )}
          </div>
        </div>
      </div>
      {itemBox && (
        <PostPayment
          billDetail={billDetail?.data}
          setItemBox={setItemBox}
          itemBox={itemBox}
        />
      )}
    </>
  );
};

export default BillDetailsTopBar;
