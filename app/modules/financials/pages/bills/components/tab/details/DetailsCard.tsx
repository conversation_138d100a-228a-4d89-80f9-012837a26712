import { type CheckboxChangeEvent } from "antd/es/checkbox";
import { Typography } from "~/shared/components/atoms/typography";
// Molecules
import { ButtonField } from "~/shared/components/molecules/buttonField";
import { InlineField } from "~/shared/components/molecules/inlineField";
import { SelectField } from "~/shared/components/molecules/selectField";
import { ConfirmModal } from "~/shared/components/molecules/confirmModal";
import { TextAreaField } from "~/shared/components/molecules/textAreaField";
import { ContactDetails } from "~/shared/components/molecules/contactDetails";
import { CustomCheckBox } from "~/shared/components/molecules/customCheckBox";
import { CrudCommonCard } from "~/shared/components/molecules/crudCommonCard";
import { DatePickerField } from "~/shared/components/molecules/datePickerField";
import { InputNumberField } from "~/shared/components/molecules/inputNumberField";
import { SelectCustomerDrawer } from "~/shared/components/molecules/selectCustomerDrawer";
import { ContactDetailsButton } from "~/shared/components/molecules/contactDetailsButton";
import FieldRedirectButton from "~/shared/components/molecules/fieldRedirect/fieldRedirectButton/FieldRedirectButton";
import { DirectoryFieldRedirectionIcon } from "~/shared/components/molecules/fieldRedirect/directoryFieldRedirectionIcon";
import { SubContractsFieldRedirectionIcon } from "~/shared/components/molecules/fieldRedirect/subContractsFieldRedirectionIcon";
// Organisms
import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";
// Other
import { useDateFormatter, useTranslation } from "~/hook";
import {
  formatAmount,
  getDifferenceBetweenDate,
  getFormat,
  Number,
  replaceDOMParams,
  sanitizeString,
} from "~/helpers/helper";
import {
  useAppBillDispatch,
  useAppBillSelector,
} from "~/modules/financials/pages/bills/redux/store";
import {
  filterOptionBySubstring,
  getStatusForField,
} from "~/shared/utils/helper/common";
import { getCurrentMenuModule } from "~/zustand/global/config/slice";
import { useEffect, useMemo, useRef, useState } from "react";
import { getGConfig, useGModules } from "~/zustand";
import dayjs, { Dayjs } from "dayjs";
import isEmpty from "lodash/isEmpty";
import {
  backendDateFormat,
  displayDateFormat,
} from "~/shared/utils/helper/defaultDateFormat";
import { setActiveField } from "~/redux/slices/selectCustomerSlice";
import { defaultConfig } from "~/data";
import DirSendEmail from "~/modules/people/directory/components/DirSendEmail";
import { addItemObject, calculateBalanceDue } from "../../../utils/common";
import {
  addNewBillType,
  fetchTermsList,
} from "../../../redux/action/billTermsAction";
import { addBillTermsAct } from "../../../redux/slices/billTermsSlice";
import { billReferenceDetails } from "../../../redux/action/billDetailAction";
import { routes } from "~/route-services/routes";

const DetailsCard = ({
  isReadOnly = false,
  loadingStatus,
  inputValues,
  setInputValues,
  handleUpdateField,
  handleChangeFieldStatus,
  totalAmountForHeader,
}: any) => {
  const handleBillableChange = (e: CheckboxChangeEvent) => {
    const isBillable = e.target.checked;
    const updates: IBillDetailsFields = {
      is_billable: isBillable,
    };
    setInputValues({ ...inputValues, ...updates });
    handleUpdateField(updates);
  };

  const handleSharedChange = (e: CheckboxChangeEvent) => {
    const isChecked = e.target.checked;
    const updates: IBillDetailsFields = { is_shared: isChecked };
    setInputValues({ ...inputValues, ...updates });
    handleUpdateField(updates);
  };

  const dateFormat = useDateFormatter();
  const { checkModuleAccessByKey, getGModuleByKey } = useGModules();

  const descriptionFieldRef = useRef<HTMLInputElement>(null);
  const [isAddingCustomData, setIsAddingCustomData] = useState<boolean>(false);
  const [selectedData, setSelectedData] = useState<Partial<IDirectoryData>>({});
  const [isOpenContactDetails, setIsOpenContactDetails] =
    useState<boolean>(false);
  const [isConfirmDialogOpen, setIsConfirmDialogOpen] =
    useState<boolean>(false);
  const dispatch = useAppBillDispatch();
  const gConfig: GConfig = getGConfig();
  const { inputFormatter, unformatted } = useCurrencyFormatter();
  const { billTermsList }: IBillTermsInitialState = useAppBillSelector(
    (state) => state.billTerms
  );
  const { billDetail, itemFilter }: IBillDetailsInitialState =
    useAppBillSelector((state) => state.billDetails);
  const [customDataAdd, setCustomDataAdd] = useState<ICommonCustomDataFrm>({});
  const [vendor, setVendor] = useState<Partial<IDirectoryData>>({});
  const { _t } = useTranslation();
  const currentModule: IInitialGlobalData["config"]["current_module"] =
    getCurrentMenuModule();
  const { module_access = "no_access" } = currentModule || {};
  const [isOpenSelectCustomer, setIsOpenSelectCustomer] =
    useState<boolean>(false);
  const [isSendEmailSidebarOpen, setIsSendEmailSidebarOpen] =
    useState<boolean>(false);

  const [refOptionData, setRefOptionData] = useState([]);
  const purchaseOrderModuleAccess: TModuleAccessStatus = checkModuleAccessByKey(
    defaultConfig.purchase_order_module
  );
  const subContractsModuleAccess: TModuleAccessStatus = checkModuleAccessByKey(
    defaultConfig.sub_contracts_module
  );

  const subContractsModule: GModule | undefined = getGModuleByKey(
    defaultConfig.sub_contracts_module
  );

  const handleVendor = (data: Partial<IDirectoryData>) => {
    if (
      data?.user_id != billDetail?.data?.supplier_id ||
      (data?.user_id == billDetail?.data?.supplier_id &&
        data.contact_id !== billDetail?.data?.supplier_contact_id)
    ) {
      handleChangeFieldStatus({
        field: "supplier_id",
        status: "loading",
        action: "API",
      });
      setInputValues({
        ...inputValues,
        supplier_id: data.user_id?.toString() || "",
        supplier_name: data.display_name || "",
        supplier_company_name: data.display_name || "",
        supplier_dir_type: data.type || "",
        supplier_contact_id: data.contact_id || "",
        supplier_details: {
          billed_to_contact: data.contact_id || "",
          billed_to: data.user_id?.toString() || "",
          image: data.image,
          company_name: data.company_name,
          type_key: data.type_key,
        },
      });
      setVendor(data);
      handleUpdateField({
        supplier_id: data.user_id?.toString() || "",
        supplier_name: data.display_name || "",
        supplier_company_name: data.display_name || "",
        supplier_dir_type: data.type || "",
        supplier_contact_id: data.contact_id || "",
        supplier_type_key: data.type_key || "",
        supplier_details: {
          billed_to_contact: data.contact_id || "",
          billed_to: data.user_id?.toString() || "",
          image: data.image,
          company_name: data.company_name,
          type_key: data.type_key,
        },
      });
    }
  };
  useEffect(() => {
    const supplierData = {
      user_id: billDetail?.data?.supplier_id?.toString() || "",
      display_name: billDetail?.data?.supplier_name || "",
      company_name:
        (billDetail?.data?.supplier_details &&
          billDetail?.data?.supplier_details?.company_name) ||
        "",
      type: billDetail?.data?.supplier_dir_type || "",
      type_key:
        (billDetail?.data?.supplier_details &&
          billDetail?.data?.supplier_details.type_key) ||
        "",
      contact_id: billDetail?.data?.supplier_contact_id || "",
      image:
        (billDetail?.data?.supplier_details &&
        !billDetail?.data?.supplier_contact_id
          ? billDetail?.data?.supplier_details.image
          : "") || "",
    };
    if (billDetail?.data?.supplier_name && billDetail?.data?.supplier_id) {
      setVendor(supplierData);
    }
  }, [billDetail]);

  const handleChangeBillOrderDate = (date: Dayjs | DateValue | null) => {
    if (!Array.isArray(date)) {
      const dueDate = getDate(inputValues?.due_date) as string;
      if (!!date) {
        const startDateFormatted = date.format("YYYY-MM-DD");
        const difference = getDifferenceBetweenDate(
          dueDate,
          startDateFormatted,
          "days"
        );
        if (dueDate && difference < 0) {
          const errorMessage =
            "Bill date must be less than or equal to due Date.";
          notification.error({
            description: errorMessage,
            duration: 1,
          });
        } else {
          const billDate = !!date ? date?.format("YYYY-MM-DD") : "";
          const billDisplayDate = !!date
            ? date?.format(CFConfig.day_js_date_format)
            : "";
          setInputValues({
            ...inputValues,
            order_date: billDisplayDate,
          });

          handleUpdateField({
            order_date: billDate,
          });
        }
      } else {
        setInputValues({
          ...inputValues,
          order_date: date,
        });
        handleUpdateField({
          order_date: "",
        });
      }
    }
  };

  const getDate = (date: string | undefined) => {
    if (!isEmpty(date)) {
      if (date === "00/00/0000") {
        return undefined;
      }
      return dateFormat({
        date,
        dateFormat: getFormat(CFConfig.day_js_date_format),
        format: "yyyy-MM-dd",
      });
    }
    return undefined;
  };

  const handleChangeBillDueDate = (date: Dayjs | DateValue | null) => {
    if (!Array.isArray(date)) {
      const orderDate = getDate(inputValues?.order_date) as string;
      if (!!date) {
        const startDateFormatted = date.format("YYYY-MM-DD");
        const difference = getDifferenceBetweenDate(
          orderDate,
          startDateFormatted,
          "days"
        );
        if (orderDate && difference > 0) {
          const errorMessage =
            "Due date must be greater than or equal to Bill Date.";
          notification.error({
            description: errorMessage,
            duration: 1,
          });
        } else {
          const dueDate = !!date ? date?.format("YYYY-MM-DD") : "";
          const dueDisplayDate = !!date
            ? date?.format(CFConfig.day_js_date_format)
            : "";
          setInputValues({
            ...inputValues,
            due_date: dueDisplayDate,
          });

          handleUpdateField({
            due_date: dueDate,
          });
        }
      } else {
        setInputValues({
          ...inputValues,
          due_date: date,
        });
        handleUpdateField({
          due_date: null,
        });
      }
    }
  };

  const handleChange = ({ value, name }: ISingleSelectOption) => {
    const termId = /^\d+$/.test(
      billTermsList?.find((item) => item?.term_id === value?.toString())
        ?.term_id ?? ""
    )
      ? value?.toString()
      : "0";
    const termKey = billTermsList?.find(
      (item: IBillTerms) => item?.term_id === value?.toString()
    )?.term_id;
    const termName = billTermsList?.find(
      (item: IBillTerms) => item?.term_id === value?.toString()
    )?.name;
    const newValue = typeof value === "string" ? value : value[0];
    setInputValues({
      ...inputValues,
      [name]: newValue,
      ...{ ...(name === "term_name" ? { term_id: termId } : {}) },
      ...{ ...(name === "term_name" ? { term_key: termKey } : {}) },
      ...{ ...(name === "term_name" ? { term_name: termName } : {}) },
      ...{ ...(name === "ref_po" ? { ref_po: newValue } : {}) },
    });
    handleUpdateField({
      [name]: newValue,
      ...{ ...(name === "term_name" ? { term_id: termId } : {}) },
      ...{ ...(name === "term_name" ? { term_key: termKey } : {}) },
      ...{ ...(name === "term_name" ? { term_name: termName } : {}) },
      ...{ ...(name === "ref_po" ? { ref_po: newValue } : {}) },
    });
  };
  const isRefRedirectIconView = useMemo(() => {
    const accessStatusForView: TModuleAccessStatus[] = [
      "full_access",
      "own_data_access",
    ];
    return (
      accessStatusForView?.includes(purchaseOrderModuleAccess) ||
      accessStatusForView?.includes(subContractsModuleAccess)
    );
  }, [purchaseOrderModuleAccess, subContractsModuleAccess]);
  const termsListData = useMemo(() => {
    if (!billTermsList?.length) return [];

    return billTermsList
      .filter((item: IBillTerms) => {
        const cleanedName = sanitizeString(item.name).trim();
        return item.term_id && cleanedName.length > 0;
      })
      .map((item: IBillTerms) => ({
        label: HTMLEntities.decode(sanitizeString(item.name)),
        value: item.term_id.toString(),
      }));
  }, [JSON.stringify(billTermsList)]);

  const handlekeyDown = (
    event: React.KeyboardEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    if (!event.currentTarget.value) {
      return;
    }

    if (event.key === "Enter") {
      event.preventDefault();

      if (
        termsListData?.some((type: Option) =>
          type?.label
            .toLowerCase()
            .includes(event?.currentTarget?.value.toLowerCase())
        )
      ) {
        return;
      }
      setCustomDataAdd({
        name: HTMLEntities.encode(event?.currentTarget?.value),
      });
      setIsConfirmDialogOpen(true);
    }
  };

  const closeConfirmationModal = () => {
    setIsConfirmDialogOpen(false);
  };
  const refDetails = async () => {
    const response = await billReferenceDetails({
      project_id: billDetail?.data.project_id || 0,
      supplier_id: billDetail?.data?.supplier_id || 0,
    });
    if (response.success) {
      setRefOptionData(response?.data);
    } else {
      setRefOptionData([]);
    }
  };
  useEffect(() => {
    if (!!billDetail?.data?.project_id && !!billDetail?.data?.supplier_id) {
      refDetails();
    }
  }, [billDetail?.data?.supplier_id, billDetail?.data?.project_id]);
  useEffect(() => {
    dispatch(fetchTermsList());
  }, []);
  const isValidRef: boolean = useMemo(
    () =>
      !!inputValues?.ref_po &&
      !["SC0", "PO0", "0"].includes(inputValues?.ref_po),
    [inputValues]
  );

  const refOptions: DefaultOptionType[] = useMemo(() => {
    const getRefOption = (data: IBillRefernce) => ({
      label: `${data?.module_prefix} ${data?.prefix_company_primary_id} : ${data?.subject}`,
      value: data?.primary_id ?? "",
    });

    let tempRefOptions: DefaultOptionType[] = [
      {
        label: "Select a reference PO or Sub-Contract",
        value: "PO0",
        disabled: true,
      },
    ];
    const isRefAvail = refOptionData.filter(
      (ele) => ele.primary_id === inputValues?.ref_po
    );
    if (
      inputValues?.ref_po !== "0" &&
      refOptionData?.length &&
      !isRefAvail.length &&
      isValidRef
    ) {
      tempRefOptions.push({
        label: inputValues.reference_po_name,
        value: inputValues.ref_po,
      });
    }
    if (Number(inputValues.project_id) > 0) {
      tempRefOptions.push(...(refOptionData?.map(getRefOption) ?? []));
    } else if (isValidRef && inputValues.project_id === "0") {
      const refOption = refOptionData.find((data: IBillRefernce) => {
        return data.primary_id === inputValues?.ref_po;
      });
      if (refOption) {
        tempRefOptions.push(getRefOption(refOption));
      }
    }
    return tempRefOptions;
  }, [refOptionData, inputValues, isValidRef]);

  const handleAddCustomData = async () => {
    if (!isAddingCustomData && customDataAdd?.name) {
      setIsAddingCustomData(true);
      const temDataRes = (await addNewBillType({
        name: customDataAdd?.name,
      })) as ICustomDataAddUpRes;
      const newTermId: string = temDataRes?.data?.item_id?.toString();
      if (temDataRes?.success) {
        dispatch(addBillTermsAct(temDataRes?.data));
        setInputValues({
          ...inputValues,
          term_name: temDataRes?.data?.name,
          term_id: newTermId,
          term_key: newTermId,
        });
        handleUpdateField({
          term_name: temDataRes?.data?.name,
          term_id: newTermId,
          term_key: newTermId,
        });
        setIsConfirmDialogOpen(false);
      } else {
        notification.error({ description: temDataRes.message });
      }
      setIsAddingCustomData(false);
    }
  };
  const refValue = refOptions?.find(
    (refOption) => inputValues?.ref_po === refOption?.value
  )?.value;

  const getUpdatedTerms = ({
    order_date,
    due_date,
    term_key,
  }: {
    order_date?: string;
    due_date?: string;
    term_key?: string;
  }) => {
    let billDueDate = due_date;
    let dueDays = 0;
    switch (term_key) {
      //Seema(2020-10-02): If someone select option 'Due on Receipt', set due date to Current Date.
      case "invoice_term_due_on_receipt":
        billDueDate = dateFormat({
          format: getFormat(CFConfig?.day_js_date_format),
        });
        break;
      case "invoice_term_net_10":
        dueDays = 10;
        break;
      case "invoice_term_net_15":
        dueDays = 15;
        break;
      case "invoice_term_net_30":
        dueDays = 30;
        break;
      case "invoice_term_net_45":
        dueDays = 45;
        break;
      case "invoice_term_net_60":
        dueDays = 60;
        break;
    }

    if (dueDays > 0) {
      billDueDate = dayjs(
        dateFormat({
          date: order_date,
          dateFormat: getFormat(CFConfig?.day_js_date_format),
          format: "yyyy-MM-dd",
        })
      )
        .add(dueDays, "days")
        .format(CFConfig?.day_js_date_format);
    }

    let billUpdateObj: TermChangeApiParams = {
      term_key: term_key && term_key.trim() ? term_key.trim() : "",
      term_id: (!isNaN(Number(term_key)) ? Number(term_key) : 0)?.toString(),
    };

    if (billDetail?.data?.due_date !== billDueDate) {
      billUpdateObj = {
        ...billUpdateObj,
        due_date: billDueDate,
      };
    }
    return billUpdateObj;
  };

  const handleTermsUpdate = (options: DefaultOptionType) => {
    if (options) {
      let updatesTermObj = getUpdatedTerms({
        term_key: options?.value || "0",
        due_date: inputValues?.due_date,
        order_date: inputValues?.order_date,
      }) as TermChangeApiParams & { term_name: string };
      const dueDate = backendDateFormat(
        updatesTermObj.due_date as string,
        CFConfig.day_js_date_format
      );
      setInputValues({
        ...inputValues,
        ...(!!updatesTermObj.due_date
          ? { due_date: updatesTermObj.due_date }
          : {}),
        term_key: options?.value,
        term_name: options?.label,
      });

      handleUpdateField({
        term_name: options?.label,
        term_key: updatesTermObj?.term_key,
        term_id: updatesTermObj?.term_id,
        ...(!!updatesTermObj.due_date ? { due_date: dueDate } : {}),
      });
    } else {
      setInputValues({
        ...inputValues,
        term_name: "",
      });
      handleUpdateField({
        term_name: "",
      });
    }
  };

  const billItems = useMemo(
    () =>
      billDetail?.data?.items?.map((billItem: any) => ({
        ...billItem,
        total: Number(billItem?.total),
      })),
    [billDetail?.data?.items]
  );
  const isTaxAvail = itemFilter?.some((ele) => ele.apply_global_tax === "1");

  return (
    <>
      <CrudCommonCard
        headerTitle={_t("Details")}
        iconProps={{
          icon: "fa-solid fa-file-lines",
          containerClassName:
            "bg-[linear-gradient(180deg,#FF868A1a_0%,#FD33331a_100%)]",
          id: "details_icon",
          colors: ["#FF868A", "#FD3333"],
        }}
        headerRightButton={
          <div className="flex sm:flex-nowrap flex-wrap items-center gap-2">
            <CustomCheckBox
              className="gap-1.5"
              name="is_billable"
              onChange={handleBillableChange}
              checked={
                typeof inputValues.is_billable === "boolean"
                  ? inputValues.is_billable
                  : inputValues.is_billable?.toString() === "1"
              }
              disabled={
                getStatusForField(loadingStatus, "is_billable") === "loading" ||
                isReadOnly
              }
              loadingProps={{
                isLoading:
                  getStatusForField(loadingStatus, "is_billable") === "loading",
                className: "bg-[#ffffff]",
              }}
            >
              {_t("Billable")}
            </CustomCheckBox>
            {Boolean(Number(billDetail?.data?.project_id)) &&
              billDetail?.data?.show_client_access === "1" && (
                <CustomCheckBox
                  className="gap-1.5"
                  name="is_shared"
                  onChange={handleSharedChange}
                  checked={
                    typeof inputValues.is_shared === "boolean"
                      ? inputValues.is_shared
                      : inputValues.is_shared?.toString() === "1"
                  }
                  disabled={
                    getStatusForField(loadingStatus, "is_shared") ===
                      "loading" || isReadOnly
                  }
                  loadingProps={{
                    isLoading:
                      getStatusForField(loadingStatus, "is_shared") ===
                      "loading",
                    className: "bg-[#ffffff]",
                  }}
                >
                  {_t("Share with Client")}
                </CustomCheckBox>
              )}
            <Typography
              title="small"
              className="px-2.5 py-[5px] block font-medium rounded bg-blue-50 dark:bg-dark-500 text-sm text-primary-900 dark:text-white/90"
            >
              Balance Due:{" "}
              {calculateBalanceDue({
                totalAmountForHeader,
                billItemsWithoutGlobalTaxAndRetainageItems:
                  billItems?.filter(
                    (data?: BillItem) =>
                      isTaxAvail &&
                      data?.apply_global_tax !== "0" &&
                      data?.is_retainage_item !== "1"
                  ) ?? [],

                totalTaxRate: Number(billDetail?.data?.total_tax_rate) || 0,
                billPayment: Number(billDetail?.data?.bill_payment) || 0,
              })}
            </Typography>
          </div>
        }
        children={
          <div className="pt-2">
            <ul className="w-full flex flex-col gap-1 mt-[3px]">
              <li className="overflow-hidden">
                <ButtonField
                  label={_t("Vendor")}
                  placeholder={_t("Select/View Vendor")}
                  labelPlacement="left"
                  editInline={true}
                  iconView={true}
                  name="supplier_id"
                  readOnly={module_access === "read_only"}
                  onClick={() => {
                    setIsOpenSelectCustomer(true);
                  }}
                  value={replaceDOMParams(
                    sanitizeString(inputValues?.supplier_name)
                  )}
                  statusProps={{
                    status: getStatusForField(loadingStatus, "supplier_id"),
                  }}
                  disabled={
                    getStatusForField(loadingStatus, "supplier_id") ===
                    "loading"
                  }
                  avatarProps={
                    vendor?.display_name
                      ? {
                          user: {
                            name: HTMLEntities.decode(
                              sanitizeString(vendor?.display_name)
                            ),
                            image:
                              !vendor?.contact_id && !!vendor?.image
                                ? vendor?.image
                                : "",
                          },
                        }
                      : undefined
                  }
                  rightIcon={
                    inputValues &&
                    inputValues?.supplier_id &&
                    inputValues.supplier_name ? (
                      <div className="flex items-center gap-1">
                        <ContactDetailsButton
                          onClick={() => setIsOpenContactDetails(true)}
                        />
                        {vendor?.type_key && (
                          <DirectoryFieldRedirectionIcon
                            directoryId={inputValues.supplier_id.toString()}
                            directoryTypeKey={vendor.type_key}
                          />
                        )}
                      </div>
                    ) : null
                  }
                />
              </li>
              <li>
                <InputNumberField
                  label="Amount"
                  labelPlacement="left"
                  editInline={true}
                  className="!text-primary-900 placeholder:!text-[#bdbdbd] hover:!bg-transparent"
                  readOnly={gConfig?.module_read_only}
                  disabled={true}
                  value={Number(billDetail?.data?.total) / 100}
                  parser={(value) => {
                    if (!value) return "";
                    const inputValue = unformatted(value.toString());
                    return inputValue;
                  }}
                  formatter={(value) => {
                    return value
                      ? inputFormatter(formatAmount(Number(value))).value
                      : "";
                  }}
                  prefix={inputFormatter().currency_symbol}
                />
              </li>

              <li>
                <InlineField
                  label={_t("Ref.#")}
                  labelPlacement="left"
                  field={
                    <div className="flex items-center w-full sm:max-w-[calc(100%-171px)]">
                      <div
                        className={`sm:w-fit w-full hover:w-full focus-within:w-full focus:w-full ${
                          !refValue ||
                          ["loading", "success", "error"].includes(
                            getStatusForField(loadingStatus, "permit_id")
                          )
                            ? "w-full"
                            : "max-w-[calc(100%-24px)]"
                        }`}
                      >
                        <SelectField
                          placeholder={_t("Select Ref.#")}
                          labelPlacement="left"
                          editInline={true}
                          iconView={true}
                          readOnly={module_access === "read_only"}
                          name="ref_po"
                          options={refOptions}
                          value={refValue}
                          showSearch
                          allowClear={true}
                          fixStatus={getStatusForField(loadingStatus, "ref_po")}
                          disabled={
                            getStatusForField(loadingStatus, "ref_po") ===
                            "loading"
                          }
                          filterOption={(input, option) =>
                            filterOptionBySubstring(
                              input,
                              option?.label as string
                            )
                          }
                          onChange={(value: string | string[]) => {
                            handleChange({
                              value,
                              name: "ref_po",
                            });
                          }}
                          onClear={() => {
                            handleChange({ value: "0", name: "ref_po" });
                          }}
                        />
                      </div>
                      {Number(refOptionData?.length) > 0 &&
                        isValidRef &&
                        billDetail?.data?.project_id &&
                        isRefRedirectIconView &&
                        Number(billDetail?.data?.reference_primary_id) > 0 && (
                          <>
                            {subContractsModule?.module_id?.toString() ===
                              billDetail?.data?.reference_module_id?.toString() &&
                            subContractsModule?.module_key ? (
                              <SubContractsFieldRedirectionIcon
                                subContractId={billDetail?.data?.reference_primary_id?.toString()}
                              />
                            ) : (
                              <FieldRedirectButton
                                href={`${routes.MANAGE_PURCHASE_ORDERS.url}/${billDetail?.data?.reference_primary_id}`}
                                tooltipTitle={_t(
                                  "Open the detail in a new tab"
                                )}
                              />
                            )}
                          </>
                        )}
                    </div>
                  }
                />
              </li>

              <li>
                <DatePickerField
                  label={_t("Bill Date")}
                  name="order_date"
                  labelPlacement="left"
                  placeholder={CFConfig.day_js_date_format}
                  editInline={true}
                  iconView={true}
                  allowClear={false}
                  readOnly={module_access === "read_only"}
                  disabled={
                    getStatusForField(loadingStatus, "order_date") === "loading"
                  }
                  format={CFConfig.day_js_date_format}
                  value={
                    inputValues?.order_date
                      ? displayDateFormat(
                          inputValues?.order_date?.toString().trim(),
                          CFConfig.day_js_date_format
                        )
                      : null
                  }
                  fixStatus={getStatusForField(loadingStatus, "order_date")}
                  onChange={(date) => handleChangeBillOrderDate(date)}
                />
              </li>

              <li>
                <DatePickerField
                  label={_t("Due Date")}
                  name="due_date"
                  labelPlacement="left"
                  placeholder={_t("Select Date")}
                  editInline={true}
                  iconView={true}
                  readOnly={module_access === "read_only"}
                  disabled={
                    getStatusForField(loadingStatus, "due_date") === "loading"
                  }
                  format={CFConfig.day_js_date_format}
                  value={
                    inputValues?.due_date &&
                    !inputValues.due_date?.includes("0000")
                      ? displayDateFormat(
                          inputValues?.due_date?.toString().trim(),
                          CFConfig.day_js_date_format
                        )
                      : undefined
                  }
                  fixStatus={getStatusForField(loadingStatus, "due_date")}
                  onChange={(date) => handleChangeBillDueDate(date)}
                />
              </li>

              <li>
                <SelectField
                  label={_t("Terms")}
                  placeholder={_t("Select Terms")}
                  labelPlacement="left"
                  name="term_name"
                  editInline={true}
                  iconView={true}
                  readOnly={module_access === "read_only"}
                  value={
                    inputValues?.term_name && inputValues?.term_name != "0"
                      ? replaceDOMParams(sanitizeString(inputValues.term_name))
                      : !!inputValues?.term_key && !inputValues?.term_name
                      ? termsListData?.find(
                          (termType) =>
                            termType?.value === inputValues?.term_key
                        )?.label
                      : undefined
                  }
                  showSearch
                  options={termsListData}
                  allowClear={true}
                  fixStatus={getStatusForField(loadingStatus, "term_name")}
                  disabled={
                    getStatusForField(loadingStatus, "term_name") === "loading"
                  }
                  filterOption={(input, option) =>
                    filterOptionBySubstring(input, option?.label as string)
                  }
                  onChange={(_value, option: DefaultOptionType | undefined) =>
                    handleTermsUpdate(option)
                  }
                  addItem={addItemObject}
                  onInputKeyDown={(e) => handlekeyDown(e)}
                  onClear={() => {
                    handleChange({ value: "", name: "term_name" });
                  }}
                />
              </li>

              <li>
                <TextAreaField
                  label={_t("Description")}
                  placeholder={_t("Bill Description")}
                  readOnly={module_access === "read_only"}
                  ref={descriptionFieldRef}
                  disabled={isReadOnly}
                  labelPlacement="left"
                  editInline={true}
                  iconView={true}
                  value={inputValues.notes || ""}
                  onChange={(e) => {
                    setInputValues({
                      ...inputValues,
                      notes: e.target.value,
                    });
                  }}
                  fixStatus={getStatusForField(loadingStatus, "notes")}
                  onBlur={(e) => {
                    const notes = e.target.value;
                    if (notes !== billDetail?.data?.notes) {
                      handleUpdateField({
                        notes: inputValues.notes || "",
                      });
                    } else {
                      handleChangeFieldStatus({
                        field: "notes",
                        status: "button",
                        action: "BLUR",
                      });
                    }
                  }}
                  onFocus={() =>
                    handleChangeFieldStatus({
                      field: "notes",
                      status: "save",
                      action: "FOCUS",
                    })
                  }
                  onClickStsIcon={() => {
                    if (getStatusForField(loadingStatus, "notes") === "edit") {
                      descriptionFieldRef?.current?.focus();
                    }
                  }}
                  onMouseEnter={() => {
                    handleChangeFieldStatus({
                      field: "notes",
                      status: "edit",
                      action: "ME",
                    });
                  }}
                  onMouseLeaveDiv={() => {
                    handleChangeFieldStatus({
                      field: "notes",
                      status: "button",
                      action: "ML",
                    });
                  }}
                />
              </li>
            </ul>
          </div>
        }
      />
      {isOpenSelectCustomer && (
        <SelectCustomerDrawer
          openSelectCustomerSidebar={isOpenSelectCustomer}
          closeDrawer={() => {
            dispatch(setActiveField(defaultConfig.employee_key));
            setIsOpenSelectCustomer(false);
          }}
          singleSelecte={true}
          options={[
            defaultConfig.contractor_key,
            defaultConfig.vendor_key,
            defaultConfig.misc_contact_key,
            "by_service",
            "my_project",
          ]}
          projectId={billDetail?.data.project_id}
          setCustomer={(data) => {
            if (data?.length) {
              handleVendor(
                data.length ? (data[0] as Partial<IDirectoryData>) : {}
              );
            } else {
              notification.error({
                description: "Vendor field is required.",
              });
            }
          }}
          selectedCustomer={
            vendor?.user_id ? ([vendor] as TselectedContactSendMail[]) : []
          }
          groupCheckBox={true}
        />
      )}
      {isOpenContactDetails && (
        <ContactDetails
          isOpenContact={isOpenContactDetails}
          contactId={vendor?.user_id || ""}
          onCloseModal={() => {
            setIsOpenContactDetails(false);
          }}
          onEmailClick={(data) => {
            setSelectedData(data);
            setIsSendEmailSidebarOpen(true);
          }}
          readOnly={isReadOnly}
          additional_contact_id={0}
        />
      )}

      <DirSendEmail
        isOpen={isSendEmailSidebarOpen}
        options={[
          defaultConfig.contractor_key,
          defaultConfig.vendor_key,
          defaultConfig.misc_contact_key,
          "by_service",
        ]}
        onSendResponse={() => {
          setSelectedData({});
        }}
        onClose={() => {
          setIsSendEmailSidebarOpen(false);
          setSelectedData({});
        }}
        groupCheckBox={true}
        selectedCustomer={
          selectedData?.user_id
            ? ([selectedData] as TselectedContactSendMail[])
            : []
        }
        app_access={false}
      />

      {isConfirmDialogOpen && (
        <ConfirmModal
          isOpen={isConfirmDialogOpen}
          modalIcon="fa-regular fa-clipboard-list-check"
          modaltitle={_t("Add Option To List")}
          description={_t(
            `This will add "${HTMLEntities.decode(
              sanitizeString(customDataAdd?.name || "")
            )}" to the list. Do you want to add it?`
          )}
          isLoading={isAddingCustomData}
          onCloseModal={closeConfirmationModal}
          onAccept={() => {
            handleAddCustomData();
          }}
          onDecline={closeConfirmationModal}
        />
      )}
    </>
  );
};

export default DetailsCard;
