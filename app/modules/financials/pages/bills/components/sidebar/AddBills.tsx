// Hook
import { useTranslation } from "~/hook";
import { sanitizeString } from "~/helpers/helper";

// atoms
import { Drawer } from "~/shared/components/atoms/drawer";
import { Header } from "~/shared/components/atoms/header";
import { CheckBox } from "~/shared/components/atoms/checkBox";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";

// Molecules
import { InputField } from "~/shared/components/molecules/inputField";
import { SelectField } from "~/shared/components/molecules/selectField";
import { ButtonField } from "~/shared/components/molecules/buttonField";
import { CloseButton } from "~/shared/components/molecules/closeButton";
import { ConfirmModal } from "~/shared/components/molecules/confirmModal";
import { PrimaryButton } from "~/shared/components/molecules/primaryButton";
import { DatePickerField } from "~/shared/components/molecules/datePickerField";
import { SidebarCardBorder } from "~/shared/components/molecules/sidebarCardBorder";
import { SelectCustomerDrawer } from "~/shared/components/molecules/selectCustomerDrawer";
import { ContactDetailsButton } from "~/shared/components/molecules/contactDetailsButton";
import { ProjectFieldRedirectionIcon } from "~/shared/components/molecules/fieldRedirect/projectFieldRedirectionIcon";
import { DirectoryFieldRedirectionIcon } from "~/shared/components/molecules/fieldRedirect/directoryFieldRedirectionIcon";
// organisms
import { SelectProject } from "~/shared/components/organisms/selectProject";
import ContactDetailsModal from "~/shared/components/organisms/contactDetailModal/ContactDetailModal";
import OnlyRequiredCustomFields from "~/shared/components/organisms/OnlyRequiredCustomFields/OnlyRequiredCustomFields";
// Other
import { useNavigate } from "@remix-run/react";
import { useFormik } from "formik";
import { formAddBillSchema } from "./utils";
import { useEffect, useMemo, useState } from "react";
import { getCurrentMenuModule } from "~/zustand/global/config/slice";
import useDirectoryKeyValue from "~/shared/hooks/useCustomField/useDirectoryKeyValue";
import useSideBarCustomField from "~/shared/hooks/useCustomField/useSidebarCustomField";
import getCustomFieldAccess from "~/shared/utils/helper/getCustomFieldAccess";
import { defaultConfig } from "~/data";
import {
  backendDateFormat,
  displayDateFormat,
} from "~/shared/utils/helper/defaultDateFormat";
import { filterOptionBySubstring } from "~/shared/utils/helper/common";
import { formatCustomFieldForRequest } from "~/shared/utils/helper/customFieldSidebarFormat";
import { addBillAPI } from "../../redux/action/addBillAction";
import { getValuableObj } from "~/shared/utils/helper/getValuebleObj";
import {
  EVENT_LOGGER_ACTION,
  EVENT_LOGGER_NAME,
} from "~/shared/constants/event-logger";
import { useAppBillDispatch, useAppBillSelector } from "../../redux/store";
import { addItemObject } from "../../utils/constants";
import { addBillTermsAct } from "../../redux/slices/billTermsSlice";
import { getGProject, getGSettings, useExistingProjects } from "~/zustand";
import { addNewBillType } from "../../redux/action/billTermsAction";
import { getModuleAutoIncrementPrimaryId } from "~/zustand/module-auto-increment-primary-id/store";
import { setModuleAutoIncrementId } from "~/zustand/module-auto-increment-primary-id/actions";
import { getGlobalAppSettings } from "~/zustand/global/settings/slice";

const AddBills = ({
  addBillSidebarOpen,
  setAddBillSidebarOpen,
  action,
  moduleSingularName,
  defaultProjectId,
  openedUsingBtn,
  isDefaultBillable,
  handleCloseAddBillSidebar = () => {},
}: IAddBillProps) => {
  const { _t } = useTranslation();
  const currentModule: IInitialGlobalData["config"]["current_module"] =
    getCurrentMenuModule();
  const { is_custom_bill_id }: GSettings = getGSettings();
  const { project_id }: GProject = getGProject();
  const [isSubmit, setIsSubmit] = useState<boolean>(false);
  const { isNoAccessCustomField }: ICustomFieldAccess = getCustomFieldAccess();
  const { module_id, module_key } = currentModule || {};
  const { directoryKeyValue, directory }: IDirectoryFormCustomField =
    useDirectoryKeyValue();
  const navigate = useNavigate();
  const dispatch = useAppBillDispatch();
  const [isSelectProjectOpen, setIsSelectProjectOpen] =
    useState<boolean>(false);
  const [isConfirmDialogOpen, setIsConfirmDialogOpen] =
    useState<boolean>(false);
  const [isSelectVendorOpen, setIsSelectVendorOpen] = useState<boolean>(false);
  const [selectedProject, setSelectedProject] = useState<IProject[]>([]);
  const [selectedVendor, setSelectedVendor] = useState<
    TselectedContactSendMail[]
  >([]);
  const { billTermsList, defaultTerm }: IBillTermsInitialState =
    useAppBillSelector((state) => state.billTerms);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isAddingCustomData, setIsAddingCustomData] = useState<boolean>(false);
  const [isOpenContactDetails, setIsOpenContactDetails] =
    useState<boolean>(false);
  const [customDataAdd, setCustomDataAdd] = useState<ICommonCustomDataFrm>({});
  const [selectedContactId, setSelectedContactId] = useState<
    number | string | undefined
  >("");
  const appSettings: IInitialGlobalData["settings"]["app_settings"] =
    getGlobalAppSettings();
  const {
    quickbook_sync,
    date_format,
    quickbook_desktop_sync,
    is_bill_billable,
  } = appSettings || {};
  const quickbookSync = useMemo(
    () =>
      Boolean(Number(quickbook_sync)) ||
      Boolean(Number(quickbook_desktop_sync)),
    [quickbook_sync, quickbook_desktop_sync]
  );
  const defaultIsBillable = useMemo(
    () =>
      !isDefaultBillable
        ? true
        : quickbookSync
        ? Boolean(is_bill_billable)
        : false,
    [is_bill_billable, quickbookSync, isDefaultBillable]
  );
  const { componentList, loadingCustomField } = useSideBarCustomField(
    { directory, directoryKeyValue } as IDirectoryFormCustomField,
    {
      moduleId: module_id,
    } as IRequestCustomFieldForSidebar
  );
  const {
    need_to_increment,
    last_primary_id,
  }: Partial<IResponseGetModuleAutoNumber> =
    getModuleAutoIncrementPrimaryId() || {};
  const customBillId = useMemo(
    () =>
      Number(is_custom_bill_id) === 2 && need_to_increment && last_primary_id
        ? need_to_increment + last_primary_id
        : "",
    [is_custom_bill_id, need_to_increment, last_primary_id]
  );
  const { initValues, validateSchema } = formAddBillSchema();

  const initialFormValues = useMemo(() => {
    return componentList.length
      ? {
          custom_fields: componentList.reduce((acc, item) => {
            acc[item.name] = item?.value ?? "";
            return acc;
          }, {} as ICustomFieldInitValue),
        }
      : {};
  }, [componentList]);

  let options: CustomerTabs[] = [
    defaultConfig.contractor_key,
    defaultConfig.vendor_key,
    defaultConfig.misc_contact_key,
    "by_service",
    "my_project",
  ];

  const formik = useFormik({
    initialValues: {
      ...initValues,
      ...initialFormValues,
      custom_fields: initialFormValues.custom_fields
        ? initialFormValues.custom_fields
        : {},
    } as Partial<IAddBill>,
    // enableReinitialize: true,
    validationSchema: validateSchema,
    onSubmit: () => {
      handleSubmitForm();
    },
  });

  let { getExistingProjectsWithApi }: UseExistingProjectsResponse =
    useExistingProjects("project");
  const { values, handleSubmit, setFieldValue, errors } = formik;

  const fetchDefaultProject = async (id: string) => {
    const projects = await getExistingProjectsWithApi(id);
    const projectName = projects && projects[0] && projects[0].project_name;
    const projectId = Number(id) || projects[0]?.key;

    if (projectId && projectName) {
      setSelectedProject([
        {
          id: projectId,
          project_name: projectName,
        },
      ]);
    } else {
      setSelectedProject([]);
    }
  };
  useEffect(() => {
    if (
      initialFormValues &&
      !loadingCustomField &&
      Object.keys(initialFormValues).length > 0
    ) {
      formik.setFieldValue("custom_fields", initialFormValues.custom_fields);
    }
  }, [initialFormValues, loadingCustomField]);

  const handleSubmitForm = async () => {
    if (is_custom_bill_id && !formik.values.custom_bill_id) {
      formik.setFieldError("custom_bill_id", "This field is required.");
      return;
    }
    setIsSubmit(true);
    setIsLoading(true);
    let isCustomFieldValid = true;
    if (componentList.length && !isNoAccessCustomField) {
      for (let index = 0; index < componentList.length; index++) {
        const value =
          formik?.values?.custom_fields?.[componentList[index].name];
        const multiple = componentList[index].multiple;
        const typeComponent = componentList[index].type;
        if (multiple || typeComponent === "checkbox-group") {
          if (!value?.length) {
            isCustomFieldValid = false;
            break;
          }
        } else if (!value) {
          isCustomFieldValid = false;
          break;
        }
      }
    }

    if (!formik.isValid || !isCustomFieldValid) {
      setIsLoading(false);
      return;
    }

    const formData = {
      ...formik.values,
      term_key: billTermsList?.find(
        (item: IBillTerms) => item?.term_id === values?.term_id?.toString()
      )?.term_id,
      term_id: Number(values.term_id) || 0,
      order_date: backendDateFormat(
        formik.values.order_date as string,
        CFConfig.day_js_date_format
      ),
      supplier_id: Number(formik.values.vendor_id),
      ...(!!formik.values.directory_contact_id
        ? { directory_contact_id: Number(formik.values.directory_contact_id) }
        : {}),
      custom_bill_id: String(formik.values.custom_bill_id).trim(),
      ...(selectedVendor.length > 0
        ? {
            ...(selectedVendor[0].contact_id && {
              supplier_contact_id: Number(selectedVendor[0].contact_id),
            }),
          }
        : {}),

      ...(selectedProject.length > 0
        ? {
            project_id:
              Number(selectedProject[0].id) ||
              Number(selectedProject[0].project_id),
          }
        : {}),

      custom_fields:
        formik.values.custom_fields && !isNoAccessCustomField
          ? formatCustomFieldForRequest(
              formik.values.custom_fields,
              componentList,
              CFConfig.day_js_date_format
            ).custom_fields
          : undefined,
      access_to_custom_fields:
        formik.values.custom_fields && !isNoAccessCustomField ? 1 : 0,
    };

    try {
      const response = await addBillAPI(getValuableObj(formData));
      if (response?.success) {
        EventLogger.log(EVENT_LOGGER_NAME.bills + EVENT_LOGGER_ACTION.added, 1);
        setIsSubmit(false);
        setIsLoading(false);
        setAddBillSidebarOpen(!addBillSidebarOpen);
        navigate(`${response?.data?.bill_id?.toString()}`);
      } else {
        notification.error({
          description: response?.message,
        });
      }
    } catch (error) {
      setIsSubmit(false);
      setIsLoading(false);
    } finally {
      setIsSubmit(false);
      setIsLoading(false);
    }
  };

  const closeConfirmationModal = () => {
    setIsConfirmDialogOpen(false);
  };

  const termsListData = useMemo(() => {
    if (!billTermsList?.length) return [];

    return billTermsList
      .filter((item: IBillTerms) => {
        const cleanedName = sanitizeString(item.name).trim();
        return item.term_id && cleanedName.length > 0;
      })
      .map((item: IBillTerms) => ({
        label: HTMLEntities.decode(sanitizeString(item.name)),
        value: item.term_id.toString(),
      }));
  }, [JSON.stringify(billTermsList)]);

  const handlekeyDown = (
    event: React.KeyboardEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    if (!event.currentTarget.value) {
      return;
    }

    if (event.key === "Enter") {
      event.preventDefault();

      if (
        termsListData?.some((type: Option) =>
          type?.label
            .toLowerCase()
            .includes(event?.currentTarget?.value.toLowerCase())
        )
      ) {
        return;
      }
      setCustomDataAdd({
        name: HTMLEntities.encode(event?.currentTarget?.value),
      });
      setIsConfirmDialogOpen(true);
    }
  };

  const handleAddCustomData = async () => {
    if (!isAddingCustomData && customDataAdd?.name) {
      setIsAddingCustomData(true);
      const temDataRes = (await addNewBillType({
        name: customDataAdd?.name,
      })) as ICustomDataAddUpRes;

      const newTermId: string = temDataRes?.data?.item_id?.toString();
      if (temDataRes?.success) {
        dispatch(addBillTermsAct(temDataRes?.data));
        setFieldValue("term_id", newTermId);
        setIsConfirmDialogOpen(false);
      } else {
        notification.error({ description: temDataRes.message });
      }
      setIsAddingCustomData(false);
    }
  };

  useEffect(() => {
    if (defaultTerm || selectedVendor?.term_key) {
      const termId =
        selectedVendor.term_key && selectedVendor.term_key != "0"
          ? selectedVendor?.term_key
          : termsListData?.some((ele) => ele?.value === defaultTerm)
          ? defaultTerm
          : "";
      if (termId?.toString() && termId?.toString() != "0") {
        setFieldValue("term_id", termId?.toString());
      }
    } else {
      setFieldValue("term_id", "");
    }
  }, [defaultTerm, selectedVendor?.user_id]);

  useEffect(() => {
    if (!openedUsingBtn) {
      if (defaultProjectId) {
        fetchDefaultProject(defaultProjectId.toString());
      } else if (!selectedProject.length && project_id) {
        fetchDefaultProject(project_id.toString());
      }
    } else if (!selectedProject.length && project_id) {
      fetchDefaultProject(project_id.toString());
    }
  }, [defaultProjectId, project_id, action]);

  useEffect(() => {
    if (Number(is_custom_bill_id) == 2 && addBillSidebarOpen) {
      setModuleAutoIncrementId(module_id, module_key);
    }
  }, [is_custom_bill_id, addBillSidebarOpen]);

  useEffect(() => {
    setFieldValue("custom_bill_id", customBillId);
  }, [customBillId]);

  useEffect(() => {
    formik.setFieldValue(
      "is_billable",
      action
        ? !isDefaultBillable
          ? defaultIsBillable
          : selectedVendor?.[0]?.is_billable
        : defaultIsBillable
    );
  }, [defaultIsBillable]);

  return (
    <>
      <Drawer
        open={addBillSidebarOpen}
        rootClassName="drawer-open"
        width={718}
        push={false}
        classNames={{
          body: "!p-0 !overflow-hidden ",
        }}
        title={
          <div className="flex items-center">
            <div className="flex items-center justify-center rounded-full mr-2.5 w-[30px] h-[30px] bg-[#e4ecf68c] dark:bg-dark-500 text-primary-900 dark:text-white/90">
              <FontAwesomeIcon
                className="w-4 h-4"
                icon="fa-regular fa-file-invoice-dollar"
              />
            </div>
            <Header
              level={5}
              className="md:!text-[17px] !text-[15px] !text-primary-900 !mb-0 font-semibold"
            >
              {_t(
                `Add ${
                  HTMLEntities.decode(sanitizeString(moduleSingularName)) ??
                  "Bill"
                }`
              )}
            </Header>
          </div>
        }
        closeIcon={
          !window.ENV.PAGE_IS_IFRAME ? (
            <CloseButton
              onClick={() => {
                handleCloseAddBillSidebar();
                setAddBillSidebarOpen(false);
              }}
            />
          ) : null
        }
      >
        <form className="py-4" onSubmit={handleSubmit} noValidate>
          <div className="sidebar-body overflow-y-auto h-[calc(100dvh-132px)] px-4">
            <div className="grid gap-4">
              <SidebarCardBorder addGap={true}>
                <div className="w-full">
                  <ButtonField
                    label={_t("Project")}
                    name="project_id"
                    labelPlacement="top"
                    onClick={() => setIsSelectProjectOpen(true)}
                    value={
                      selectedProject.length &&
                      HTMLEntities.decode(
                        sanitizeString(selectedProject[0].project_name)
                      )
                    }
                    addonBefore={
                      selectedProject.length &&
                      selectedProject[0]?.id &&
                      !isNaN(Number(selectedProject[0]?.id)) ? (
                        <ProjectFieldRedirectionIcon
                          projectId={`${
                            selectedProject[0].id || selectedProject[0]?.key
                          }`}
                        />
                      ) : null
                    }
                  />
                </div>
                <div className="grid md:grid-cols-2 md:gap-5 gap-5">
                  <div className="w-full">
                    <ButtonField
                      label={_t("Vendor")}
                      labelPlacement="top"
                      required={true}
                      onClick={() => {
                        setIsSelectVendorOpen(true);
                      }}
                      value={
                        selectedVendor.length &&
                        HTMLEntities.decode(
                          sanitizeString(selectedVendor[0].display_name)
                        )
                      }
                      errorMessage={
                        isSubmit && errors.vendor_id ? errors.vendor_id : ""
                      }
                      avatarProps={
                        selectedVendor?.[0]?.display_name
                          ? {
                              user: {
                                name: HTMLEntities.decode(
                                  sanitizeString(
                                    selectedVendor?.[0]?.display_name
                                  )
                                ),
                                image:
                                  !selectedVendor?.[0]?.contact_id &&
                                  !!selectedVendor?.[0]?.image
                                    ? selectedVendor?.[0]?.image
                                    : "",
                              },
                            }
                          : undefined
                      }
                      addonBefore={
                        selectedVendor.length ? (
                          <div className="flex items-center gap-1">
                            <ContactDetailsButton
                              onClick={(e) => {
                                e.stopPropagation();
                                setSelectedContactId(values.vendor_id);
                                setIsOpenContactDetails(true);
                              }}
                            />
                            <DirectoryFieldRedirectionIcon
                              className="!w-5 !h-5"
                              directoryId={
                                selectedVendor[0].user_id?.toString() || ""
                              }
                              directoryTypeKey={
                                selectedVendor[0].type_key?.toString() || ""
                              }
                            />
                          </div>
                        ) : null
                      }
                    />
                  </div>
                  <div className="w-full">
                    <DatePickerField
                      label={_t("Bill Date")}
                      labelPlacement="top"
                      format={CFConfig.day_js_date_format}
                      placeholder=""
                      name="order_date"
                      allowClear={false}
                      value={displayDateFormat(
                        values.order_date?.toString().trim(),
                        CFConfig.day_js_date_format
                      )}
                      onChange={(value) => {
                        if (value) {
                          setFieldValue(
                            "order_date",
                            value.format(CFConfig.day_js_date_format)
                          );
                        } else {
                          setFieldValue("order_date", undefined);
                        }
                      }}
                      errorMessage={
                        isSubmit && errors.order_date ? errors.order_date : ""
                      }
                    />
                  </div>
                </div>
                <div className="grid md:grid-cols-2 gap-5">
                  <div className="w-full">
                    <InputField
                      label={_t("Bill #")}
                      name="custom_bill_id"
                      labelPlacement="top"
                      isRequired={true}
                      value={
                        is_custom_bill_id == 0
                          ? "Save To View"
                          : HTMLEntities.decode(
                              sanitizeString(
                                formik?.values?.custom_bill_id?.toString()
                              )
                            )
                      }
                      onChange={formik.handleChange}
                      errorMessage={
                        isSubmit &&
                        !formik.values.custom_bill_id &&
                        is_custom_bill_id
                          ? _t("This field is required.")
                          : formik.touched.custom_bill_id &&
                            errors.custom_bill_id
                          ? errors.custom_bill_id
                          : ""
                      }
                      disabled={is_custom_bill_id == 0}
                    />
                  </div>
                  <div className="w-full">
                    <SelectField
                      label={_t("Terms")}
                      value={
                        values?.term_id && values?.term_id != "0"
                          ? values?.term_id
                          : ""
                      }
                      labelPlacement="top"
                      iconView={true}
                      showSearch
                      options={termsListData}
                      allowClear={true}
                      onChange={(val) => {
                        setFieldValue("term_id", val);
                      }}
                      filterOption={(input, option) =>
                        filterOptionBySubstring(input, option?.label as string)
                      }
                      addItem={addItemObject}
                      onInputKeyDown={(e) => handlekeyDown(e)}
                    />
                  </div>
                </div>
                <div className="w-full">
                  <CheckBox
                    className="gap-1.5 w-fit"
                    name="is_billable"
                    checked={formik?.values?.is_billable}
                    onChange={(event) => {
                      const valueToSet: number = event.target.checked ? 1 : 0;
                      setFieldValue("is_billable", valueToSet);
                    }}
                  >
                    Billable
                  </CheckBox>
                </div>
              </SidebarCardBorder>
              <OnlyRequiredCustomFields
                componentList={componentList}
                formik={formik}
                isSubmit={isSubmit}
                loadingCustomField={loadingCustomField}
              />
            </div>
          </div>

          <div className="sidebar-footer flex items-center justify-center w-full px-4 pt-4">
            <PrimaryButton
              htmlType="submit"
              onClick={() => setIsSubmit(true)}
              buttonText={_t(
                `Create ${
                  HTMLEntities.decode(sanitizeString(moduleSingularName)) ??
                  "Bill"
                }`
              )}
              disabled={isLoading || loadingCustomField}
              isLoading={isLoading}
            />
          </div>
        </form>
      </Drawer>
      {isConfirmDialogOpen && (
        <ConfirmModal
          isOpen={isConfirmDialogOpen}
          modalIcon="fa-regular fa-clipboard-list-check"
          modaltitle={_t("Add Option To List")}
          description={_t(
            `This will add "${HTMLEntities.decode(
              sanitizeString(customDataAdd?.name || "")
            )}" to the list. Do you want to add it?`
          )}
          isLoading={isAddingCustomData}
          onCloseModal={closeConfirmationModal}
          onAccept={() => {
            handleAddCustomData();
          }}
          onDecline={closeConfirmationModal}
        />
      )}
      {isOpenContactDetails && (
        <ContactDetailsModal
          isOpenContact={isOpenContactDetails}
          onCloseModal={() => setIsOpenContactDetails(false)}
          contactId={Number(selectedContactId)}
          additional_contact_id={selectedVendor[0]?.contact_id?.toString()}
        />
      )}
      {isSelectProjectOpen && (
        <SelectProject
          isSingleSelect={true}
          open={isSelectProjectOpen}
          setOpen={setIsSelectProjectOpen}
          selectedProjects={selectedProject}
          onProjectSelected={(data) => {
            setSelectedProject(data);
          }}
          isRequired={false}
          genericProjects="project,opportunity"
          module_key={module_key}
        />
      )}
      {isSelectVendorOpen && (
        <SelectCustomerDrawer
          closeDrawer={() => {
            setIsSelectVendorOpen(false);
          }}
          singleSelecte={true}
          openSelectCustomerSidebar={isSelectVendorOpen}
          options={options}
          setCustomer={(data) => {
            setSelectedVendor(data);
            if (data.length > 0) {
              formik.setFieldValue(
                "vendor_id",
                data[0].user_id?.toString() || ""
              );
              if (
                data?.[0]?.type_key === CFConfig.vendor_key ||
                data?.[0]?.type_key === CFConfig.contractor_key
              ) {
                // formik.setFieldValue(
                //   "is_billable",
                //   "billed_to" in data?.[0] && data?.[0]?.billed_to ? 1 : 0
                // );
                formik.setFieldValue(
                  "is_billable",
                  "is_billable" in data?.[0] &&
                    data?.[0]?.is_billable?.toString() === "1"
                    ? 1
                    : 0
                );
              }
            } else {
              formik.setFieldValue("vendor_id", "");
              formik.setFieldValue("is_billable", defaultIsBillable);
            }
          }}
          selectedCustomer={selectedVendor}
          groupCheckBox={false}
          projectId={
            selectedProject.length > 0
              ? Number(selectedProject[0].id) || Number(selectedProject[0]?.key)
              : 0
          }
          additionalContactDetails={1}
        />
      )}
    </>
  );
};

export default AddBills;
