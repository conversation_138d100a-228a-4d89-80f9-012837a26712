// Molecules
import { InputField } from "~/shared/components/molecules/inputField";
import { SelectField } from "~/shared/components/molecules/selectField";
import { InlineField } from "~/shared/components/molecules/inlineField";
import { ConfirmModal } from "~/shared/components/molecules/confirmModal";
import { CrudCommonCard } from "~/shared/components/molecules/crudCommonCard";
import { DatePickerField } from "~/shared/components/molecules/datePickerField";
import { SelectCustomerDrawer } from "~/shared/components/molecules/selectCustomerDrawer";
import { PermitFieldRedirectionIcon } from "~/shared/components/molecules/fieldRedirect/permitFieldRedirectionIcon";
// organisms
import ContactDetailsModal from "~/shared/components/organisms/contactDetailModal/ContactDetailModal";
// Hook
import { useTranslation } from "~/hook";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { useInspectionLogDetail } from "../../../hooks/useInspectionDetails";
import {
  filterOptionBySubstring,
  getStatusForField,
} from "~/shared/utils/helper/common";
import { useInAppSelector } from "../../../redux/store";
import { Number, sanitizeString } from "~/helpers/helper";
import dayjs, { Dayjs } from "dayjs";
import isSameOrAfter from "dayjs/plugin/isSameOrAfter";
import isSameOrBefore from "dayjs/plugin/isSameOrBefore";
import {
  backendDateFormat,
  displayDateFormat,
} from "~/shared/utils/helper/defaultDateFormat";
import {
  getDirectaryIdByKey,
  getDirectaryKeyById,
} from "~/components/sidebars/multi-select/customer/zustand/action";
import { getGConfig } from "~/zustand";
import { sendNotificationToAssignee } from "../../../redux/action/inspectionDetailsAction";
import isEmpty from "lodash/isEmpty";

const PermitDetails = () => {
  const { _t } = useTranslation();
  const [isConfirmDialogOpen, setIsConfirmDialogOpen] =
    useState<boolean>(false);
  const [isAssignedToOpen, setIsAssignedToOpen] = useState<boolean>(false);
  const [contactDetailDialogOpen, setContactDetailDialogOpen] =
    useState<boolean>(false);
  const [isSendNotificationLoading, setIsSendNotificationLoading] =
    useState(false);
  const [isDateTimeVisible, setDateTimeVisible] = useState<boolean>(false);
  const dtDivRef = useRef<HTMLLIElement>(null);
  dayjs.extend(isSameOrAfter);
  dayjs.extend(isSameOrBefore);
  const gConfig = getGConfig();
  const { details, permitLogList } = useInAppSelector(
    (state) => state.inspectionDetails
  );

  const closeConfirmationModal = () => {
    setIsConfirmDialogOpen(false);
  };

  const permitLogOptions = useMemo(() => {
    let permits = permitLogList
      .map((permit) => {
        return {
          label: `${HTMLEntities.decode(
            sanitizeString(permit.permission || "")
          )}`,
          // CU-86czc591w
          //  ${
          //   permit.permit_type_name
          //     ? `(${HTMLEntities.decode(
          //         sanitizeString(permit.permit_type_name)
          //       )})`
          //     : ""
          // }
          value: permit.permit_id.toString(),
        };
      })
      .filter((option) => option.label && option.value);
    return permits || [];
  }, [permitLogList]);

  const selcPermitData = useMemo(() => {
    const selPer = permitLogList?.find(
      (item) => item.permit_id == details?.permit_id
    );
    return selPer;
  }, [details?.permit_id, permitLogList]);

  const {
    handleUpdateField,
    onMouseLeaveUpdateFieldStatus,
    updateInputFieldOnBlur,
    loadingStatus,
    isReadOnly,
    inputVals,
  } = useInspectionLogDetail();

  const getDate = (date: string | undefined) => {
    if (!isEmpty(date)) {
      if (date === "00/00/0000") {
        return undefined;
      }
      return backendDateFormat(date as string, CFConfig.day_js_date_format);
    }
    return undefined;
  };

  const handleChangePermitDate = (date: Dayjs | Date | null) => {
    if (!Array.isArray(date)) {
      if (!!date) {
        const today = getDate(details.inspection_date?.toString()) as string; // Get the start of today

        const inputDate = dayjs(date).startOf("day");
        const isValidDate = inputDate?.isSameOrAfter(today);

        const newDateToSendInApi = inputDate.format("YYYY-MM-DD");
        const newDateToUpdateInStore = inputDate.format(
          CFConfig.day_js_date_format
        );

        if (isValidDate || !details.inspection_date) {
          handleUpdateField({
            data_to_send_in_api: {
              permit_expire_date: newDateToSendInApi,
            },
            data_to_update_in_store: {
              permit_expire_date: newDateToUpdateInStore,
            },
          });
        } else {
          const errorMessage =
            "Permit expire Date should be greater than or equal to inspection date.";
          notification.error({
            description: errorMessage,
          });
        }
      } else {
        handleUpdateField({
          data_to_send_in_api: {
            permit_expire_date: "",
          },
        });
      }
    }
  };

  const selectedCustomerData = useMemo(() => {
    return Number(details.assigned_to ?? 0)
      ? ([
          {
            display_name: details?.assigned_to_name,
            user_id: details?.assigned_to,
            contact_id: details?.contact_id || 0,
            type: details.dir_type,
            type_key: getDirectaryKeyById(
              details.dir_type === 1 ? 2 : Number(details.dir_type ?? ""),
              gConfig
            ),
            image: details?.assigned_to_image,
          },
        ] as TselectedContactSendMail[])
      : [];
  }, [details]);

  const getUpdatedContact = useCallback(
    (contact: Array<Partial<TselectedContactSendMail>>) => {
      let data = contact[0] as CustomerEmail;

      if (data?.user_id !== undefined) {
        if (data?.user_id?.toString() != details.assigned_to?.toString()) {
          const assigned_to_dir_type_key =
            data.type_key !== "contact"
              ? data.type_key || ""
              : data.parent_type_key || "";

          return {
            data_to_update_in_store: {
              assignee_to_company_name: data?.company_name,
              assigned_to_name: data?.display_name,
              assigned_to_image: data?.image,
              dir_type: getDirectaryIdByKey(
                assigned_to_dir_type_key as CustomerTabs,
                gConfig
              ),
            },
            data_to_send_in_api: {
              assigned_to: data?.user_id,
              contact_id: Number(data?.contact_id),
            },
          };
        }

        return null;
      } else {
        return {
          data_to_update_in_store: {
            assignee_to_company_name: "",
            assigned_to_name: null,
            dir_type: null,
          },
          data_to_send_in_api: {
            assigned_to: null,
            contact_id: null,
          },
        };
      }
    },
    [details]
  );

  const handleContactSelection = (
    data: Array<Partial<TselectedContactSendMail>>
  ) => {
    const updatedData = getUpdatedContact(data);

    if (updatedData) {
      handleUpdateField(updatedData);
    }
  };

  const handleDrawerSelection = (
    data: Array<Partial<TselectedContactSendMail>>
  ) => {
    handleContactSelection(data);
  };

  const handleSendNotification = async () => {
    setIsSendNotificationLoading(true);
    try {
      const response = (await sendNotificationToAssignee({
        id: details.inspection_id,
      })) as ApiCallResponse;

      if (response.success) {
        setIsSendNotificationLoading(false);
        closeConfirmationModal();
      } else {
        notification.error({
          description: response.message || "Something went wrong!",
        });
        setIsSendNotificationLoading(false);
        closeConfirmationModal();
      }
    } catch (error) {
      notification.error({
        description: (error as Error).message || "Something went wrong!",
      });
      setIsSendNotificationLoading(false);
      closeConfirmationModal();
    }
  };

  const handleNameOutsideClick = (event: MouseEvent) => {
    const clickedElement = event.target as HTMLElement;

    const datePickerDropdown = document.querySelector(".ant-picker-dropdown");
    const timePickerDropdowns = document.querySelectorAll(
      ".ant-picker-time-panel"
    );

    if (
      (datePickerDropdown && datePickerDropdown.contains(clickedElement)) ||
      Array.from(timePickerDropdowns).some((timePickerDropdown) =>
        timePickerDropdown.contains(clickedElement)
      )
    ) {
      return;
    }

    if (
      dtDivRef.current &&
      dtDivRef.current.contains(clickedElement) &&
      clickedElement.tagName.toLowerCase() === "svg"
    ) {
      setDateTimeVisible(false);
      return;
    }

    if (dtDivRef.current && !dtDivRef.current.contains(clickedElement)) {
      setDateTimeVisible(false);
    }
  };

  const handlePermitSelect = (value: number) => {
    if (details.permit_id == value) return;

    const selPer = permitLogList?.find((item) => item.permit_id == value);

    let isValidDate = true;

    if (selPer?.expire_date && details?.inspection_date) {
      const permitExpireDate = dayjs(
        selPer.expire_date,
        CFConfig.day_js_date_format
      );
      const inspectionDate = dayjs(
        details.inspection_date,
        CFConfig.day_js_date_format
      );

      if (inspectionDate > permitExpireDate) {
        isValidDate = false;
      }
    }

    if (isValidDate) {
      handleUpdateField({
        data_to_send_in_api: {
          permit_expire_date: selPer?.expire_date
            ? backendDateFormat(
                selPer?.expire_date as string,
                CFConfig.day_js_date_format
              )
            : null,
          permit_id: value,
        },
        data_to_update_in_store: {
          permit_id: value,
          permit_expire_date: selPer?.expire_date,
        },
      });
    } else {
      const errorMessage =
        "Permit expire date should be greater than or equal to inspection date.";
      notification.error({
        description: errorMessage,
      });
    }
  };

  useEffect(() => {
    document.addEventListener("mousedown", handleNameOutsideClick);
    return () => {
      document.removeEventListener("mousedown", handleNameOutsideClick);
    };
  }, []);

  return (
    <>
      <CrudCommonCard
        headerTitle={_t("Permit Details")}
        iconProps={{
          icon: "fa-solid fa-stamp",
          containerClassName:
            "bg-[linear-gradient(180deg,#42DD9B1a_0%,#3CB9B31a_100%)]",
          id: "permit_details_icon",
          colors: ["#42DD9B", "#3CB9B3"],
        }}
        children={
          <div className="pt-2">
            <ul className="w-full flex flex-col gap-1 mt-[3px]">
              <li className="overflow-hidden">
                <InlineField
                  label={_t("Permit") + " #"}
                  labelPlacement="left"
                  field={
                    <div className="flex items-center w-full sm:max-w-[calc(100%-171px)]">
                      <div
                        className={`hover:w-full focus-within:w-full focus:w-full ${
                          !details.permit_id ||
                          ["loading", "success", "error"].includes(
                            getStatusForField(loadingStatus, "permit_id")
                          )
                            ? "w-full"
                            : "max-w-[calc(100%-24px)]"
                        }`}
                      >
                        <SelectField
                          placeholder={_t("Select Permit") + " #"}
                          name="permit_id"
                          labelPlacement="left"
                          editInline={true}
                          iconView={true}
                          disabled={isReadOnly}
                          readOnly={isReadOnly}
                          showSearch
                          filterOption={(input, option) =>
                            filterOptionBySubstring(
                              input,
                              option?.label as string
                            )
                          }
                          allowClear={!!Number(details?.permit_id ?? "")}
                          options={permitLogOptions || []}
                          value={
                            details.permit_id
                              ? permitLogOptions.filter((item) => {
                                  return (
                                    details?.permit_id?.toString() ===
                                    item?.value?.toString()
                                  );
                                })
                              : []
                          }
                          onSelect={(value) => {
                            handlePermitSelect(value);
                          }}
                          onClear={() => {
                            updateInputFieldOnBlur({
                              field: "permit_id",
                              value: "",
                            });
                            updateInputFieldOnBlur({
                              field: "permit_expire_date",
                              value: null,
                            });
                          }}
                          fixStatus={getStatusForField(
                            loadingStatus,
                            "permit_id"
                          )}
                        />
                      </div>
                      {details.permit_id !== null &&
                        permitLogOptions.some(
                          (opt) => opt.value === details?.permit_id?.toString()
                        ) &&
                        Boolean(Number(details.permit_id)) && (
                          <PermitFieldRedirectionIcon
                            permitId={Number(details?.permit_id)}
                          />
                        )}
                    </div>
                  }
                />
              </li>
              <li>
                <DatePickerField
                  label={_t("Permit Expiry Date")}
                  placeholder={_t("Select Date")}
                  labelPlacement="left"
                  id="permit_expire_date"
                  disabled={
                    !!details.permit_expire_date ||
                    !details.permit_id ||
                    isReadOnly
                  }
                  readOnly={isReadOnly}
                  editInline={true}
                  iconView={true}
                  format={CFConfig.day_js_date_format}
                  allowClear={true}
                  fixStatus={getStatusForField(
                    loadingStatus,
                    "permit_expire_date"
                  )}
                  value={
                    displayDateFormat(
                      details.permit_expire_date?.toString().trim(),
                      CFConfig.day_js_date_format
                    ) ?? null
                  }
                  onChange={(date) => handleChangePermitDate(date)}
                  onMouseLeave={() => {
                    onMouseLeaveUpdateFieldStatus({
                      field: "permit_expire_date",
                    });
                  }}
                />
              </li>
              <li>
                <InputField
                  label="Permit Type"
                  name="permit_type"
                  placeholder={_t("Permit Type")}
                  labelPlacement="left"
                  value={HTMLEntities.decode(
                    sanitizeString(selcPermitData?.permit_type_name)
                  )}
                  disabled
                  onChange={() => {}}
                />
              </li>
            </ul>
          </div>
        }
      />

      <ConfirmModal
        isOpen={isConfirmDialogOpen}
        modalIcon="fa-regular fa-paper-plane"
        size="510px"
        modaltitle={_t("Send Notification")}
        description={_t(
          `Do you want to send a notification to the assigned contact? Notifications will be sent based on the preferences within the user's account.`
        )}
        onCloseModal={closeConfirmationModal}
        onAccept={() => {
          handleSendNotification();
        }}
        isLoading={isSendNotificationLoading}
        onDecline={closeConfirmationModal}
      />

      {isAssignedToOpen && (
        <SelectCustomerDrawer
          openSelectCustomerSidebar={isAssignedToOpen}
          closeDrawer={() => setIsAssignedToOpen(false)}
          singleSelecte={true}
          setCustomer={(data) => {
            handleDrawerSelection(data);
          }}
          options={[
            CFConfig.employee_key,
            "my_crew",
            CFConfig.customer_key,
            CFConfig.contractor_key,
            CFConfig.vendor_key,
            CFConfig.misc_contact_key,
            "by_service",
            "my_project",
          ]}
          selectedCustomer={selectedCustomerData}
          groupCheckBox={true}
          projectId={Number(details?.project_id)}
          additionalContactDetails={1}
        />
      )}

      {contactDetailDialogOpen && (
        <ContactDetailsModal
          isOpenContact={contactDetailDialogOpen}
          onCloseModal={() => {
            setContactDetailDialogOpen(false);
          }}
          contactId={details?.assigned_to || ""}
          readOnly={isReadOnly}
          additional_contact_id={details.contact_id || 0}
          sendEmailDrawer={{
            customEmailData: { subject: details?.email_subject },
          }}
        />
      )}
    </>
  );
};

export default PermitDetails;
