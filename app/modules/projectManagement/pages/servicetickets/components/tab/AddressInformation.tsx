import { useEffect, useMemo, useRef, useState } from "react";
import isEmpty from "lodash/isEmpty";

// Atoms
import { Spin } from "~/shared/components/atoms/spin";
import { Typography } from "~/shared/components/atoms/typography";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";

// Molecules
import { GoogleMap } from "~/shared/components/molecules/googleMap";
import { FieldStatus } from "~/shared/components/molecules/fieldStatus";
import { InlineField } from "~/shared/components/molecules/inlineField";

// Hook
import { useTranslation } from "~/hook";

// Other
import { getGConfig } from "~/zustand";
import {
  dirAddrLatLong,
  fieldStatus,
} from "~/modules/people/directory/utils/constasnts";

import { getStatusForField } from "~/shared/utils/helper/common";
import { getAddressComponent } from "~/shared/utils/helper/locationAddress";
import delay from "lodash/delay";
import { useAppSTDispatch } from "../../redux/store";
import {
  fetchServiceTicketCustomerDetails,
  updateSTCustomerApi,
} from "../../redux/action/serviceTicketCustomerAction";
import { useParams } from "@remix-run/react";
import { twMerge } from "tailwind-merge";

const AddressInformation = (props: {
  addressInfo: any;
  setLoadingStatus: React.Dispatch<React.SetStateAction<IFieldStatus[]>>;
  loadingAddress: boolean;
  setViewPull?: any;
  loadingStatus: IFieldStatus[];
}) => {
  const { _t } = useTranslation();
  const {
    addressInfo,
    setLoadingStatus,
    loadingAddress = false,
    setViewPull,
    loadingStatus = fieldStatus,
  } = props;
  const params: RouteParams = useParams();
  const gConfig: GConfig = getGConfig();
  const isReadOnly = gConfig?.module_access === "read_only";
  const dispatch = useAppSTDispatch();
  const [viewUnit, setViewUnit] = useState<boolean>(false);
  const addressInfoRef = useRef<HTMLDivElement | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const toggleAddressInfo = () => {
    if (!loading) setViewUnit((current) => !current);
  };

  useEffect(() => {
    if (viewUnit) {
      setViewPull?.(viewUnit);
    } else {
      setTimeout(() => {
        setViewPull?.(viewUnit);
      }, 300);
    }
  }, [viewUnit]);

  useEffect(() => {
    if (addressInfoRef?.current) {
      const mouseEventHandler = (e: MouseEvent) => {
        if (!addressInfoRef?.current?.contains(e.target as Node)) {
          setViewUnit(false);
        }
      };
      window.addEventListener("mousedown", mouseEventHandler);
      return () => {
        window.removeEventListener("mousedown", mouseEventHandler);
      };
    }
  }, [addressInfoRef]);

  const {
    customer_street,
    customer_street2,
    customer_city,
    customer_state,
    customer_zip,
  } = addressInfo;
  const initialValues: ISTCustAddInfo = useMemo(
    () => ({
      customer_street: customer_street || "",
      customer_street2: customer_street2 || "",
      customer_city: customer_city || "",
      customer_state: customer_state || "",
      customer_zip: customer_zip || "",
    }),
    [addressInfo]
  );
  const [inputValues, setInputValues] = useState<ISTCustAddInfo>(initialValues);

  useEffect(() => {
    setInputValues((prev: ISTCustAddInfo) => ({
      ...prev,
      // ...customerDetails,
      customer_street: customer_street || "",
      customer_street2: customer_street2 || "",
      customer_city: customer_city || "",
      customer_state: customer_state || "",
      customer_zip: customer_zip || "",
    }));
  }, [addressInfo, viewUnit]);

  const hasCustAddress1 = Boolean(inputValues?.customer_street);
  const hasCustAddress2 = Boolean(inputValues?.customer_street2);
  const hasCustCity = Boolean(inputValues?.customer_city);
  const hasCustState = Boolean(inputValues?.customer_state);
  const hasCustZip = Boolean(inputValues?.customer_zip);

  const [locationLatLong, setLocationLatLong] =
    useState<ISTAddrLatLong>(dirAddrLatLong);

  const handleChangeFieldStatus = ({ field, status, action }: IFieldStatus) => {
    const checkStatus = loadingStatus.find(
      (item: IFieldStatus) => item?.field === field
    );
    if (
      !(
        (checkStatus?.status === "loading" ||
          checkStatus?.status === "success" ||
          checkStatus?.status === "save") &&
        (action === "ME" || action === "ML")
      )
    ) {
      setLoadingStatus((prevState: IFieldStatus[]) =>
        prevState.map((item) =>
          item.field === field ? { ...item, status: status } : item
        )
      );
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setInputValues({ ...inputValues, [name]: value });
  };

  const handleSelectedLocation = async (place: IdirAddrPlaceDetails) => {
    setLocationLatLong({
      ...locationLatLong,
      latitude: place?.geometry?.location?.lat(),
      longitude: place?.geometry?.location?.lng(),
    });
    const streetNumber = getAddressComponent(place, "street_number")
      ? `${getAddressComponent(place, "street_number")}, `
      : "";
    const updateValues: ISTCustAddInfo = {
      customer_street: `${streetNumber}${getAddressComponent(place, "route")}`,
      customer_street2: "",
      customer_city: getAddressComponent(place, "locality"),
      customer_state: getAddressComponent(place, "administrative_area_level_1"),
      customer_zip: getAddressComponent(place, "postal_code"),
    };
    setInputValues(updateValues);
    await updateAddressInfo(updateValues);
  };

  const handleInputBlur = async () => {
    if (viewUnit) {
      return false;
    }
    const updateValues: ISTCustAddInfo = {
      ...inputValues,
    };
    delete updateValues.user_id;
    await updateAddressInfo(updateValues);
  };

  async function updateAddressInfo(updateInfo: ISTCustAddInfo) {
    if (
      updateInfo &&
      updateInfo.customer_street?.trim() === customer_street?.trim() &&
      updateInfo.customer_street2?.trim() === customer_street2?.trim() &&
      updateInfo.customer_city?.trim() === customer_city?.trim() &&
      updateInfo.customer_state?.trim() === customer_state?.trim() &&
      updateInfo.customer_zip?.trim() === customer_zip?.trim()
    ) {
      return;
    }
    setLoading(true);
    handleChangeFieldStatus({
      field: "address_info",
      status: "loading",
      action: "API",
    });
    const updateRes = (await updateSTCustomerApi({
      service_ticket_id: params?.id,
      ...updateInfo,
    })) as IServiceTicketDetailApiRes;

    if (updateRes?.success) {
      setViewUnit(false);

      handleChangeFieldStatus({
        field: "address_info",
        status: "success",
        action: "API",
      });
      dispatch(fetchServiceTicketCustomerDetails({ id: params?.id || "" }));
    } else {
      setViewUnit(false);

      handleChangeFieldStatus({
        field: "address_info",
        status: "error",
        action: "API",
      });
      setInputValues((prev: ISTCustAddInfo) => ({
        ...prev,
        customer_street: customer_street ?? "",
        customer_street2: customer_street2 ?? "",
        customer_city: customer_city ?? "",
        customer_state: customer_state ?? "",
        customer_zip: customer_zip ?? "",
      }));
      notification.error({
        description: updateRes?.message,
      });
    }
    setLoading(false);
    delay(() => {
      handleChangeFieldStatus({
        field: "address_info",
        status: "button",
        action: "API",
      });
    }, 3000);
  }
  const addressInfoLoading = getStatusForField(loadingStatus, "address_info");

  return (
    <InlineField
      label={_t("Address")}
      labelPlacement="left"
      field={
        <ul
          className={`grid items-start w-full ${
            viewUnit ? "" : "xl:grid-cols-2 gap-2"
          }`}
        >
          <li
            className={`w-full gap-1 flex justify-between p-1.5 pt-2 ${
              isReadOnly
                ? "cursor-default sm:bg-transparent bg-[#f4f5f6]"
                : "hover:bg-[#f4f5f6] min-[768px]:bg-transparent bg-[#f4f5f6] cursor-pointer"
            } ${viewUnit ? "hidden" : ""}`}
            onClick={() => {
              if (isReadOnly) {
                return false;
              }
              toggleAddressInfo();
            }}
            onMouseEnter={() => {
              handleChangeFieldStatus({
                field: "address_info",
                status: "edit",
                action: "ME",
              });
            }}
            onMouseLeave={() => {
              handleChangeFieldStatus({
                field: "address_info",
                status: "button",
                action: "ML",
              });
            }}
          >
            <ul className="w-full">
              {isEmpty(inputValues?.customer_street) &&
              isEmpty(inputValues?.customer_street2) &&
              isEmpty(inputValues?.customer_city) &&
              isEmpty(inputValues?.customer_state) &&
              isEmpty(inputValues.customer_zip) &&
              !loadingAddress ? (
                "-"
              ) : (
                <>
                  <li className="text-primary-900 text-sm">
                    {inputValues?.customer_street}
                    {(hasCustAddress1 && hasCustAddress2) ||
                    (hasCustAddress1 &&
                      (hasCustCity || hasCustState || hasCustZip))
                      ? ", "
                      : ""}
                  </li>
                  <li className="text-primary-900 text-sm">
                    {inputValues?.customer_street2}
                    {hasCustAddress2 &&
                    (hasCustCity || hasCustState || hasCustZip)
                      ? ", "
                      : ""}
                  </li>
                  <li>
                    <Typography className="text-primary-900 text-sm">
                      {inputValues?.customer_city}
                    </Typography>
                    <Typography className="text-primary-900 text-sm">
                      {hasCustCity && hasCustState
                        ? `, ${inputValues?.customer_state}`
                        : inputValues?.customer_state}
                      {""}
                    </Typography>
                    <Typography className="text-primary-900 text-sm">
                      {hasCustZip && (hasCustState || hasCustCity)
                        ? `, ${inputValues?.customer_zip}`
                        : inputValues?.customer_zip}
                    </Typography>
                  </li>
                </>
              )}
            </ul>

            {loadingAddress &&
              !(
                isEmpty(inputValues?.customer_street) &&
                isEmpty(inputValues?.customer_street2) &&
                isEmpty(inputValues?.customer_city) &&
                isEmpty(inputValues?.customer_state) &&
                isEmpty(inputValues.customer_zip)
              ) &&
              addressInfoLoading !== "success" && (
                <Spin
                  indicator={
                    <FontAwesomeIcon
                      className={twMerge(
                        `fa-spin text-primary-900 field-loader dark:text-white/90 w-5 h-5`
                      )}
                      icon="fa-duotone fa-solid fa-spinner-third"
                    />
                  }
                />
              )}

            {!isReadOnly && <FieldStatus status={addressInfoLoading} />}
          </li>
          <li className="w-full grid 2xl:grid-cols-2 gap-2">
            <GoogleMap
              ref={addressInfoRef}
              cssStyle={{ height: "150px" }}
              addressInfo={inputValues}
              mapAddress={{
                address1: addressInfo.customer_street || "",
                address2: addressInfo.customer_street2 || "",
                city: addressInfo.customer_city || "",
                state: addressInfo.customer_state || "",
                zip: addressInfo.customer_zip || "",
              }}
              handleInputChange={handleInputChange}
              handleSelectedLocation={handleSelectedLocation}
              isEditable={viewUnit}
              handleInputBlur={handleInputBlur}
              title={[
                inputValues?.customer_street,
                inputValues?.customer_street2,
                inputValues?.customer_city,
                inputValues?.customer_state,
                inputValues.customer_zip,
              ]
                .filter((value) => !!value)
                .join(", ")}
            />
          </li>
        </ul>
      }
    />
  );
};

export default AddressInformation;
