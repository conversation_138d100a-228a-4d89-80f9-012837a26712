import { useEffect, useMemo, useReducer, useState } from "react";
import isEmpty from "lodash/isEmpty";
import delay from "lodash/delay";
import { FormikValues, useFormik } from "formik";
// Hook
import { useTranslation } from "~/hook";
// Atoms
import { Drawer } from "~/shared/components/atoms/drawer";
import { Header } from "~/shared/components/atoms/header";
import { Link } from "~/shared/components/atoms/link";
import { Spin } from "~/shared/components/atoms/spin";
import { Tooltip } from "~/shared/components/atoms/tooltip";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// molecules
import { InputField } from "~/shared/components/molecules/inputField";
import { InputPhoneNumber } from "~/shared/components/molecules/inputPhoneNumber";
import { SidebarCardBorder } from "~/shared/components/molecules/sidebarCardBorder";
import { GoogleAutoCompleteInput } from "~/shared/components/molecules/googleAutoCompleteInput";
import { CloseButton } from "~/shared/components/molecules/closeButton";
import { PrimaryButton } from "~/shared/components/molecules/primaryButton";
import { CheckboxGroupList } from "~/shared/components/molecules/checkboxGroupList";
import { DatePickerField } from "~/shared/components/molecules/datePickerField";
import { SelectField } from "~/shared/components/molecules/selectField";
import { ErrorMessage } from "~/shared/components/molecules/errorMessage";
import { TextAreaField } from "~/shared/components/molecules/textAreaField";
import { ConfirmModal } from "~/shared/components/molecules/confirmModal";
import { InlineField } from "~/shared/components/molecules/inlineField";
import { RadioGroupList } from "~/shared/components/molecules/radioGroupList";
import { CustomFieldSkeleton } from "~/shared/components/molecules/customFieldSkeleton";
// Other
import { directoryTypeKey } from "~/constants/directory";
import { getCustomFieldAccess } from "~/shared/utils/helper/getCustomFieldAccess";
import {
  validateEmail,
  onKeyDownNumber,
  filterOptionBySubstring,
  onKeyDownDigit,
  sanitizeAndTruncate,
  validatePhoneNumber,
  validateSpecialAlphabeticInput,
  validateSpecialAlphaNumericInput,
} from "~/shared/utils/helper/common";
import {
  handleLocation,
  getAddressComponent,
} from "~/shared/utils/helper/locationAddress";
import { keyDownPositiveNumber } from "~/shared/utils/helper/numberFormat";
import { displayDateFormat } from "~/shared/utils/helper/defaultDateFormat";
import { getPhoneFormat } from "~/shared/utils/helper/defaultPhoneFormat";
import { getValuableObj } from "~/shared/utils/helper/getValuebleObj";
import { useDirectoryKeyValue } from "~/shared/hooks/useCustomField/useDirectoryKeyValue";
import { useSideBarCustomField } from "~/shared/hooks/useCustomField/useSidebarCustomField";
import { addDirectoryAPI } from "~/modules/people/directory/redux/action/driDashAction";
// zustand
import { getGConfig, getGSettings } from "~/zustand";
import * as Yup from "yup";
import {
  escapeHtmlEntities,
  replaceDOMParams,
  sanitizeString,
} from "~/helpers/helper";
import { formatCustomFieldForRequest } from "~/shared/utils/helper/customFieldSidebarFormat";
import { addCustomData } from "~/redux/action/customDataAction";
import { customDataTypesByKey } from "~/utils/constasnts";
import {
  formAddDirectorySchema,
  initialLatLong,
  stateValidation,
} from "~/modules/people/directory/components/sidebar/utils";
import {
  addItemObject,
  dirTypeKeyById,
  dirTypeKeys,
} from "~/modules/people/directory/utils/constasnts";
import { getCustomDataList } from "./actions/directoryAction";
import { initialState, reducer } from "./utils/reducer";
import { getGlobalAppSettings } from "~/zustand/global/settings/slice";
import { getCurrentMenuModule } from "~/zustand/global/config/slice";
import { onEnterSelectSearchValue } from "../selectField/units";
import OnlyRequiredCustomFields from "../../organisms/OnlyRequiredCustomFields/OnlyRequiredCustomFields";

const AddMultiSelectDir = ({
  addDirectory,
  setAddDirectory,
  contactData,
  callOnAddComplete,
  showCustomField,
}: IAddMultiSelectDirProps) => {
  const { _t } = useTranslation();

  let gSettings: IInitialGlobalData["settings"]["app_settings"] =
    getGlobalAppSettings();

  // this remove when demo apply on all existing modules
  const oldGSettings = getGSettings();
  if (!gSettings) {
    gSettings = oldGSettings;
  }

  const currentMenuModule: IInitialGlobalData["config"]["current_module"] =
    getCurrentMenuModule();
  const { module_id: current_module_id } = currentMenuModule || {};

  // this remove when demo apply on all existing modules
  let module_id = current_module_id;
  const { module_id: old_current_module_id }: GConfig = getGConfig();
  if (!module_id) {
    module_id = old_current_module_id;
  }

  // add initValue based on type and Required type and Validate Schema
  const { initValues, requiredForm, validateArray } = formAddDirectorySchema(
    contactData?.key
  );

  const { isNoAccessCustomField }: ICustomFieldAccess = getCustomFieldAccess();
  const [isSubmit, setIsSubmit] = useState<boolean>(false);

  const { directoryKeyValue, directory }: IDirectoryFormCustomField =
    useDirectoryKeyValue();

  const { componentList, loadingCustomField } = useSideBarCustomField(
    { directory, directoryKeyValue } as IDirectoryFormCustomField,
    {
      moduleId: 3, // Directory module_id, in order to get the custom fields from directory & based on userType
      userType: contactData.value,
    } as IRequestCustomFieldForSidebar
  );

  const staticValidationSchema = validateArray.reduce((acc, fieldName) => {
    acc[fieldName] = Yup.string().required("This field is required.");
    return acc;
  }, {} as Record<string, Yup.StringSchema>);

  const dynamicValidationSchema = componentList.reduce((acc, fieldName) => {
    if (fieldName.multiple || fieldName.type === "checkbox-group") {
      acc[fieldName.name] = Yup.array()
        .of(Yup.string().required("This field is required."))
        .min(1, "This field is required.")
        .required("This field is required.");
    } else {
      acc[fieldName.name] = Yup.string().required("This field is required.");
    }
    return acc;
  }, {} as Record<string, Yup.StringSchema | Yup.AnySchema>);

  const validationSchema =
    showCustomField &&
    componentList.length &&
    !isNoAccessCustomField &&
    !["customers", "leads"].includes(contactData?.key || "")
      ? Yup.object().shape({
          ...staticValidationSchema,
          custom_fields: Yup.object().shape(dynamicValidationSchema),
        })
      : Yup.object().shape({
          ...staticValidationSchema,
        });

  const initialFormValues =
    showCustomField && componentList.length && !isNoAccessCustomField
      ? {
          ...initValues,
          custom_fields: componentList.reduce((acc, item) => {
            acc[item.name] = item?.value ?? "";
            return acc;
          }, {} as ICustomFieldInitValue),
        }
      : initValues;
  const [initialValuesState, setInitialValuesState] =
    useState(initialFormValues);
  const [email, setEmail] = useState<string>();
  const [emailError, setEmailError] = useState<string>();
  const [state, setState] = useState<string>("");
  const [stateError, setStateError] = useState<string>("");
  const [stage, setStage] = useState<string>("lead_stage_received");
  const [stageError, setStageError] = useState<string>("");

  const [locationLatLong, setLocationLatLong] =
    useState<ILatLongItf>(initialLatLong);
  // submit
  const handleSubmit = async ({ setSubmitting }: FormikValues) => {
    let isValid = true;
    const isEmailValidate = validateEmail(email || "");
    if (!isEmailValidate && !!email) {
      setEmailError(`Enter valid email address.`);
    } else {
      setEmailError(``);
    }
    let isEmailValid = !!email ? (!!emailError ? false : true) : true;
    const isStateValidate = stateValidation(state || "");
    setStateError(isStateValidate);

    if ([directoryTypeKey.employee].includes(contactData?.key || "")) {
      if (
        !formik.values.first_name?.trim() ||
        !formik.values.last_name?.trim()
      ) {
        formik.setErrors({
          first_name: !!formik.values.first_name?.trim()
            ? ""
            : "This field is required.",
          last_name: !!formik.values.last_name?.trim()
            ? ""
            : "This field is required.",
        });
        formik.setValues({
          ...formik.values,
          first_name: formik.values.first_name?.trim(),
          last_name: formik.values.last_name?.trim(),
        });
        isValid = false;
      }
    } else if (
      [directoryTypeKey.contractor, directoryTypeKey.vendor].includes(
        contactData?.key || ""
      )
    ) {
      if (!formik.values.company_name?.trim()) {
        formik.setErrors({
          company_name: !!formik.values.company_name?.trim()
            ? ""
            : "This field is required.",
        });
        formik.setValues({
          ...formik.values,
          company_name: formik.values.company_name?.trim(),
        });
        isValid = false;
      }
    } else if (
      [directoryTypeKey.misc_contact].includes(contactData?.key || "")
    ) {
      if (
        !formik.values.company_name?.trim() ||
        !formik.values.first_name?.trim() ||
        !formik.values.last_name?.trim()
      ) {
        formik.setErrors({
          company_name: !!formik.values.company_name?.trim()
            ? ""
            : "This field is required.",
          first_name: !!formik.values.first_name?.trim()
            ? ""
            : "This field is required.",
          last_name: !!formik.values.last_name?.trim()
            ? ""
            : "This field is required.",
        });
        formik.setValues({
          ...formik.values,
          company_name: formik.values.company_name?.trim(),
          first_name: formik.values.first_name?.trim(),
          last_name: formik.values.last_name?.trim(),
        });
        isValid = false;
      }
    } else if (
      [directoryTypeKey.customer, directoryTypeKey.lead].includes(
        contactData?.key || ""
      )
    ) {
      if (
        [directoryTypeKey.lead].includes(contactData?.key || "") &&
        !stage?.toString()?.trim()
      ) {
        setStageError("This field is required.");
        isValid = false;
      }
      // if (
      //   !formik.values.company_name?.trim() &&
      //   (!formik.values.first_name?.trim() || !formik.values.last_name?.trim())
      // ) {
      //   notification.error({
      //     description: "First/Last Name or Company Name must be defined.",
      //   });

      //   formik.setValues({
      //     ...formik.values,
      //     company_name: formik.values.company_name?.trim(),
      //     first_name: formik.values.first_name?.trim(),
      //     last_name: formik.values.last_name?.trim(),
      //   });
      //   isValid = false;
      // }
    }
    let isCustomFieldValid = true;
    if (componentList.length && showCustomField) {
      for (let index = 0; index < componentList.length; index++) {
        const value =
          formik?.values?.custom_fields?.[componentList[index].name];
        const multiple = componentList[index].multiple;
        const typeComponent = componentList[index].type;
        if (multiple || typeComponent === "checkbox-group") {
          if (!value?.length) {
            isCustomFieldValid = false;
            break;
          }
        } else if (!value) {
          isCustomFieldValid = false;
          break;
        }
      }
    }

    // changes by jaydeep bhai 28-08-2024
    if (
      [directoryTypeKey.customer, directoryTypeKey.lead].includes(
        contactData?.key || ""
      ) &&
      isValid &&
      isCustomFieldValid
    ) {
      if (
        !formik.values.company_name?.trim() &&
        (!formik.values.first_name?.trim() || !formik.values.last_name?.trim())
      ) {
        notification.error({
          description: "First/Last Name or Company Name must be defined.",
        });

        formik.setValues({
          ...formik.values,
          company_name: formik.values.company_name?.trim(),
          first_name: formik.values.first_name?.trim(),
          last_name: formik.values.last_name?.trim(),
        });
        isValid = false;
      }
    }

    if (
      !formik.isValid ||
      !isValid ||
      !isEmailValid ||
      !isCustomFieldValid ||
      !!stateError ||
      !!stageError
    )
      return;

    const formData = {
      ...formik.values,
      company_name: !!formik.values.company_name
        ? HTMLEntities?.encode(formik.values.company_name)
        : undefined,
      first_name: HTMLEntities?.encode(formik?.values?.first_name || ""),
      last_name: HTMLEntities?.encode(formik?.values?.last_name || ""),
      custom_fields:
        formik.values.custom_fields && showCustomField && !isNoAccessCustomField
          ? formatCustomFieldForRequest(
              formik.values.custom_fields,
              componentList,
              gSettings?.date_format || "" // this change when demo apply on all existing modules
            ).custom_fields
          : undefined,
      isPopupCall: isNoAccessCustomField || !showCustomField,
      module_id: module_id,
      email,
      state,
      type: contactData.value,
      latitude: locationLatLong?.latitude,
      longitude: locationLatLong?.longitude,
      stage: contactData.key === "leads" ? stage : undefined,
    };

    try {
      const responseApi = await addDirectoryAPI(getValuableObj(formData));

      if (responseApi?.success) {
        const dirType = Object.entries(dirTypeKeyById).find(
          ([_, val]) => val == contactData.value
        )?.[0];
        if (!!responseApi?.data?.user_id) {
          // setAddDirectory(!addDirectory);
          if (callOnAddComplete) {
            callOnAddComplete(responseApi);
          }
        } else {
          notification.error({
            description: responseApi?.message || "Something went wrong!",
          });
        }
      } else {
        notification.error({
          description: responseApi?.message || "",
        });
      }
    } catch (error) {
      notification.error({
        description: (error as Error).message || "",
      });
    } finally {
      setSubmitting(false);
    }
  };

  const onBlurEmail = async (e: React.FocusEvent<HTMLInputElement>) => {
    if (e.target.value !== email) {
      setEmail(e.target.value);
    }
    const isValid = validateEmail(email || "");
    if (!isValid && !!email) {
      setEmailError(`Enter valid email address.`);
    } else {
      setEmailError(``);
    }
  };

  const onBlurState = async (e: React.FocusEvent<HTMLInputElement>) => {
    if (e.target.value !== state) {
      setState(e.target.value);
    }
    const isStateValidate = stateValidation(state || "");
    setStateError(isStateValidate);
  };

  // add formik
  const formik = useFormik({
    initialValues: initialFormValues,
    validationSchema: validationSchema,
    onSubmit: handleSubmit,
  });

  const handleSelectedLocation = (
    googleMapsPlaces: google.maps.places.PlaceResult | null
  ) => {
    const place = googleMapsPlaces as unknown as PlaceDetails;
    setLocationLatLong({
      ...locationLatLong,
      latitude: place?.geometry?.location?.lat(),
      longitude: place?.geometry?.location?.lng(),
    });

    const streetNumber = getAddressComponent(place, "street_number")
      ? `${getAddressComponent(place, "street_number")} `
      : "";
    const address1 = `${streetNumber}${getAddressComponent(place, "route")}`;
    const address2 = "";
    const city =
      getAddressComponent(place, "locality") ||
      getAddressComponent(place, "sublocality");
    const getState = getAddressComponent(place, "administrative_area_level_1");
    const zip = getAddressComponent(place, "postal_code");

    formik.setValues({
      ...formik.values,
      city,
      zip,
      address1,
      address2,
    });
    setState(getState);
    const isStateValidate = stateValidation(getState || "");
    setStateError(isStateValidate);
  };
  const handleEnterKeyPress = (
    event: React.KeyboardEvent<HTMLInputElement>
  ) => {
    event.preventDefault();
  };
  const selectedIcon = useMemo(() => {
    let selectedIconComp = (
      <FontAwesomeIcon
        className="w-4 h-4 text-primary-900"
        icon="fa-regular fa-user-vneck"
      />
    );
    if (contactData.key === dirTypeKeys.contractor) {
      selectedIconComp = (
        <FontAwesomeIcon
          className="w-4 h-4 text-primary-900"
          icon="fa-regular fa-user-helmet-safety"
        />
      );
    } else if (contactData.key === dirTypeKeys.customer) {
      selectedIconComp = (
        <FontAwesomeIcon
          className="w-4 h-4 text-primary-900"
          icon="fa-regular fa-user"
        />
      );
    } else if (contactData.key === dirTypeKeys.lead) {
      selectedIconComp = (
        <FontAwesomeIcon
          className="w-4 h-4 text-primary-900"
          icon="fa-regular fa-child"
        />
      );
    } else if (contactData.key === dirTypeKeys.misc_contact) {
      selectedIconComp = (
        <FontAwesomeIcon
          className="w-4 h-4 text-primary-900"
          icon="fa-regular fa-address-book"
        />
      );
    } else if (contactData.key === dirTypeKeys.vendor) {
      selectedIconComp = (
        <FontAwesomeIcon
          className="w-4 h-4 text-primary-900"
          icon="fa-regular fa-person-digging"
        />
      );
    }

    return selectedIconComp;
  }, [contactData]);

  //Add stage using reducer

  const [directoryData, dispatch] = useReducer(reducer, initialState);

  useEffect(() => {
    const getData = async () => {
      dispatch({ type: "SET_CUSTOM_DATA_PENDING" });
      const resData = (await getCustomDataList({
        types: [customDataTypesByKey?.leadStageKeyID],
        moduleId: module_id,
      })) as IAMDGetCustomeDataRes;

      dispatch({ type: "SET_CUSTOM_DATA_LIST", payload: resData });
    };
    if (contactData?.key === directoryTypeKey.lead) {
      getData();
    }
  }, [contactData]);

  const { customDataList } = directoryData;

  // Add stage
  const [customDataAdd, setCustomDataAdd] = useState<ICommonCustomDataFrm>({});
  const [isAddingCustomData, setIsAddingCustomData] = useState<boolean>(false);
  const [isConfirmDialogOpen, setIsConfirmDialogOpen] =
    useState<boolean>(false);
  // this is will NF once testing done it will be merge on dev
  // const [isConfirmDrawerOpen, setIsConfirmDrawerOpen] =
  //   useState<boolean>(false);
  // const { customDataList }: ICustomDataInitialState = useAppSelector(
  //   (state) => state.customData
  // );
  const categCusDataItems: ICategCusDataItems = useMemo(() => {
    const accumulatedData = customDataList.data.reduce<ICategCusDataItems>(
      (acc, item) => {
        acc?.stageList?.push({
          label: replaceDOMParams(sanitizeString(item?.name)),
          value: item?.item_id,
        });
        return acc;
      },
      { stageList: [] }
    );
    return accumulatedData;
  }, [customDataList]);

  const sortedStageList = useMemo(() => {
    if (!categCusDataItems?.stageList?.length) return [];

    const seenIds = new Set<string>();
    let newDataList = categCusDataItems.stageList
      .sort((a, b) => {
        const orderA = a.sort_order === null ? Infinity : Number(a.sort_order);
        const orderB = b.sort_order === null ? Infinity : Number(b.sort_order);
        return orderA - orderB;
      })
      .filter((item) => {
        if (seenIds.has(item.value)) {
          return false; // Skip duplicates
        }
        seenIds.add(item.value);
        return true; // Include unique items
      });

    const seleStage = newDataList?.find((i) => i.value == stage);
    if (stage && !seleStage?.value) {
      newDataList = [
        ...newDataList,
        {
          label: `Received (Archived)`,
          value: stage,
        },
      ];
    }

    return newDataList;
  }, [JSON.stringify(categCusDataItems?.stageList), stage]);

  const handlekeyDown = (
    event: React.KeyboardEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    if (event.key === "Enter") {
      const value = event?.currentTarget?.value?.trim();
      const newType = onEnterSelectSearchValue(
        event,
        categCusDataItems?.stageList || []
      );
      if (newType) {
        setCustomDataAdd({
          itemType: customDataTypesByKey.leadStageKeyID,
          name: escapeHtmlEntities(event?.currentTarget?.value),
        });
        setIsConfirmDialogOpen(true);
      } else if (value) {
        notification.error({
          description: "Records already exist, no new records were added.",
        });
      }
    }
  };

  const handleAddCustomData = async () => {
    if (!isAddingCustomData && customDataAdd?.name) {
      setIsAddingCustomData(true);

      const cDataRes = (await addCustomData({
        itemType: customDataAdd?.itemType,
        name: customDataAdd?.name,
      })) as ICustomDataAddUpRes;

      if (cDataRes?.success) {
        dispatch({ type: "ADD_CUSTOM_DATA", payload: cDataRes?.data });
        delay(() => {
          setStage(cDataRes?.data?.item_id);
          setStageError("");
        }, 500);
        setIsConfirmDialogOpen(false);
      } else {
        notification.error({ description: cDataRes.message });
      }
      setIsAddingCustomData(false);
    }
  };

  const closeConfirmationModal = () => {
    setIsConfirmDialogOpen(false);
  };

  const handleChangeStage = (val: string | string[]) => {
    if (!!val) {
      setStage(val as string);
      setStageError("");
    } else {
      setStage("");
      if (isSubmit) setStageError("This field is required.");
    }
  };

  const handleChangeState = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = e.target;
    setState(value);
    const isStateValidate = stateValidation(state || "");
    setStateError(isStateValidate);
  };

  // this is will NF once testing done it will be merge on dev
  // const isFormModified = useMemo(() => {
  //   return JSON.stringify(formik.values) !== JSON.stringify(initialValuesState);
  // }, [formik.values, initialValuesState]);

  // const closeDrawerConfirmationModal = () => {
  //   addDirectory;
  //   setIsConfirmDrawerOpen(false);
  // };

  // const handleAlertBox = async () => {
  //   setIsConfirmDrawerOpen(false);
  //   setAddDirectory(false);
  // };

  // const handleCloseDrawer = () => {
  //   if (!isFormModified) {
  //     setAddDirectory(false);
  //     setIsSubmit(false);
  //   } else {
  //     setIsConfirmDrawerOpen(true);
  //   }
  // };

  return (
    <Drawer
      open={addDirectory}
      rootClassName="drawer-open"
      width={718}
      push={false}
      classNames={{
        body: "!p-0 !overflow-hidden ",
      }}
      title={
        <div className="flex items-center">
          <div className="flex items-center justify-center rounded-full mr-2.5 w-[30px] h-[30px] bg-[#e4ecf68c] dark:bg-dark-500 text-primary-900 dark:text-white/90">
            {selectedIcon}
          </div>
          <Header
            level={5}
            className="md:!text-[17px] !text-[15px] !text-primary-900 !mb-0 font-semibold"
          >
            {_t(`Add ${contactData?.labelSingular}`)}
          </Header>
        </div>
      }
      closeIcon={
        <CloseButton
          isLoading={formik?.isSubmitting}
          onClick={() => {
            setAddDirectory(false);
            setIsSubmit(false);
          }}
          // this is will NF once testing done it will be merge on dev
          // onClick={() => handleCloseDrawer()}
        />
      }
    >
      {!customDataList.loading ? (
        <form className="py-4" onSubmit={formik.handleSubmit} noValidate>
          <div className="sidebar-body overflow-y-auto h-[calc(100dvh-132px)] px-4">
            <div className="grid gap-4">
              <SidebarCardBorder addGap={true}>
                {contactData?.key !== directoryTypeKey?.employee && (
                  <div className="w-full">
                    <InputField
                      label={_t("Company")}
                      name="company_name"
                      labelPlacement="top"
                      maxLength={150}
                      value={formik?.values?.company_name}
                      onChange={(e) => {
                        const { value } = e.target;
                        if (validateSpecialAlphaNumericInput(value, 150)) {
                          formik.handleChange(e);
                        }
                      }}
                      isRequired={requiredForm?.isRequiredCompanyName}
                      errorMessage={
                        formik?.touched?.company_name &&
                        ![
                          directoryTypeKey?.customer,
                          directoryTypeKey?.lead,
                        ]?.includes(contactData?.key || "")
                          ? formik?.errors?.company_name
                          : ""
                      }
                      onPressEnter={handleEnterKeyPress}
                    />
                  </div>
                )}
                <div className="grid md:grid-cols-2 md:gap-5 gap-5">
                  <div className="w-full">
                    <InputField
                      label={_t("First Name")}
                      name="first_name"
                      labelPlacement="top"
                      value={formik?.values?.first_name}
                      onChange={(e) => {
                        const { value } = e.target;
                        if (validateSpecialAlphabeticInput(value, 20)) {
                          formik.handleChange(e);
                        }
                      }}
                      isRequired={requiredForm?.isRequiredFirstName}
                      errorMessage={
                        formik?.touched?.first_name &&
                        ![
                          directoryTypeKey?.customer,
                          directoryTypeKey?.lead,
                        ]?.includes(contactData?.key || "")
                          ? formik?.errors?.first_name
                          : ""
                      }
                      onPressEnter={handleEnterKeyPress}
                    />
                  </div>
                  <div className="w-full">
                    <InputField
                      label={_t("Last Name")}
                      name="last_name"
                      labelPlacement="top"
                      value={formik?.values?.last_name}
                      onChange={(e) => {
                        const { value } = e.target;
                        if (validateSpecialAlphabeticInput(value, 20)) {
                          formik.handleChange(e);
                        }
                      }}
                      isRequired={requiredForm?.isRequiredLastName}
                      errorMessage={
                        formik?.touched?.last_name &&
                        ![
                          directoryTypeKey?.customer,
                          directoryTypeKey?.lead,
                        ]?.includes(contactData?.key || "")
                          ? formik?.errors?.last_name
                          : ""
                      }
                      onPressEnter={handleEnterKeyPress}
                    />
                  </div>
                </div>

                {contactData?.key === directoryTypeKey.lead && (
                  <div className="w-full">
                    <SelectField
                      label={_t("Stage")}
                      value={
                        stage
                          ? sortedStageList?.find((item) => {
                              return (
                                stage?.toString() === item?.value?.toString()
                              );
                            })
                          : []
                      }
                      name="stage"
                      labelPlacement="top"
                      isRequired={true}
                      iconView={true}
                      showSearch
                      options={sortedStageList}
                      allowClear={true}
                      filterOption={(input, option) =>
                        filterOptionBySubstring(input, option?.label as string)
                      }
                      onChange={handleChangeStage}
                      addItem={addItemObject}
                      onInputKeyDown={(e) => handlekeyDown(e)}
                      errorMessage={!!stageError ? stageError : ""}
                    />
                  </div>
                )}
              </SidebarCardBorder>

              <SidebarCardBorder addGap={true}>
                <div className="grid md:grid-cols-2 md:gap-5 gap-5">
                  <div className="grid grid-cols-3 gap-5">
                    <div className="w-full col-span-2">
                      <InputPhoneNumber
                        mask={getPhoneFormat(gSettings?.phone_format || "")}
                        label={_t("Phone")}
                        labelPlacement="top"
                        name="phone"
                        value={formik?.values?.phone}
                        onChange={formik.handleChange}
                        onBlur={(e: React.FocusEvent<HTMLInputElement>) => {
                          if (isEmpty(gSettings?.phone_format)) {
                            formik?.setFieldValue(
                              "phone",
                              sanitizeAndTruncate(e.currentTarget.value, 15)
                            );
                          }
                          if (
                            !validatePhoneNumber(
                              e?.target?.value,
                              gSettings?.phone_format || ""
                            )
                          ) {
                            formik.setFieldValue("phone", "");
                            return false;
                          }
                        }}
                        onKeyDown={(
                          event: React.KeyboardEvent<HTMLInputElement>
                        ) => {
                          if (event.key === "Enter") {
                            event.preventDefault();
                          }
                          if (event.metaKey || event.ctrlKey) {
                            return;
                          }
                          if (gSettings?.phone_format !== "") {
                            return false;
                          }
                          return onKeyDownDigit(event, {
                            integerDigits: 14,
                          });
                        }}
                      />
                    </div>
                    <div className="w-full">
                      <InputField
                        name="phone_ext"
                        label={_t("Ext.")}
                        labelPlacement="top"
                        value={formik?.values?.phone_ext}
                        onChange={formik.handleChange}
                        maxLength={5}
                        onKeyDown={keyDownPositiveNumber}
                        onPressEnter={handleEnterKeyPress}
                        onBlur={(e: React.FocusEvent<HTMLInputElement>) => {
                          formik?.setFieldValue(
                            "phone_ext",
                            sanitizeAndTruncate(e.currentTarget.value, 5)
                          );
                        }}
                      />
                    </div>
                  </div>
                  <div className="grid grid-cols-3 gap-5">
                    <div className="w-full col-span-2">
                      <InputPhoneNumber
                        mask={getPhoneFormat(gSettings?.phone_format || "")}
                        label={_t("Phone 2")}
                        labelPlacement="top"
                        name="phone2"
                        value={formik?.values?.phone2}
                        onChange={formik.handleChange}
                        onBlur={(e: React.FocusEvent<HTMLInputElement>) => {
                          if (isEmpty(gSettings?.phone_format)) {
                            formik?.setFieldValue(
                              "phone2",
                              sanitizeAndTruncate(e.currentTarget.value, 15)
                            );
                          }
                          if (
                            !validatePhoneNumber(
                              e?.target?.value,
                              gSettings?.phone_format || ""
                            )
                          ) {
                            formik.setFieldValue("phone2", "");
                            return false;
                          }
                        }}
                        onKeyDown={(
                          event: React.KeyboardEvent<HTMLInputElement>
                        ) => {
                          if (event.key === "Enter") {
                            event.preventDefault();
                          }
                          if (event.metaKey || event.ctrlKey) {
                            return;
                          }
                          if (gSettings?.phone_format !== "") {
                            return false;
                          }
                          return onKeyDownDigit(event, {
                            integerDigits: 14,
                          });
                        }}
                      />
                    </div>
                    <div className="w-full">
                      <InputField
                        name="phone_ext2"
                        label={_t("Ext.")}
                        labelPlacement="top"
                        value={formik?.values?.phone_ext2}
                        onChange={formik.handleChange}
                        maxLength={5}
                        onKeyDown={keyDownPositiveNumber}
                        onPressEnter={handleEnterKeyPress}
                        onBlur={(e: React.FocusEvent<HTMLInputElement>) => {
                          formik?.setFieldValue(
                            "phone_ext2",
                            sanitizeAndTruncate(e.currentTarget.value, 5)
                          );
                        }}
                      />
                    </div>
                  </div>
                </div>
                <div className="grid md:grid-cols-2 md:gap-5 gap-5">
                  <div className="w-full">
                    <InputPhoneNumber
                      mask={getPhoneFormat(gSettings?.phone_format || "")}
                      label={_t("Cell")}
                      labelPlacement="top"
                      name="cell"
                      value={formik?.values?.cell}
                      onChange={formik.handleChange}
                      onBlur={(e: React.FocusEvent<HTMLInputElement>) => {
                        if (isEmpty(gSettings?.phone_format)) {
                          formik?.setFieldValue(
                            "cell",
                            sanitizeAndTruncate(e.currentTarget.value, 15)
                          );
                        }
                        if (
                          !validatePhoneNumber(
                            e?.target?.value,
                            gSettings?.phone_format || ""
                          )
                        ) {
                          formik.setFieldValue("cell", "");
                          return false;
                        }
                      }}
                      onKeyDown={(
                        event: React.KeyboardEvent<HTMLInputElement>
                      ) => {
                        if (event.key === "Enter") {
                          event.preventDefault();
                        }
                        if (event.metaKey || event.ctrlKey) {
                          return;
                        }
                        if (gSettings?.phone_format !== "") {
                          return false;
                        }
                        return onKeyDownDigit(event, {
                          integerDigits: 14,
                        });
                      }}
                    />
                  </div>
                  <div className="w-full">
                    <InputField
                      label={_t("Email")}
                      labelPlacement="top"
                      addonAfterIcon={true}
                      name="email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      addonAfter={
                        <div className="flex items-center gap-2.5">
                          <FontAwesomeIcon
                            className="w-3.5 h-3.5 text-primary-900 dark:text-[#dcdcdd]"
                            icon="fa-regular fa-envelope"
                          />
                        </div>
                      }
                      errorMessage={!!emailError ? emailError : ""}
                      onBlur={onBlurEmail}
                      onPressEnter={handleEnterKeyPress}
                    />
                  </div>
                </div>
              </SidebarCardBorder>

              <SidebarCardBorder addGap={true}>
                <div className="w-full">
                  <GoogleAutoCompleteInput
                    label={_t("Street")}
                    labelPlacement="top"
                    onSelectValue={(
                      item: google.maps.places.PlaceResult | null
                    ) => handleSelectedLocation(item)}
                    onSetValue={(val) => {
                      formik.setFieldValue("address1", val.trim());
                    }}
                    name="address1"
                    value={formik?.values?.address1}
                    addonAfterIcon={true}
                    addonAfter={
                      <div className="flex items-center gap-2.5">
                        <Tooltip title={_t("Location")}>
                          <Link href={"#"} className="!leading-3" title={""}>
                            <FontAwesomeIcon
                              className="w-3.5 h-3.5 text-primary-900 dark:text-[#dcdcdd] cursor-pointer"
                              icon="fa-regular fa-location-dot"
                              onClick={() => handleLocation(locationLatLong)}
                            />
                          </Link>
                        </Tooltip>
                      </div>
                    }
                  />
                </div>
                <div className="w-full">
                  <InputField
                    label={_t("Street 2")}
                    name="address2"
                    labelPlacement="top"
                    value={formik?.values?.address2}
                    onChange={formik.handleChange}
                    onPressEnter={handleEnterKeyPress}
                  />
                </div>
                <div className="grid md:grid-cols-3 md:gap-5 gap-5">
                  <div className="w-full">
                    <InputField
                      label={_t("City")}
                      name="city"
                      labelPlacement="top"
                      value={formik?.values?.city}
                      onChange={formik.handleChange}
                      onPressEnter={handleEnterKeyPress}
                    />
                  </div>
                  <div className="w-full">
                    <InputField
                      label={_t("State")}
                      name="state"
                      maxLength={20}
                      labelPlacement="top"
                      value={state}
                      onChange={handleChangeState}
                      onBlur={onBlurState}
                      errorMessage={!!stateError ? stateError : ""}
                      onPressEnter={handleEnterKeyPress}
                    />
                  </div>
                  <div className="w-full">
                    <InputField
                      label={_t("Zip")}
                      name="zip"
                      labelPlacement="top"
                      value={formik?.values?.zip}
                      onChange={formik.handleChange}
                      maxLength={11}
                      onPressEnter={handleEnterKeyPress}
                    />
                  </div>
                </div>
              </SidebarCardBorder>

              {/* Required Custom Fields */}
              {showCustomField && (
                <OnlyRequiredCustomFields
                  componentList={componentList}
                  formik={formik}
                  isSubmit={isSubmit}
                  loadingCustomField={loadingCustomField}
                />
              )}
            </div>
          </div>
          <div className="sidebar-footer flex items-center justify-center w-full px-4 pt-4">
            <PrimaryButton
              htmlType="submit"
              disabled={formik?.isSubmitting || loadingCustomField}
              isLoading={formik?.isSubmitting}
              onClick={() => setIsSubmit(true)}
            />
          </div>
        </form>
      ) : (
        <Spin className="w-full h-[203px] flex items-center justify-center" />
      )}
      {isConfirmDialogOpen && (
        <ConfirmModal
          isOpen={isConfirmDialogOpen}
          modalIcon="fa-regular fa-clipboard-list-check"
          modaltitle={_t("Add Option To List")}
          description={_t(
            `This will add "${replaceDOMParams(
              sanitizeString(customDataAdd?.name || "")
            )}" to the list. Do you want to add it?`
          )}
          isLoading={isAddingCustomData}
          onCloseModal={closeConfirmationModal}
          onAccept={() => {
            handleAddCustomData();
          }}
          onDecline={closeConfirmationModal}
        />
      )}
      {/* this is will NF once testing done it will be merge on dev */}
      {/* {isConfirmDrawerOpen && (
        <ConfirmModal
          isOpen={isConfirmDrawerOpen}
          modalIcon="fa-regular fa-file-check"
          modaltitle={_t("Confirmation")}
          description={_t(
            `Do you really want to leave this page and lose your unsaved changes?`
          )}
          onCloseModal={closeDrawerConfirmationModal}
          onAccept={() => {
            handleAlertBox();
          }}
          onDecline={closeDrawerConfirmationModal}
        />
      )} */}
    </Drawer>
  );
};

export default AddMultiSelectDir;
