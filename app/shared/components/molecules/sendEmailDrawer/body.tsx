// Atom
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// Molecules
import { InfiniteScroll } from "../InfiniteScroll/InfiniteScroll";
import { LoadingItems } from "~/shared/components/molecules/loadingItems";
import { FieldLabel } from "~/shared/components/molecules/fieldLabel";
import { NoRecords } from "~/shared/components/molecules/noRecords";
import { PrimaryButton } from "~/shared/components/molecules/primaryButton";
// organisms
import { AttachmentCard } from "~/shared/components/organisms/attachmentCard";
// third-party
import { CFSearchInput } from "~/components/third-party/ant-design/cf-search-input";
import { CFTypography } from "~/components/third-party/ant-design/cf-typography";
import { CFButton } from "~/components/third-party/ant-design/cf-button";
import { CFCheckBox } from "~/components/third-party/ant-design/cf-checkbox";
import { CFInput } from "~/components/third-party/ant-design/cf-input";
import CFGoogleReCAPTCHA from "~/components/third-party/google-recaptcha/cf-google-recaptcha.client";
import CFQuillEditor from "~/components/third-party/quill-editor/cf-quill-editor.client";

import React, { useCallback, useEffect, useState } from "react";
import debounce from "lodash/debounce";
import isEmpty from "lodash/isEmpty";
import { defaultConfig } from "~/data";

import { getGConfig, getGSettings, useGModules } from "~/zustand";
import { CheckboxChangeEvent } from "antd/es/checkbox";

// helper
import { getApiDefaultParams, sanitizeString } from "~/helpers/helper";
// Hooks
import { useTranslation } from "~/hook";
import ListOfSeletedCustomers from "./component/ListOfSeletedCustomers";
import { changeFavAction } from "~/redux/action/changeFavEmailSection";
import GetSelectedCom from "./component/GetSelectedCom";
import { ACTIVE_FIELD_NAME } from "../selectCustomerDrawer/constants";
import { getUniqueIdDirData } from "./utils";
import { getGlobalUser } from "~/zustand/global/user/slice";
import { ConfirmModal } from "../confirmModal";
import { removeDuplicatesFile } from "~/shared/utils/helper/removeDuplicatesFile";

const CustomerSelectBody = ({
  loadMore,
  hasMore,
  isInfiniteScrollLoading,
  infiniteScrollHideLoadingComponent,
  selectedContact,
  dispatch,
  activeField,
  customer,
  singleSelecte,
  sendEmail,
  setSendEmail,
  emailApiCall,
  groupCheckBox,
  closeDrawer,
  isViewAttachment = true,
  isViewSaveThisToFileCheckbox = false,
  saveFileModuleName = "",
  emailData,
  setEmailData,
  sendCustomEmail,
  mailFormInitialState,
  isAddAllow,
  scrollRef,
  viewCapcha,
  customSubject,
  rightScrollRef,
  validationParams,
  setIsSelectedCaptcha,
  isSelectedCaptcha,
  isShowAddIcon,
  isCustomerRemovable,
}: ICustomerSelectBodyProps) => {
  let { _t } = useTranslation();
  const gConfig: GConfig = getGConfig();
  const { module_id, module_access, module_key }: GConfig = gConfig;
  const { date_format, image_resolution }: GSettings = getGSettings();
  const { checkModuleAccessByKey } = useGModules();
  const user: IInitialGlobalData["user"] = getGlobalUser();
  const [isConfirmDialogOpen, setIsConfirmDialogOpen] =
    useState<boolean>(false);
  const closeConfirmationModal = () => {
    setIsConfirmDialogOpen(false);
  };

  const [userHasNoEmail, setUserHasNoEmail] = useState<string>("");
  const [dataTempForm, setDataTempForm] = useState<
    ApiDefaultParams & SendEmailFormData
  >();
  const [errors, setErrors] = useState<Partial<SendEmailFormError>>({
    subjectError: "",
    selectedCaptchaError: "",
    externalEmailError: "",
  });
  const [confirmMailDialogOpen, setConfirmMailDialogOpen] =
    useState<boolean>(false);
  const [bottomSheetOpen, setBottomSheetOpen] = useState<boolean>(false);
  const [emailSendingMethod, setEmailSendingMethod] =
    useState<EmailSendingMethod>("group");
  const [emailInput, setEmailInput] = useState<string>("");
  const [sendEmailLoading, setSendEmailLoading] = useState(false);

  const isContactWithoutEmail = selectedContact
    .filter((ele) => !ele.email)
    .map((ele) => ele.display_name);
  const isEmailAvailable = selectedContact.some((ele) => ele.email);
  const closeSendMailSidebar = () => {
    mailFormInitialState && setEmailData?.(mailFormInitialState);
    dispatch({
      type: "RESET_ALL_DATA",
    });
    setErrors({
      subjectError: "",
      selectedCaptchaError: "",
    });
  };

  useEffect(() => {
    if (validationParams.save_a_copy_of_sent_pdf === 1) {
      setEmailData?.((prev) => ({
        ...prev,
        saveThisToFile: true,
      }));
    } else {
      setEmailData?.((prev) => ({
        ...prev,
        saveThisToFile: false,
      }));
    }
  }, []);

  const handleEmailPopup = () => {
    if (dataTempForm) {
      if (!isEmpty(emailData?.subject) && isSelectedCaptcha) {
        closeConfirmationModal();
        dispatch({
          type: "RESET_ALL_DATA",
        });
        emailApiCall?.(
          dataTempForm,
          closeSendMailSidebar,
          emailData?.ccMailCopy || false
        );
        if (emailData?.generatedCaptcha) {
          viewCapcha.current = false;
        }
        setSendEmail?.(false);
        mailFormInitialState && setEmailData?.(mailFormInitialState);
        closeDrawer();
        setDataTempForm(undefined);
        setUserHasNoEmail("");
      } else {
        setDataTempForm(undefined);
        setUserHasNoEmail("");
        closeConfirmationModal();
      }
    }
  };

  const onSubmit = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();
    const isEveryOneHaveEmail = selectedContact.every((ele) => ele.email);

    if (!isEmpty(emailData?.subject?.trim()) && isSelectedCaptcha) {
      if (!isEveryOneHaveEmail) {
        setConfirmMailDialogOpen(true);
      } else {
        if (selectedContact?.length) {
          emailSend();
        } else {
          notification.error({
            description: "Please add/select email to send.",
          });
          setIsSelectedCaptcha(false);
          setSendEmail?.(false);
          return false;
        }
      }
    } else {
      setErrors({
        subjectError: !emailData?.subject?.trim()
          ? "This field is required."
          : "",
        selectedCaptchaError: !isSelectedCaptcha
          ? "Please confirm that you are not a robot."
          : "",
      });
    }
  };
  const emailSend = async () => {
    const selectedEmails: string[] = [];
    const selected_usr_array: {
      user_id: number | undefined;
      user_type: number | undefined;
      user_type_key: string | undefined;
      user_email: string;
      contact_id?: number;
    }[] = [];
    const contactEmail = selectedContact.filter((ele) => ele.email);
    if (contactEmail?.length) {
      contactEmail?.forEach((employee: Partial<CustomerSelectedData>) => {
        if (employee?.email) {
          selectedEmails.push(employee?.email);
          selected_usr_array.push({
            user_id: employee?.user_id,
            user_type: employee?.type,
            user_type_key: employee.type_key,
            user_email: employee?.email,
            contact_id: employee?.contact_id,
          });
        }
      });
    }

    const tempFormData = getApiDefaultParams<SendEmailFormData>({
      op: "send_custom_email",
      user,
      otherParams: {
        custom_subject: HTMLEntities.encode(emailData?.subject || ""),
        send_me_copy: emailData?.ccMailCopy ?? false,
        emails: selectedEmails,
        selected_user_arr: selected_usr_array,
        "g-recaptcha-response": sendCustomEmail
          ? emailData?.generatedCaptcha
          : "",
        custom_msg: emailData?.emailbody || "",
        method_to_send: emailSendingMethod,
        send_custom_email: 1,
        attached_email_files: isViewAttachment
          ? emailData?.files.map((file: IFile) => file?.file_path)
          : undefined,
        save_file_to_folder: emailData?.saveThisToFile === true ? "1" : "0",
        files: String(emailData?.files.map((file: IFile) => file?.image_id)),
      },
    });

    if (!isEmpty(emailData?.subject) && isSelectedCaptcha) {
      setSendEmailLoading(true);
      await emailApiCall?.(
        tempFormData,
        closeSendMailSidebar,
        emailData?.ccMailCopy || false
      );
      dispatch({
        type: "RESET_ALL_DATA",
      });
      setSendEmailLoading(false);
      if (emailData?.generatedCaptcha) {
        viewCapcha.current = false;
      }
      setSendEmail?.(false);
      mailFormInitialState && setEmailData?.(mailFormInitialState);
      closeDrawer();
    }
  };
  const handleSendEmail = () => {
    const isEmailAvail = selectedContact.some((ele) => ele.email);
    setConfirmMailDialogOpen(false);
    if (!isEmailAvail) {
      setIsSelectedCaptcha(false);
      setSendEmail?.(false);
      return false;
    } else {
      emailSend();
    }
  };
  const onCaptchaChange = (response: string | null) => {
    if (!isEmpty(response)) {
      setErrors((prev) => ({
        ...prev,
        selectedCaptchaError: "",
      }));
      setIsSelectedCaptcha(true);
      setEmailData?.((prev) => ({
        ...prev,
        generatedCaptcha: response,
      }));
    } else {
      setIsSelectedCaptcha(false);
    }
  };

  const dispatchSelectedContact = (
    data: TSetSelectedContactEmailActionPayload
  ) => {
    dispatch({
      type: "SET_SELECTED_CONTACT",
      payload: { contactData: data, isCustomerRemovable },
    });
  };

  const dispatchChangeFavAction = async (favData: CustomerEmail) => {
    if (!user) return;
    let res = (await changeFavAction({
      globalProject: user?.global_project,
      favData: favData,
    })) as IChangeFav;

    dispatch({
      type: "CHANGE_FAV_ACTION_FULFILLED",
      payload: {
        response: res,

        unique_id: getUniqueIdDirData(favData),
        is_favorite: String(favData.is_favorite) === "0" ? 1 : 0,
      },
    });
  };

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = event.target.value;
    setEmailData?.((prev) => ({
      ...prev,
      subject: newValue,
    }));

    if (isEmpty(newValue.trim()) || "") {
      setErrors((prev) => ({
        ...prev,
        subjectError: "This field is required.",
      }));
    } else {
      setErrors((prev) => ({
        ...prev,
        subjectError: "",
      }));
    }
  };

  // handleEmailSearch -- function to set the search data in redux email slice
  const handleEmailSearch = (searchText: string) => {
    setMainSearch(searchText);
    dispatch({
      type: "SET_SEARCH",
      payload: {
        activeField,
        searchText,
      },
    });
  };

  const setMainSearch = useCallback(
    debounce((searchText: string) => {
      dispatch({
        type: "SET_MAIN_SEARCH",
        payload: {
          activeField,
          searchText,
        },
      });
    }, 1000),
    [activeField]
  );

  const onDeleteFile = (data: {
    file_path?: string;
    image_id?: number;
    response?: any; //this type will be updated in future
  }) => {
    if (data.image_id) {
      setEmailData?.((prevFiles) => ({
        ...prevFiles,
        files: prevFiles.files.filter(
          (item) => item.image_id !== data.image_id
        ),
      }));
    } else {
      setEmailData?.((prevFiles) => ({
        ...prevFiles,
        files: prevFiles.files.filter(
          (item) => item.file_path !== data.file_path
        ),
      }));
    }
  };

  useEffect(() => {
    if (isInfiniteScrollLoading && !customer[activeField].list.length) {
      scrollRef && scrollRef.current && (scrollRef.current.scrollTop = 0);
    }
  }, [isInfiniteScrollLoading]);
  const checkForDuplicateEmails = (array: TselectedContactSendMail[]) => {
    const seenEmails = new Set();
    for (const obj of array) {
      if (seenEmails.has(obj.email) && !!obj.email) {
        return true;
      }
      seenEmails.add(obj.email);
    }
    return false;
  };
  return (
    <div className="flex lg:flex-row flex-col h-full">
      {!sendEmail ? (
        <div className="w-full xl:max-w-[630px] md:flex-[1_0_0%] border-r border-gray-300 dark:border-white/10">
          <div className="py-3 px-[15px] flex items-center justify-between whitespace-nowrap gap-3">
            <div className="w-full flex items-center whitespace-nowrap gap-3">
              <div className="w-full">
                {/* <CFIconButton
                    htmlType="button"
                    className={`w-7 !m-0 group/handle-visible-change max-w-[28px] max-h-[28px] block md:hidden ${
                      viewSearch ? "" : ""
                    }`}
                    variant="text"
                    onClick={() => {
                      setViewSearch((prev) => !prev);
                    }}
                  >
                    <FontAwesomeIcon
                      className="text-base w-[18px] h-[18px] !text-primary-gray-80 dark:!text-white/90"
                      icon={faMagnifyingGlass}
                    />
                  </CFIconButton> */}
                {/* <CFIconButton
                        htmlType="button"
                        className={`w-6 group/handle-visible-change max-w-[24px] max-h-[24px] md:hidden`}
                        variant="text"
                        onClick={() => {
                          setViewSearch((prev) => !prev);
                        }}
                      >
                        <FontAwesomeIcon
                          className="text-base w-[18px] h-[18px] text-primary-900/80 group-hover/handle-visible-change:text-primary-900 dark:!text-white/90"
                          icon={faAngleLeft}
                        />
                      </CFIconButton> */}
                <CFSearchInput
                  placeholder={_t(
                    `Search for ${ACTIVE_FIELD_NAME[activeField]}`
                  )}
                  value={customer[activeField].search}
                  onChange={(e) => {
                    handleEmailSearch(e.target.value);
                  }}
                  beforeName=""
                  className="border border-solid border-gray-200 focus:!border-primary-900 hover:!border-primary-900 focus-within:!border-primary-900 search-with-border"
                />
              </div>
              {(activeField === defaultConfig.vendor_key ||
                activeField === defaultConfig.contractor_key ||
                activeField === defaultConfig.lead_key ||
                activeField === defaultConfig.misc_contact_key) && (
                <CFCheckBox
                  className="gap-1.5 text-primary-900 checkbox-padding-remove"
                  label="Show Favorites Only"
                  name="show_only"
                  value={customer[activeField].isFavorite}
                  checked={customer[activeField].isFavorite}
                  onChange={async (e: CheckboxChangeEvent) => {
                    dispatch({
                      type: "CHANGE_IS_FAVORITE_MAIN",
                      payload: {
                        tab: activeField,
                        is_favorite: e.target.checked,
                        is_all_tab: true,
                      },
                    });
                  }}
                />
              )}
            </div>
            <CFTypography
              title="h5"
              className="!text-[15px] !mb-0 block lg:hidden"
              onClick={() => {
                setBottomSheetOpen(true);
              }}
            >
              {_t("Selected")}{" "}
              {selectedContact?.length > 0
                ? ` (${selectedContact?.length})`
                : ""}
            </CFTypography>
          </div>
          {!isInfiniteScrollLoading &&
          customer[activeField].list?.length === 0 ? (
            <div className="md:h-[calc(100dvh-120px)] h-[calc(100dvh-200px)] flex justify-center items-center">
              <NoRecords
                image={`${window.ENV.CDN_URL}assets/images/no-records-contact.svg`}
              />
            </div>
          ) : (
            <div
              ref={scrollRef}
              className={`h-screen lg:max-h-[calc(100dvh-115px)] max-h-[calc(100dvh-174px)]  flex flex-col gap-1.5 overflow-y-auto overflow-x-hidden`}
            >
              <InfiniteScroll
                loadMore={loadMore}
                hasMore={hasMore}
                isLoading={isInfiniteScrollLoading}
                hideLoadingComponent={infiniteScrollHideLoadingComponent}
                continueCall={customer[activeField].list?.length > 0}
                loadingComponent={
                  <LoadingItems
                    isAvatar={false}
                    skeleton={!customer[activeField].list.length ? 20 : 1}
                  />
                }
              >
                <GetSelectedCom
                  customer={customer}
                  activeField={activeField}
                  selectedContact={selectedContact}
                  singleSelecte={singleSelecte}
                  dispatchSelectedContact={dispatchSelectedContact}
                  dispatchChangeFavAction={dispatchChangeFavAction}
                  isCustomerRemovable={isCustomerRemovable}
                />
              </InfiniteScroll>
            </div>
          )}
          {!singleSelecte && (
            <div className="sidebar-footer flex items-center justify-center w-full p-[15px] lg:hidden">
              <CFButton
                variant="primary"
                className="w-full primary-btn justify-center"
                onClick={(e: React.MouseEvent<HTMLButtonElement>) => {
                  if (sendEmail) {
                    onSubmit(e);
                  } else {
                    setSendEmail?.(!sendEmail);
                  }
                }}
              >
                {!sendEmail ? "Next" : "Send"}
              </CFButton>
            </div>
          )}
        </div>
      ) : (
        <div className="w-full xl:max-w-[870px] md:flex-[1_0_0%] border-r border-gray-300 dark:border-white/10">
          <CFTypography
            title="h5"
            className="!text-[15px] !mb-0 block lg:hidden pt-[15px] px-[15px] text-end"
            onClick={() => {
              setBottomSheetOpen(true);
            }}
          >
            {_t("Selected")}{" "}
            {selectedContact?.length > 0 ? ` (${selectedContact?.length})` : ""}
          </CFTypography>
          <div className="p-4 flex flex-col gap-5 h-screen lg:max-h-[calc(100dvh-52px)] max-h-[calc(100dvh-151px)] overflow-y-auto">
            <div>
              <CFInput
                label={_t("From")}
                type="text"
                size="middle"
                name="from"
                defaultValue={HTMLEntities.decode(
                  sanitizeString(user?.full_name)
                )}
                disabled
              />
            </div>
            <div>
              <CFInput
                label={_t("Subject")}
                type="text"
                size="middle"
                name="subject"
                placeholder={_t("Email Subject")}
                value={emailData?.subject}
                onChange={handleChange}
                required
              />
              {errors?.subjectError && (
                <div className="text-red-400 pt-1 text-xs">
                  {errors?.subjectError}
                </div>
              )}
            </div>

            <div className="flex flex-col gap-2.5 editor-table-border">
              <CFTypography
                title="small"
                className="text-[#4B4B4B] text-sm font-semibold dark:text-white/90"
              >
                {_t("Write Your Email Body Below")}
              </CFTypography>
              <CFQuillEditor
                value={emailData?.emailbody || ""}
                quillEditorName="email-body"
                onChange={(value) => {
                  setEmailData?.((prev) => ({
                    ...prev,
                    emailbody: value,
                  }));
                }}
                isSetDate={!sendEmail}
              />
            </div>
            {isViewAttachment && (
              <div className="grid gap-2.5">
                <FieldLabel children={_t("Files")} />
                <div className="flex items-start flex-wrap gap-3.5">
                  <AttachmentCard
                    files={emailData?.files || []}
                    isReadOnly={false}
                    isShowDeleteMenu={true}
                    onDeleteFile={onDeleteFile}
                    onAddAttachment={(data) => {
                      setEmailData?.((prev) => {
                        const newFiles = [
                          ...(emailData?.files || []),
                          ...(data as IAddFileRequestBodyChild[]),
                        ];
                        return {
                          ...prev,
                          files: removeDuplicatesFile(newFiles),
                        };
                      });
                    }}
                    isAddAllow={isAddAllow}
                    editView={false}
                    validationParams={{
                      date_format,
                      file_support_module_access: checkModuleAccessByKey(
                        defaultConfig.file_support_key
                      ),
                      image_resolution,
                      module_key,
                      module_id,
                      module_access,
                    }}
                    isShowAddIcon={isShowAddIcon}
                    attachmentsFromEmailDrawer={true}
                  />
                </div>
              </div>
            )}
            <CFGoogleReCAPTCHA
              onChange={onCaptchaChange}
              view={viewCapcha.current}
              onExpired={() => {
                if (!isSelectedCaptcha) {
                  viewCapcha.current.reset();
                } else {
                  setIsSelectedCaptcha(false);
                  setErrors((prev) => ({
                    ...prev,
                    selectedCaptchaError:
                      "Please confirm that you are not a robot.",
                  }));
                }
              }}
            />
            {errors?.selectedCaptchaError && (
              <div className="text-red-400 text-xs">
                {errors?.selectedCaptchaError}
              </div>
            )}
          </div>
          {!singleSelecte && (
            <div className="sidebar-footer flex items-center justify-center w-full p-[15px] lg:hidden">
              <CFButton
                variant="primary"
                className="w-full primary-btn justify-center"
                onClick={(e: React.MouseEvent<HTMLButtonElement>) => {
                  if (sendEmail) {
                    onSubmit(e);
                  } else {
                    setSendEmail?.(!sendEmail);
                  }
                }}
              >
                {!sendEmail ? "Next" : "Send"}
              </CFButton>
            </div>
          )}
        </div>
      )}
      <div
        className={`z-20 xl:w-[340px] xl:min-w-[340px] xl:max-w-[340px] lg:w-[280px] lg:min-w-[280px] lg:max-w-[280px] w-full lg:bg-white bg-primary-gray-20/60 dark:bg-dark-800 lg:dark:bg-[#15202B] flex-[1_0_0%] max-md:overflow-hidden ${
          bottomSheetOpen ? "fixed top-0 h-full" : "lg:h-full h-0"
        }`}
      >
        {bottomSheetOpen && (
          <div
            className="lg:hidden block fixed left-0 bg-black/20 h-full w-full"
            onClick={() => {
              setBottomSheetOpen(false);
            }}
          ></div>
        )}
        <div
          className={`transition-all ease-in-out duration-300 bg-white lg:rounded-none rounded-t-lg max-lg:fixed w-full z-10 ${
            bottomSheetOpen
              ? "h-[calc(100vh-150px)] bottom-0 left-0"
              : "lg:h-full h-0 max-lg:-bottom-[100%]"
          }`}
        >
          <div className="py-[13px] px-[15px] flex items-center relative before:absolute before:left-0 before:bottom-0 before:w-full before:h-px before:bg-[linear-gradient(90deg,#3f4c653d_24%,#3f4c6500_100%)] dark:before:bg-[linear-gradient(90deg,#343f54_24%,#1e2732_100%)]">
            <div className="flex items-center justify-center rounded-full mr-[7px] w-9 h-9 bg-gray-200/50 dark:bg-dark-500">
              <FontAwesomeIcon
                className="w-[18px] h-[18px] text-primary-900 dark:text-white/90"
                icon="fa-regular fa-box-check"
              />
            </div>
            <div>
              <CFTypography
                title="h5"
                className="!text-[15px] !mb-0 !text-primary-900 dark:!text-white/90"
              >
                {_t("Currently Selected")}
                {selectedContact?.length > 0
                  ? ` (${selectedContact?.length})`
                  : ""}
              </CFTypography>
              <CFTypography className="text-[11px] opacity-60 text-primary-900 dark:text-white/90 !mb-0 font-normal">
                {_t("Selected contacts will appear here!")}
              </CFTypography>
            </div>
          </div>
          <div className="lg:h-[calc(100vh-179px)] max-md:h-[calc(100vh-216px)] overflow-hidden select-list-block">
            <ListOfSeletedCustomers
              rightScrollRef={rightScrollRef}
              groupCheckBox={groupCheckBox}
              isInput={true}
              sendEmail={sendEmail}
              dispatchSelectedContact={dispatchSelectedContact}
              selectedContact={selectedContact}
              setEmailInput={setEmailInput}
              emailInput={emailInput}
              mailCheckBoxs={
                <div className="flex flex-col gap-2 mx-[15px] py-3.5 border-b border-dashed border-[#ddd]">
                  {isViewSaveThisToFileCheckbox && (
                    <CFCheckBox
                      className="gap-1.5 text-13 text-primary-900 checkbox-padding-remove"
                      label={_t(
                        `Save a copy of the PDF to ${saveFileModuleName} folder`
                      )}
                      onChange={(e: CheckboxChangeEvent) => {
                        setEmailData?.((prev) => ({
                          ...prev,
                          saveThisToFile: e.target.checked,
                        }));
                      }}
                      name="cc_mail_copy"
                      checked={emailData?.saveThisToFile}
                    />
                  )}
                  <CFCheckBox
                    className="gap-1.5 text-13 text-primary-900 checkbox-padding-remove"
                    label={_t("CC a Copy to My Email")}
                    onChange={(e: CheckboxChangeEvent) => {
                      setEmailData?.((prev) => ({
                        ...prev,
                        ccMailCopy: e.target.checked,
                      }));
                    }}
                    name="cc_mail_copy"
                    checked={emailData?.ccMailCopy}
                  />
                </div>
              }
              setEmailSendingMethod={setEmailSendingMethod}
              emailSendingMethod={emailSendingMethod}
              error={errors}
              setErrors={setErrors}
              isCustomerRemovable={isCustomerRemovable}
            />
          </div>
          {!singleSelecte && (
            <div className="sidebar-footer flex items-center justify-center w-full p-[15px] max-lg:hidden">
              <PrimaryButton
                className="w-full"
                onClick={(e: React.MouseEvent<HTMLButtonElement>) => {
                  const hasDuplicatesEmailAvailable =
                    checkForDuplicateEmails(selectedContact);
                  const emailError =
                    emailInput &&
                    errors.externalEmailError === "Email already exists";
                  if (
                    errors.externalEmailError === "Email already exists" &&
                    emailInput
                  ) {
                    return;
                  } else if (
                    errors &&
                    errors.externalEmailError !== "" &&
                    errors.externalEmailError !== undefined &&
                    emailInput
                  ) {
                    return notification.error({
                      description: "You've entered an Invalid Email.",
                    });
                  }
                  if (
                    (hasDuplicatesEmailAvailable && emailInput) ||
                    emailError
                  ) {
                    setErrors({
                      externalEmailError: "Email already exists",
                    });
                    return;
                  } else {
                    setErrors({
                      externalEmailError: "",
                    });
                  }
                  if (sendEmail) {
                    onSubmit(e);
                  } else {
                    setSendEmail?.(!sendEmail);
                  }
                }}
                buttonText={!sendEmail ? "Next" : "Send"}
                disabled={sendEmailLoading}
                isLoading={sendEmailLoading}
              />
            </div>
          )}
        </div>
      </div>
      {confirmMailDialogOpen && (
        <ConfirmModal
          isOpen={confirmMailDialogOpen}
          modaltitle={_t("Send Email")}
          modalIcon="fa-regular fa-envelope"
          description={
            isEmailAvailable
              ? _t(
                  `${
                    isContactWithoutEmail?.length > 1
                      ? "Recipient(s)"
                      : " Recipient"
                  } ${isContactWithoutEmail.join(
                    ", "
                  )} do not have an email associated with their account.  Go back to their Directory listing to add it or click OK to send the message anyway to the remaining recipients.`
                )
              : _t(
                  `${
                    isContactWithoutEmail?.length > 1
                      ? "Recipient(s)"
                      : " Recipient"
                  } ${isContactWithoutEmail.join(
                    ", "
                  )} do not have an email associated with their account.   Go back to their Directory listing to add it.`
                )
          }
          yesButtonLabel={"OK"}
          noButtonLabel={isEmailAvailable ? "Cancel" : ""}
          onAccept={() => {
            handleSendEmail();
          }}
          onDecline={() => setConfirmMailDialogOpen(false)}
          onCloseModal={() => setConfirmMailDialogOpen(false)}
        />
      )}
    </div>
  );
};

export default CustomerSelectBody;
